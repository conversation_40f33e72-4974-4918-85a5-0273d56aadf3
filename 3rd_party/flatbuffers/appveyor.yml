branches:
  only:
    - master

os: Visual Studio 2015

environment:

  global:
    # Workaround for https://github.com/conda/conda-build/issues/636
    PYTHONIOENCODING: UTF-8
    CONDA_INSTALL_LOCN: "C:\\Miniconda35-x64"

  matrix:
    - CMAKE_VS_VERSION: "10 2010"
      MONSTER_EXTRA: "skip"

    - CMAKE_VS_VERSION: "12 2013"
      MONSTER_EXTRA: "skip"

    - CMAKE_VS_VERSION: "14 2015"
      MONSTER_EXTRA: ""

platform:
  - x86
  - x64

configuration:
  - Debug
  - Release

before_build:
  - set MONSTER_EXTRA=%MONSTER_EXTRA%
  - cmake -G"Visual Studio %CMAKE_VS_VERSION%"
  # This cuts down on a lot of noise generated by xamarin warnings.
  - del "C:\Program Files (x86)\MSBuild\14.0\Microsoft.Common.targets\ImportAfter\Xamarin.Common.targets"

build:
  project: ALL_BUILD.vcxproj
  verbosity: minimal

after_build:
  - python conan/appveyor/install.py
  - python conan/appveyor/build.py

install:
  - set PATH=%CONDA_INSTALL_LOCN%;%CONDA_INSTALL_LOCN%\scripts;%PATH%;
  - curl -sSf -o rustup-init.exe https://win.rustup.rs/
  - rustup-init.exe -y
  - set PATH=%PATH%;C:\Users\<USER>\.cargo\bin
  - rustc -V
  - cargo -V

test_script:
  - call .appveyor\check-generate-code.bat -b %CONFIGURATION%
  - "cd tests"
  - rem "Building all code"
  - generate_code.bat -b %CONFIGURATION%
  - 7z a GeneratedMyGameCode.zip MyGame\
  - rem "---------------- C++ -----------------"
  - "cd .."
  - "%CONFIGURATION%\\flattests.exe"
  - "cd tests"
  - rem "---------------- Java -----------------"
  - "java -version"
  - "JavaTest.bat"
  - rem "---------------- Rust ----------------"
  - "RustTest.bat"
  - rem "---------------- JS -----------------"
  - "node --version"
  - "..\\%CONFIGURATION%\\flatc -b -I include_test monster_test.fbs unicode_test.json"
  - "node JavaScriptTest ./monster_test_generated"
  - rem "-------------- Python ---------------"
  - where python
  - python --version
  - where pip
  - pip --version
  - where conda
  - conda --version
  - rem "installing flatbuffers python library"
  - pip install ../python
  - rem "testing without installing Numpy"
  - python py_test.py 0 0 0
  - rem "testing after installing Numpy - disabled"
  # FIXME: This has a LOT of unnecessary dependencies and makes the tests fail
  # with timeouts.
  # - conda install --yes numpy
  # - python py_test.py 0 0 0
  - rem "---------------- C# -----------------"
  # Have to compile this here rather than in "build" above because AppVeyor only
  # supports building one project??
  - "cd FlatBuffers.Test"
  - "msbuild.exe /property:Configuration=Release;OutputPath=tempcs /verbosity:minimal FlatBuffers.Test.csproj"
  - "tempcs\\FlatBuffers.Test.exe"
  # TODO: add more languages.
  - "cd ..\\.."

artifacts:
  - path: $(CONFIGURATION)\flatc.exe
    name: flatc.exe
  - path: tests\GeneratedMyGameCode.zip
    name: GeneratedMyGameCode.zip
