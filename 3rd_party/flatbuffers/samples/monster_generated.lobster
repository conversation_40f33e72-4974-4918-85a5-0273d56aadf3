// automatically generated by the FlatBuffers compiler, do not modify

include "flatbuffers.lobster"

namespace MyGame_Sample

enum + 
    Color_Red = 0,
    Color_Green = 1,
    Color_Blue = 2

enum + 
    Equipment_NONE = 0,
    Equipment_Weapon = 1

struct Vec3

struct Monster

struct Weapon

struct Vec3 : flatbuffers_handle
    def x():
        buf_.read_float32_le(pos_ + 0)
    def y():
        buf_.read_float32_le(pos_ + 4)
    def z():
        buf_.read_float32_le(pos_ + 8)

def CreateVec3(b_:flatbuffers_builder, x:float, y:float, z:float):
    b_.Prep(4, 12)
    b_.PrependFloat32(z)
    b_.PrependFloat32(y)
    b_.PrependFloat32(x)
    return b_.Offset()

struct Monster : flatbuffers_handle
    def pos():
        o := buf_.flatbuffers_field_struct(pos_, 4)
        if o: MyGame_Sample_Vec3 { buf_, o } else: nil
    def mana():
        buf_.flatbuffers_field_int16(pos_, 6, 150)
    def hp():
        buf_.flatbuffers_field_int16(pos_, 8, 100)
    def name():
        buf_.flatbuffers_field_string(pos_, 10)
    def inventory(i:int):
        buf_.read_int8_le(buf_.flatbuffers_field_vector(pos_, 14) + i * 1)
    def inventory_length():
        buf_.flatbuffers_field_vector_len(pos_, 14)
    def color():
        buf_.flatbuffers_field_int8(pos_, 16, 2)
    def weapons(i:int):
        MyGame_Sample_Weapon { buf_, buf_.flatbuffers_indirect(buf_.flatbuffers_field_vector(pos_, 18) + i * 4) }
    def weapons_length():
        buf_.flatbuffers_field_vector_len(pos_, 18)
    def equipped_type():
        buf_.flatbuffers_field_int8(pos_, 20, 0)
    def equipped_as_Weapon():
        MyGame_Sample_Weapon { buf_, buf_.flatbuffers_field_table(pos_, 22) }

def GetRootAsMonster(buf:string): Monster { buf, buf.flatbuffers_indirect(0) }

def MonsterStart(b_:flatbuffers_builder):
    b_.StartObject(10)
def MonsterAddPos(b_:flatbuffers_builder, pos:int):
    b_.PrependStructSlot(0, pos, 0)
def MonsterAddMana(b_:flatbuffers_builder, mana:int):
    b_.PrependInt16Slot(1, mana, 150)
def MonsterAddHp(b_:flatbuffers_builder, hp:int):
    b_.PrependInt16Slot(2, hp, 100)
def MonsterAddName(b_:flatbuffers_builder, name:int):
    b_.PrependUOffsetTRelativeSlot(3, name, 0)
def MonsterAddInventory(b_:flatbuffers_builder, inventory:int):
    b_.PrependUOffsetTRelativeSlot(5, inventory, 0)
def MonsterStartInventoryVector(b_:flatbuffers_builder, n_:int):
    b_.StartVector(1, n_, 1)
def MonsterCreateInventoryVector(b_:flatbuffers_builder, v_:[int]):
    b_.StartVector(1, v_.length, 1)
    reverse(v_) e_: b_.PrependUint8(e_)
    b_.EndVector(v_.length)
def MonsterAddColor(b_:flatbuffers_builder, color:int):
    b_.PrependInt8Slot(6, color, 2)
def MonsterAddWeapons(b_:flatbuffers_builder, weapons:int):
    b_.PrependUOffsetTRelativeSlot(7, weapons, 0)
def MonsterStartWeaponsVector(b_:flatbuffers_builder, n_:int):
    b_.StartVector(4, n_, 4)
def MonsterCreateWeaponsVector(b_:flatbuffers_builder, v_:[int]):
    b_.StartVector(4, v_.length, 4)
    reverse(v_) e_: b_.PrependUOffsetTRelative(e_)
    b_.EndVector(v_.length)
def MonsterAddEquippedType(b_:flatbuffers_builder, equipped_type:int):
    b_.PrependUint8Slot(8, equipped_type, 0)
def MonsterAddEquipped(b_:flatbuffers_builder, equipped:int):
    b_.PrependUOffsetTRelativeSlot(9, equipped, 0)
def MonsterEnd(b_:flatbuffers_builder):
    b_.EndObject()

struct Weapon : flatbuffers_handle
    def name():
        buf_.flatbuffers_field_string(pos_, 4)
    def damage():
        buf_.flatbuffers_field_int16(pos_, 6, 0)

def GetRootAsWeapon(buf:string): Weapon { buf, buf.flatbuffers_indirect(0) }

def WeaponStart(b_:flatbuffers_builder):
    b_.StartObject(2)
def WeaponAddName(b_:flatbuffers_builder, name:int):
    b_.PrependUOffsetTRelativeSlot(0, name, 0)
def WeaponAddDamage(b_:flatbuffers_builder, damage:int):
    b_.PrependInt16Slot(1, damage, 0)
def WeaponEnd(b_:flatbuffers_builder):
    b_.EndObject()

