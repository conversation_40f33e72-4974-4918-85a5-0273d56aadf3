Thank you for submitting a PR!

Please make sure you include the names of the affected language(s) in your PR title.
This helps us get the correct maintainers to look at your issue.

If you make changes to any of the code generators, be sure to run
`cd tests && sh generate_code.sh` (or equivalent .bat) and include the generated
code changes in the PR. This allows us to better see the effect of the PR.

If your PR includes C++ code, please adhere to the Google C++ Style Guide,
and don't forget we try to support older compilers (e.g. VS2010, GCC 4.6.3),
so only some C++11 support is available.

Include other details as appropriate.

Thanks!
