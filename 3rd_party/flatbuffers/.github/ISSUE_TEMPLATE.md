Thank you for submitting an issue!

Please make sure you include the names of the affected language(s), compiler version(s), operating system version(s), and FlatBuffers version(s) in your issue title.

This helps us get the correct maintainers to look at your issue. Here are examples of good titles:

- Crash when accessing FlatBuffer [C++, gcc 4.8, OS X, master]
- Flatc converts a protobuf 'bytes' field to 'string' in fbs schema file [all languages, FlatBuffers 1.4]

Include other details as appropriate.

Thanks!
