Go API
======

\addtogroup flatbuffers_go_api

<!-- Note: The `GoApi_generate.txt` code snippet was generated using `godoc` and
     customized for use with this markdown file. To regenerate the file, use the
     `godoc` tool (http://godoc.org) with the files in the `flatbuffers/go`
     folder.

     You may need to ensure that copies of the files exist in the `src/`
     subfolder at the path set by the `$GOROOT` environment variable. You can
     either move the files to `$GOROOT/src/flatbuffers` manually, if `$GOROOT`
     is already set, otherwise you will need to manually set the `$GOROOT`
     variable to a path and create `src/flatbuffers` subfolders at that path.
     Then copy the flatbuffers files into `$GOROOT/src/flatbuffers`. (Some
     versions of `godoc` include a `-path` flag. This could be used instead, if
     available).

     Once the files exist at the `$GOROOT/src/flatbuffers` location, you can
     regenerate this doc using the following command:
     `godoc flatbuffers > GoApi_generated.txt`.

     After the documentation is generated, you will have to manually remove any
     non-user facing documentation from this file. -->
\snippet GoApi_generated.txt Go API
