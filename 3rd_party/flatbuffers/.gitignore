*_wire.txt
*_wire.bin
.DS_Store
*.o
*.o.d
*.class
*.a
*.swp
*~
*.vcxproj
*.vcxproj.filters
*.vcxproj.user
*.sln
*.suo
*.opendb
*.keystore
**/.vs/**
**/bin/**
!tests/rust_usage_test/bin/**
**/gen/**
**/libs/**
**/obj/**
**/*.dir/**
**/CMakeFiles/**
**/cmake_install.cmake
**/install_manifest.txt
**/CMakeCache.txt
**/CMakeTestfile.cmake
**/Debug/**
**/Release/**
**/RelWithDebInfo/**
**/x64/ #build artifacts from VS
build.xml
local.properties
project.properties
proguard-project.txt
linklint_results
Makefile
flatc
flatc.exe
flathash
flathash.exe
flattests
flattests.exe
flatsamplebinary
flatsamplebinary.exe
flatsampletext
flatsampletext.exe
flatsamplebfbs
flatsamplebfbs.exe
grpctest
grpctest.exe
snapshot.sh
tags
tests/dart_gen
tests/go_gen
tests/monsterdata_java_wire.mon
tests/monsterdata_java_wire_sp.mon
tests/monsterdata_go_wire.mon
tests/monsterdata_javascript_wire.mon
tests/monsterdata_lobster_wire.mon
tests/monsterdata_rust_wire.mon
tests/unicode_test.mon
tests/ts/
tests/php/
CMakeLists.txt.user
CMakeScripts/**
CTestTestfile.cmake
FlatbuffersConfigVersion.cmake
FlatBuffers.cbp
build/Xcode/FlatBuffers.xcodeproj/project.xcworkspace/**
build/Xcode/FlatBuffers.xcodeproj/xcuserdata/**
FlatBuffers.xcodeproj/
java/.idea
java/*.iml
.idea
*.iml
target
**/*.pyc
build/VS2010/FlatBuffers.sdf
build/VS2010/FlatBuffers.opensdf
build/VS2010/ipch/**/*.ipch
*.so
Testing/Temporary
.cproject
.settings/
.project
net/**/obj
node_modules/
android/.externalNativeBuild/
android/.gradle/
android/build/
samples/android/.externalNativeBuild/
samples/android/.gradle/
samples/android/build/
js/flatbuffers.mjs
/bazel-bin
/bazel-flatbuffers
/bazel-genfiles
/bazel-out
/bazel-testlogs
.ninja_deps
.ninja_log
build.ninja
rules.ninja
.vscode
dart/.pub/
dart/.packages
dart/pubspec.lock
dart/.dart_tool/
dart/build/
dart/doc/api/
Cargo.lock
.corpus**
.seed**
