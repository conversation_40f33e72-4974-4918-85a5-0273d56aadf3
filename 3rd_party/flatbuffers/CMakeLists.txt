cmake_minimum_required(VERSION 3.6)
# generate compile_commands.json
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)
include(CheckCXXSymbolExists)

project(FlatBuffers)

# NOTE: Code coverage only works on Linux & OSX.
option(FLATBUFFERS_CODE_COVERAGE "Enable the code coverage build option." OFF)
option(FLATBUFFERS_BUILD_TESTS "Enable the build of tests and samples." ON)
option(FLATBUFFERS_INSTALL "Enable the installation of targets." ON)
option(FLATBUFFERS_BUILD_FLATLIB "Enable the build of the flatbuffers library"
       ON)
option(FLATBUFFERS_BUILD_FLATC "Enable the build of the flatbuffers compiler"
       ON)
option(FLATBUFFERS_BUILD_FLATHASH "Enable the build of flathash" ON)
option(FLATBUFFERS_BUILD_GRPCTEST "Enable the build of grpctest" OFF)
option(FLATBUFFERS_BUILD_SHAREDLIB
       "Enable the build of the flatbuffers shared library"
       OFF)
option(FLATBUFFERS_LIBCXX_WITH_CLANG "Force libc++ when using Clang" ON)
# NOTE: Sanitizer check only works on Linux & OSX (gcc & llvm).
option(FLATBUFFERS_CODE_SANITIZE
      "Add '-fsanitize' flags to 'flattests' and 'flatc' targets."
      OFF)

if(NOT FLATBUFFERS_BUILD_FLATC AND FLATBUFFERS_BUILD_TESTS)
    message(WARNING
    "Cannot build tests without building the compiler. Tests will be disabled.")
    set(FLATBUFFERS_BUILD_TESTS OFF)
endif()

if(DEFINED FLATBUFFERS_MAX_PARSING_DEPTH)
  # Override the default recursion depth limit.
  add_definitions(-DFLATBUFFERS_MAX_PARSING_DEPTH=${FLATBUFFERS_MAX_PARSING_DEPTH})
  message(STATUS "FLATBUFFERS_MAX_PARSING_DEPTH: ${FLATBUFFERS_MAX_PARSING_DEPTH}")
endif()

# Auto-detect locale-narrow 'strtod_l' function.
if(NOT DEFINED FLATBUFFERS_LOCALE_INDEPENDENT)
  if(MSVC)
    check_cxx_symbol_exists(_strtof_l stdlib.h FLATBUFFERS_LOCALE_INDEPENDENT)
  else()
    check_cxx_symbol_exists(strtof_l stdlib.h FLATBUFFERS_LOCALE_INDEPENDENT)
  endif()
endif()
add_definitions(-DFLATBUFFERS_LOCALE_INDEPENDENT=$<BOOL:${FLATBUFFERS_LOCALE_INDEPENDENT}>)

set(FlatBuffers_Library_SRCS
  include/flatbuffers/code_generators.h
  include/flatbuffers/base.h
  include/flatbuffers/flatbuffers.h
  include/flatbuffers/hash.h
  include/flatbuffers/idl.h
  include/flatbuffers/util.h
  include/flatbuffers/reflection.h
  include/flatbuffers/reflection_generated.h
  include/flatbuffers/stl_emulation.h
  include/flatbuffers/flexbuffers.h
  include/flatbuffers/registry.h
  include/flatbuffers/minireflect.h
  src/code_generators.cpp
  src/idl_parser.cpp
  src/idl_gen_text.cpp
  src/reflection.cpp
  src/util.cpp
)

set(FlatBuffers_Compiler_SRCS
  ${FlatBuffers_Library_SRCS}
  src/idl_gen_cpp.cpp
  src/idl_gen_dart.cpp
  src/idl_gen_general.cpp
  src/idl_gen_go.cpp
  src/idl_gen_js_ts.cpp
  src/idl_gen_php.cpp
  src/idl_gen_python.cpp
  src/idl_gen_lobster.cpp
  src/idl_gen_lua.cpp
  src/idl_gen_rust.cpp
  src/idl_gen_fbs.cpp
  src/idl_gen_grpc.cpp
  src/idl_gen_json_schema.cpp
  src/flatc.cpp
  src/flatc_main.cpp
  grpc/src/compiler/schema_interface.h
  grpc/src/compiler/cpp_generator.h
  grpc/src/compiler/cpp_generator.cc
  grpc/src/compiler/go_generator.h
  grpc/src/compiler/go_generator.cc
  grpc/src/compiler/java_generator.h
  grpc/src/compiler/java_generator.cc
)

set(FlatHash_SRCS
  include/flatbuffers/hash.h
  src/flathash.cpp
)

set(FlatBuffers_Tests_SRCS
  ${FlatBuffers_Library_SRCS}
  src/idl_gen_fbs.cpp
  tests/test.cpp
  tests/test_assert.h
  tests/test_assert.cpp
  tests/test_builder.h
  tests/test_builder.cpp
  # file generate by running compiler on tests/monster_test.fbs
  ${CMAKE_CURRENT_BINARY_DIR}/tests/monster_test_generated.h
)

set(FlatBuffers_Sample_Binary_SRCS
  include/flatbuffers/flatbuffers.h
  samples/sample_binary.cpp
  # file generated by running compiler on samples/monster.fbs
  ${CMAKE_CURRENT_BINARY_DIR}/samples/monster_generated.h
)

set(FlatBuffers_Sample_Text_SRCS
  ${FlatBuffers_Library_SRCS}
  samples/sample_text.cpp
  # file generated by running compiler on samples/monster.fbs
  ${CMAKE_CURRENT_BINARY_DIR}/samples/monster_generated.h
)

set(FlatBuffers_Sample_BFBS_SRCS
  ${FlatBuffers_Library_SRCS}
  src/idl_gen_general.cpp
  samples/sample_bfbs.cpp
  # file generated by running compiler on samples/monster.fbs
  ${CMAKE_CURRENT_BINARY_DIR}/samples/monster_generated.h
)

set(FlatBuffers_GRPCTest_SRCS
  include/flatbuffers/flatbuffers.h
  include/flatbuffers/grpc.h
  tests/monster_test.grpc.fb.h
  tests/test_assert.h
  tests/test_builder.h
  tests/monster_test.grpc.fb.cc
  tests/test_assert.cpp
  tests/test_builder.cpp
  grpc/tests/grpctest.cpp
  grpc/tests/message_builder_test.cpp
  # file generated by running compiler on samples/monster.fbs
  ${CMAKE_CURRENT_BINARY_DIR}/samples/monster_generated.h
)

# source_group(Compiler FILES ${FlatBuffers_Compiler_SRCS})
# source_group(Tests FILES ${FlatBuffers_Tests_SRCS})

if(EXISTS "${CMAKE_TOOLCHAIN_FILE}")
  # do not apply any global settings if the toolchain
  # is being configured externally
elseif(APPLE)
  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11 -stdlib=libc++")
  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -pedantic -Wextra -Wno-unused-parameter")
  set(FLATBUFFERS_PRIVATE_CXX_FLAGS "-Wold-style-cast")
elseif(CMAKE_COMPILER_IS_GNUCXX)
  if(CYGWIN)
    set(CMAKE_CXX_FLAGS
      "${CMAKE_CXX_FLAGS} -std=gnu++11")
  else(CYGWIN)
    set(CMAKE_CXX_FLAGS
      "${CMAKE_CXX_FLAGS} -std=c++0x")
  endif(CYGWIN)
  set(CMAKE_CXX_FLAGS
    "${CMAKE_CXX_FLAGS} -Wall -pedantic -Wextra")
  set(FLATBUFFERS_PRIVATE_CXX_FLAGS "-Wold-style-cast")
  if (CMAKE_CXX_COMPILER_VERSION VERSION_GREATER 4.4)
    if (CMAKE_CXX_COMPILER_VERSION VERSION_GREATER 7.0)
      set(CMAKE_CXX_FLAGS
        "${CMAKE_CXX_FLAGS} -faligned-new")
    endif()
    set(CMAKE_CXX_FLAGS
      "${CMAKE_CXX_FLAGS} -Wunused-result -Wunused-parameter")
  endif()

  # Certain platforms such as ARM do not use signed chars by default
  # which causes issues with certain bounds checks.
  set(CMAKE_CXX_FLAGS
    "${CMAKE_CXX_FLAGS} -fsigned-char")

elseif(${CMAKE_CXX_COMPILER_ID} MATCHES "Clang")
  set(CMAKE_CXX_FLAGS
      "${CMAKE_CXX_FLAGS} -std=c++0x -Wall -pedantic -Werror -Wextra -Wno-unused-parameter")
  set(FLATBUFFERS_PRIVATE_CXX_FLAGS "-Wold-style-cast")
  if(FLATBUFFERS_LIBCXX_WITH_CLANG)
    if(NOT "${CMAKE_SYSTEM_NAME}" MATCHES "Linux")
      set(CMAKE_CXX_FLAGS
          "${CMAKE_CXX_FLAGS} -stdlib=libc++")
    endif()
    if(NOT ("${CMAKE_SYSTEM_NAME}" MATCHES "FreeBSD" OR
            "${CMAKE_SYSTEM_NAME}" MATCHES "Linux"))
      set(CMAKE_EXE_LINKER_FLAGS
          "${CMAKE_EXE_LINKER_FLAGS} -lc++abi")
    endif()
  endif()

  # Certain platforms such as ARM do not use signed chars by default
  # which causes issues with certain bounds checks.
  set(CMAKE_CXX_FLAGS
    "${CMAKE_CXX_FLAGS} -fsigned-char")

elseif(MSVC)
  # Visual Studio pedantic build settings
  # warning C4512: assignment operator could not be generated
  # warning C4316: object allocated on the heap may not be aligned
  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /wd4512 /wd4316")
endif()

if(FLATBUFFERS_CODE_COVERAGE)
  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -fprofile-arcs -ftest-coverage")
  set(CMAKE_EXE_LINKER_FLAGS
      "${CMAKE_EXE_LINKER_FLAGS} -fprofile-arcs -ftest-coverage")
endif()

function(add_fsanitize_to_target _target _sanitizer)
  # FLATBUFFERS_CODE_SANITIZE: boolean {ON,OFF,YES,NO} or string with list of sanitizer.
  # List of sanitizer is string starts with '=': "=address,undefined,thread,memory".
  if((${CMAKE_CXX_COMPILER_ID} MATCHES "Clang") OR
    ((${CMAKE_CXX_COMPILER_ID} MATCHES "GNU") AND NOT (CMAKE_CXX_COMPILER_VERSION VERSION_LESS "4.9"))
  )
    set(_sanitizer_flags "=address,undefined")
    if(_sanitizer MATCHES "=.*")
      # override default by user-defined sanitizer list
      set(_sanitizer_flags ${_sanitizer})
    endif()
    target_compile_options(${_target} PRIVATE
      -g -fsigned-char -fno-omit-frame-pointer
      "-fsanitize${_sanitizer_flags}")
    target_link_libraries(${_target} PRIVATE
      "-fsanitize${_sanitizer_flags}")
    set_property(TARGET ${_target} PROPERTY POSITION_INDEPENDENT_CODE ON)
    message(STATUS "Sanitizer ${_sanitizer_flags} added to ${_target}")
  endif()
endfunction()

if(BIICODE)
  include(biicode/cmake/biicode.cmake)
  return()
endif()

include_directories(include)
include_directories(grpc)

if(FLATBUFFERS_BUILD_FLATLIB)
  add_library(flatbuffers STATIC ${FlatBuffers_Library_SRCS})
  # CMake > 2.8.11: Attach header directory for when build via add_subdirectory().
  target_include_directories(flatbuffers INTERFACE
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>)
  target_compile_options(flatbuffers PRIVATE "${FLATBUFFERS_PRIVATE_CXX_FLAGS}")
endif()

if(FLATBUFFERS_BUILD_FLATC)
  add_executable(flatc ${FlatBuffers_Compiler_SRCS})
  target_compile_options(flatc PRIVATE "${FLATBUFFERS_PRIVATE_CXX_FLAGS}")
  if(FLATBUFFERS_CODE_SANITIZE)
    add_fsanitize_to_target(flatc ${FLATBUFFERS_CODE_SANITIZE})
  endif()
  if(NOT FLATBUFFERS_FLATC_EXECUTABLE)
    set(FLATBUFFERS_FLATC_EXECUTABLE $<TARGET_FILE:flatc>)
  endif()
  if(MSVC)
    # Make flatc.exe not depend on runtime dlls for easy distribution.
    target_compile_options(flatc PUBLIC $<$<CONFIG:Release>:/MT>)
  endif()
endif()

if(FLATBUFFERS_BUILD_FLATHASH)
  add_executable(flathash ${FlatHash_SRCS})
endif()

if(FLATBUFFERS_BUILD_SHAREDLIB)
  add_library(flatbuffers_shared SHARED ${FlatBuffers_Library_SRCS})

  # Shared object version: "major.minor.micro"
  # - micro updated every release when there is no API/ABI changes
  # - minor updated when there are additions in API/ABI
  # - major (ABI number) updated when there are changes in ABI (or removals)
  set(FlatBuffers_Library_SONAME_MAJOR "1")
  set(FlatBuffers_Library_SONAME_FULL "${FlatBuffers_Library_SONAME_MAJOR}.10.0")
  set_target_properties(flatbuffers_shared PROPERTIES OUTPUT_NAME flatbuffers
                        SOVERSION "${FlatBuffers_Library_SONAME_MAJOR}"
                        VERSION "${FlatBuffers_Library_SONAME_FULL}")
endif()

function(compile_flatbuffers_schema_to_cpp SRC_FBS)
  get_filename_component(SRC_FBS_DIR ${SRC_FBS} PATH)
  string(REGEX REPLACE "\\.fbs$" "_generated.h" GEN_HEADER ${SRC_FBS})
  add_custom_command(
    OUTPUT ${GEN_HEADER}
    COMMAND "${FLATBUFFERS_FLATC_EXECUTABLE}" -c --no-includes --gen-mutable
            --gen-object-api --gen-compare -o "${SRC_FBS_DIR}"
            --cpp-ptr-type flatbuffers::unique_ptr # Used to test with C++98 STLs
            --reflect-names
            -I "${CMAKE_CURRENT_SOURCE_DIR}/tests/include_test"
            "${CMAKE_CURRENT_SOURCE_DIR}/${SRC_FBS}"
    DEPENDS flatc)
endfunction()

function(compile_flatbuffers_schema_to_binary SRC_FBS)
  get_filename_component(SRC_FBS_DIR ${SRC_FBS} PATH)
  string(REGEX REPLACE "\\.fbs$" ".bfbs" GEN_BINARY_SCHEMA ${SRC_FBS})
  add_custom_command(
    OUTPUT ${GEN_BINARY_SCHEMA}
    COMMAND "${FLATBUFFERS_FLATC_EXECUTABLE}" -b --schema -o "${SRC_FBS_DIR}"
            "${CMAKE_CURRENT_SOURCE_DIR}/${SRC_FBS}"
    DEPENDS flatc)
endfunction()

if(FLATBUFFERS_BUILD_TESTS)
  compile_flatbuffers_schema_to_cpp(tests/monster_test.fbs)
  include_directories(${CMAKE_CURRENT_BINARY_DIR}/tests)
  add_executable(flattests ${FlatBuffers_Tests_SRCS})
  if(FLATBUFFERS_CODE_SANITIZE)
    add_fsanitize_to_target(flattests ${FLATBUFFERS_CODE_SANITIZE})
  endif()
  set_property(TARGET flattests
    PROPERTY COMPILE_DEFINITIONS FLATBUFFERS_TRACK_VERIFIER_BUFFER_SIZE
    FLATBUFFERS_DEBUG_VERIFICATION_FAILURE=1)

  compile_flatbuffers_schema_to_cpp(samples/monster.fbs)
  include_directories(${CMAKE_CURRENT_BINARY_DIR}/samples)
  add_executable(flatsamplebinary ${FlatBuffers_Sample_Binary_SRCS})
  add_executable(flatsampletext ${FlatBuffers_Sample_Text_SRCS})
  add_executable(flatsamplebfbs ${FlatBuffers_Sample_BFBS_SRCS})
endif()

if(FLATBUFFERS_BUILD_GRPCTEST)
  if(CMAKE_COMPILER_IS_GNUCXX)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wno-unused-parameter -Wno-shadow")
  endif()
  if(NOT GRPC_INSTALL_PATH)
    message(SEND_ERROR "GRPC_INSTALL_PATH variable is not defined. See grpc/README.md")
  endif()
  if(NOT PROTOBUF_DOWNLOAD_PATH)
    message(SEND_ERROR "PROTOBUF_DOWNLOAD_PATH variable is not defined. See grpc/README.md")
  endif()
  INCLUDE_DIRECTORIES(${GRPC_INSTALL_PATH}/include)
  INCLUDE_DIRECTORIES(${PROTOBUF_DOWNLOAD_PATH}/src)
  LINK_DIRECTORIES(${GRPC_INSTALL_PATH}/lib)
  add_executable(grpctest ${FlatBuffers_GRPCTest_SRCS})
  target_link_libraries(grpctest grpc++_unsecure grpc_unsecure gpr pthread dl)
endif()

include(CMake/Version.cmake)

if(FLATBUFFERS_INSTALL)
  include(GNUInstallDirs)

  install(DIRECTORY include/flatbuffers DESTINATION ${CMAKE_INSTALL_INCLUDEDIR})

  set(FB_CMAKE_DIR "${CMAKE_INSTALL_LIBDIR}/cmake/flatbuffers")

  configure_file(CMake/FlatbuffersConfigVersion.cmake.in FlatbuffersConfigVersion.cmake @ONLY)
  install(
      FILES "CMake/FlatbuffersConfig.cmake" "${CMAKE_CURRENT_BINARY_DIR}/FlatbuffersConfigVersion.cmake"
      DESTINATION ${FB_CMAKE_DIR}
  )

  if(FLATBUFFERS_BUILD_FLATLIB)
    if(CMAKE_VERSION VERSION_LESS 3.0)
      install(
        TARGETS flatbuffers EXPORT FlatbuffersTargets
        ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
      )
    else()
      install(
        TARGETS flatbuffers EXPORT FlatbuffersTargets
        ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
        INCLUDES DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
      )
    endif()

    install(EXPORT FlatbuffersTargets
      FILE FlatbuffersTargets.cmake
      NAMESPACE flatbuffers::
      DESTINATION ${FB_CMAKE_DIR}
    )
  endif()

  if(FLATBUFFERS_BUILD_FLATC)
    install(
      TARGETS flatc EXPORT FlatcTargets
      RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
      CONFIGURATIONS Release
    )

    install(
      EXPORT FlatcTargets
      FILE FlatcTargets.cmake
      NAMESPACE flatbuffers::
      DESTINATION ${FB_CMAKE_DIR}
      CONFIGURATIONS Release
    )
  endif()

  if(FLATBUFFERS_BUILD_SHAREDLIB)
    if(CMAKE_VERSION VERSION_LESS 3.0)
      install(
        TARGETS flatbuffers_shared EXPORT FlatbuffersSharedTargets
        ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
        RUNTIME DESTINATION ${CMAKE_INSTALL_LIBDIR}
        LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
      )
    else()
      install(
        TARGETS flatbuffers_shared EXPORT FlatbuffersSharedTargets
        ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
        RUNTIME DESTINATION ${CMAKE_INSTALL_LIBDIR}
        LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
        INCLUDES DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
      )
    endif()

  install(
      EXPORT FlatbuffersSharedTargets
      FILE FlatbuffersSharedTargets.cmake
      NAMESPACE flatbuffers::
      DESTINATION ${FB_CMAKE_DIR}
    )
  endif()
endif()

if(FLATBUFFERS_BUILD_TESTS)
  enable_testing()

  file(COPY "${CMAKE_CURRENT_SOURCE_DIR}/tests" DESTINATION
       "${CMAKE_CURRENT_BINARY_DIR}")
  add_test(NAME flattests COMMAND flattests)
  if(FLATBUFFERS_BUILD_GRPCTEST)
    add_test(NAME grpctest COMMAND grpctest)
  endif()
endif()

include(CMake/BuildFlatBuffers.cmake)

if(FLATBUFFERS_PACKAGE_DEBIAN)
    include(CMake/PackageDebian.cmake)
endif()
