// Code generated by the FlatBuffers compiler. DO NOT EDIT.

package MyGame

import (
	flatbuffers "github.com/google/flatbuffers/go"
)

type InParentNamespace struct {
	_tab flatbuffers.Table
}

func GetRootAsInParentNamespace(buf []byte, offset flatbuffers.UOffsetT) *InParentNamespace {
	n := flatbuffers.GetUOffsetT(buf[offset:])
	x := &InParentNamespace{}
	x.Init(buf, n+offset)
	return x
}

func (rcv *InParentNamespace) Init(buf []byte, i flatbuffers.UOffsetT) {
	rcv._tab.Bytes = buf
	rcv._tab.Pos = i
}

func (rcv *InParentNamespace) Table() flatbuffers.Table {
	return rcv._tab
}

func InParentNamespaceStart(builder *flatbuffers.Builder) {
	builder.StartObject(0)
}
func InParentNamespaceEnd(builder *flatbuffers.Builder) flatbuffers.UOffsetT {
	return builder.EndObject()
}
