# automatically generated by the FlatBuffers compiler, do not modify

# namespace: Example

import flatbuffers

class Referrable(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAsReferrable(cls, buf, offset):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = Referrable()
        x.Init(buf, n + offset)
        return x

    # Referrable
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # Referrable
    def Id(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint64Flags, o + self._tab.Pos)
        return 0

def ReferrableStart(builder): builder.StartObject(1)
def ReferrableAddId(builder, id): builder.PrependUint64Slot(0, id, 0)
def ReferrableEnd(builder): return builder.EndObject()
