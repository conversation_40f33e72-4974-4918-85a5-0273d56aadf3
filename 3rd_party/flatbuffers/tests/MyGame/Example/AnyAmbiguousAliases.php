<?php
// automatically generated by the FlatBuffers compiler, do not modify

namespace MyGame\Example;

class AnyAmbiguousAliases
{
    const NONE = 0;
    const M1 = 1;
    const M2 = 2;
    const M3 = 3;

    private static $names = array(
        "NONE",
        "M1",
        "M2",
        "M3",
    );

    public static function Name($e)
    {
        if (!isset(self::$names[$e])) {
            throw new \Exception();
        }
        return self::$names[$e];
    }
}
