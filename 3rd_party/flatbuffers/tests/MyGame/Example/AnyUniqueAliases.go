// Code generated by the FlatBuffers compiler. DO NOT EDIT.

package Example

type AnyUniqueAliases = byte
const (
	AnyUniqueAliasesNONE AnyUniqueAliases = 0
	AnyUniqueAliasesM AnyUniqueAliases = 1
	AnyUniqueAliasesT AnyUniqueAliases = 2
	AnyUniqueAliasesM2 AnyUniqueAliases = 3
)

var EnumNamesAnyUniqueAliases = map[AnyUniqueAliases]string{
	AnyUniqueAliasesNONE:"NONE",
	AnyUniqueAliasesM:"M",
	AnyUniqueAliasesT:"T",
	AnyUniqueAliasesM2:"M2",
}

