-- automatically generated by the FlatBuffers compiler, do not modify

-- namespace: Example

local flatbuffers = require('flatbuffers')

local TestSimpleTableWithEnum = {} -- the module
local TestSimpleTableWithEnum_mt = {} -- the class metatable

function TestSimpleTableWithEnum.New()
    local o = {}
    setmetatable(o, {__index = TestSimpleTableWithEnum_mt})
    return o
end
function TestSimpleTableWithEnum.GetRootAsTestSimpleTableWithEnum(buf, offset)
    local n = flatbuffers.N.UOffsetT:Unpack(buf, offset)
    local o = TestSimpleTableWithEnum.New()
    o:Init(buf, n + offset)
    return o
end
function TestSimpleTableWithEnum_mt:Init(buf, pos)
    self.view = flatbuffers.view.New(buf, pos)
end
function TestSimpleTableWithEnum_mt:Color()
    local o = self.view:Offset(4)
    if o ~= 0 then
        return self.view:Get(flatbuffers.N.Int8, o + self.view.pos)
    end
    return 2
end
function TestSimpleTableWithEnum.Start(builder) builder:StartObject(1) end
function TestSimpleTableWithEnum.AddColor(builder, color) builder:PrependInt8Slot(0, color, 2) end
function TestSimpleTableWithEnum.End(builder) return builder:EndObject() end

return TestSimpleTableWithEnum -- return the module