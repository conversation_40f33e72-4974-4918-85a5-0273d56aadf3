// Code generated by the FlatBuffers compiler. DO NOT EDIT.

package Example

type AnyAmbiguousAliases = byte
const (
	AnyAmbiguousAliasesNONE AnyAmbiguousAliases = 0
	AnyAmbiguousAliasesM1 AnyAmbiguousAliases = 1
	AnyAmbiguousAliasesM2 AnyAmbiguousAliases = 2
	AnyAmbiguousAliasesM3 AnyAmbiguousAliases = 3
)

var EnumNamesAnyAmbiguousAliases = map[AnyAmbiguousAliases]string{
	AnyAmbiguousAliasesNONE:"NONE",
	AnyAmbiguousAliasesM1:"M1",
	AnyAmbiguousAliasesM2:"M2",
	AnyAmbiguousAliasesM3:"M3",
}

