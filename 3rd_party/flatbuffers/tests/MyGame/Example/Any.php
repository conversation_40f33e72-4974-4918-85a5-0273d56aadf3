<?php
// automatically generated by the FlatBuffers compiler, do not modify

namespace MyGame\Example;

class Any
{
    const NONE = 0;
    const Monster = 1;
    const TestSimpleTableWithEnum = 2;
    const MyGame_Example2_Monster = 3;

    private static $names = array(
        "NONE",
        "Monster",
        "TestSimpleTableWithEnum",
        "MyGame_Example2_Monster",
    );

    public static function Name($e)
    {
        if (!isset(self::$names[$e])) {
            throw new \Exception();
        }
        return self::$names[$e];
    }
}
