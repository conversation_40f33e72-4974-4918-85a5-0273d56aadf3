# automatically generated by the FlatBuffers compiler, do not modify

# namespace: MyGame

import flatbuffers

class InParentNamespace(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAsInParentNamespace(cls, buf, offset):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = InParentNamespace()
        x.Init(buf, n + offset)
        return x

    # InParentNamespace
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

def InParentNamespaceStart(builder): builder.StartObject(0)
def InParentNamespaceEnd(builder): return builder.EndObject()
