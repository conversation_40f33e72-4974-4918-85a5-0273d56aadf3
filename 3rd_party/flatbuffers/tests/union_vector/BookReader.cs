// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

using global::System;
using global::FlatBuffers;

public struct BookReader : IFlatbufferObject
{
  private Struct __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public void __init(int _i, ByteBuffer _bb) { __p.bb_pos = _i; __p.bb = _bb; }
  public BookReader __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public int BooksRead { get { return __p.bb.GetInt(__p.bb_pos + 0); } }

  public static Offset<BookReader> CreateBookReader(FlatBufferBuilder builder, int BooksRead) {
    builder.Prep(4, 4);
    builder.PutInt(BooksRead);
    return new Offset<BookReader>(builder.Offset);
  }
};

