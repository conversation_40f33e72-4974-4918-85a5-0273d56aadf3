<?php
// automatically generated by the FlatBuffers compiler, do not modify

use \Google\FlatBuffers\Struct;
use \Google\FlatBuffers\Table;
use \Google\FlatBuffers\ByteBuffer;
use \Google\FlatBuffers\FlatBufferBuilder;

class Attacker extends Table
{
    /**
     * @param ByteBuffer $bb
     * @return Attacker
     */
    public static function getRootAsAttacker(ByteBuffer $bb)
    {
        $obj = new Attacker();
        return ($obj->init($bb->getInt($bb->getPosition()) + $bb->getPosition(), $bb));
    }

    public static function AttackerIdentifier()
    {
        return "MOVI";
    }

    public static function AttackerBufferHasIdentifier(ByteBuffer $buf)
    {
        return self::__has_identifier($buf, self::AttackerIdentifier());
    }

    /**
     * @param int $_i offset
     * @param ByteBuffer $_bb
     * @return Attacker
     **/
    public function init($_i, ByteBuffer $_bb)
    {
        $this->bb_pos = $_i;
        $this->bb = $_bb;
        return $this;
    }

    /**
     * @return int
     */
    public function getSwordAttackDamage()
    {
        $o = $this->__offset(4);
        return $o != 0 ? $this->bb->getInt($o + $this->bb_pos) : 0;
    }

    /**
     * @param FlatBufferBuilder $builder
     * @return void
     */
    public static function startAttacker(FlatBufferBuilder $builder)
    {
        $builder->StartObject(1);
    }

    /**
     * @param FlatBufferBuilder $builder
     * @return Attacker
     */
    public static function createAttacker(FlatBufferBuilder $builder, $sword_attack_damage)
    {
        $builder->startObject(1);
        self::addSwordAttackDamage($builder, $sword_attack_damage);
        $o = $builder->endObject();
        return $o;
    }

    /**
     * @param FlatBufferBuilder $builder
     * @param int
     * @return void
     */
    public static function addSwordAttackDamage(FlatBufferBuilder $builder, $swordAttackDamage)
    {
        $builder->addIntX(0, $swordAttackDamage, 0);
    }

    /**
     * @param FlatBufferBuilder $builder
     * @return int table offset
     */
    public static function endAttacker(FlatBufferBuilder $builder)
    {
        $o = $builder->endObject();
        return $o;
    }
}
