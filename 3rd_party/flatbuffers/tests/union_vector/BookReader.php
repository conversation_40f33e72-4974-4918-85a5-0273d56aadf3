<?php
// automatically generated by the FlatBuffers compiler, do not modify

use \Google\FlatBuffers\Struct;
use \Google\FlatBuffers\Table;
use \Google\FlatBuffers\ByteBuffer;
use \Google\FlatBuffers\FlatBufferBuilder;

class BookReader extends Struct
{
    /**
     * @param int $_i offset
     * @param ByteBuffer $_bb
     * @return BookReader
     **/
    public function init($_i, ByteBuffer $_bb)
    {
        $this->bb_pos = $_i;
        $this->bb = $_bb;
        return $this;
    }

    /**
     * @return int
     */
    public function GetBooksRead()
    {
        return $this->bb->getInt($this->bb_pos + 0);
    }


    /**
     * @return int offset
     */
    public static function createBookReader(FlatBufferBuilder $builder, $booksRead)
    {
        $builder->prep(4, 4);
        $builder->putInt($booksRead);
        return $builder->offset();
    }
}
