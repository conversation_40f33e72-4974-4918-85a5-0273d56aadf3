// automatically generated by the FlatBuffers compiler, do not modify

/**
 * @const
 * @namespace
 */
var MyGame = MyGame || {};

/**
 * @const
 * @namespace
 */
MyGame.Example = MyGame.Example || {};

/**
 * @const
 * @namespace
 */
MyGame.Example2 = MyGame.Example2 || {};

/**
 * @const
 * @namespace
 */
MyGame.OtherNameSpace = MyGame.OtherNameSpace || {};

/**
 * @enum
 */
MyGame.Example.Color = {
  Red: 1, 1: 'Red',
  Green: 2, 2: 'Green',
  Blue: 8, 8: 'Blue'
};

/**
 * @enum
 */
MyGame.Example.Any = {
  NONE: 0, 0: 'NONE',
  Monster: 1, 1: 'Monster',
  TestSimpleTableWithEnum: 2, 2: 'TestSimpleTableWithEnum',
  MyGame_Example2_Monster: 3, 3: 'MyGame_Example2_Monster'
};

/**
 * @enum
 */
MyGame.Example.AnyUniqueAliases = {
  NONE: 0, 0: 'NONE',
  M: 1, 1: 'M',
  T: 2, 2: 'T',
  M2: 3, 3: 'M2'
};

/**
 * @enum
 */
MyGame.Example.AnyAmbiguousAliases = {
  NONE: 0, 0: 'NONE',
  M1: 1, 1: 'M1',
  M2: 2, 2: 'M2',
  M3: 3, 3: 'M3'
};

/**
 * @constructor
 */
MyGame.InParentNamespace = function() {
  /**
   * @type {flatbuffers.ByteBuffer}
   */
  this.bb = null;

  /**
   * @type {number}
   */
  this.bb_pos = 0;
};

/**
 * @param {number} i
 * @param {flatbuffers.ByteBuffer} bb
 * @returns {MyGame.InParentNamespace}
 */
MyGame.InParentNamespace.prototype.__init = function(i, bb) {
  this.bb_pos = i;
  this.bb = bb;
  return this;
};

/**
 * @param {flatbuffers.ByteBuffer} bb
 * @param {MyGame.InParentNamespace=} obj
 * @returns {MyGame.InParentNamespace}
 */
MyGame.InParentNamespace.getRootAsInParentNamespace = function(bb, obj) {
  return (obj || new MyGame.InParentNamespace).__init(bb.readInt32(bb.position()) + bb.position(), bb);
};

/**
 * @param {flatbuffers.Builder} builder
 */
MyGame.InParentNamespace.startInParentNamespace = function(builder) {
  builder.startObject(0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @returns {flatbuffers.Offset}
 */
MyGame.InParentNamespace.endInParentNamespace = function(builder) {
  var offset = builder.endObject();
  return offset;
};

/**
 * @constructor
 */
MyGame.Example2.Monster = function() {
  /**
   * @type {flatbuffers.ByteBuffer}
   */
  this.bb = null;

  /**
   * @type {number}
   */
  this.bb_pos = 0;
};

/**
 * @param {number} i
 * @param {flatbuffers.ByteBuffer} bb
 * @returns {MyGame.Example2.Monster}
 */
MyGame.Example2.Monster.prototype.__init = function(i, bb) {
  this.bb_pos = i;
  this.bb = bb;
  return this;
};

/**
 * @param {flatbuffers.ByteBuffer} bb
 * @param {MyGame.Example2.Monster=} obj
 * @returns {MyGame.Example2.Monster}
 */
MyGame.Example2.Monster.getRootAsMonster = function(bb, obj) {
  return (obj || new MyGame.Example2.Monster).__init(bb.readInt32(bb.position()) + bb.position(), bb);
};

/**
 * @param {flatbuffers.Builder} builder
 */
MyGame.Example2.Monster.startMonster = function(builder) {
  builder.startObject(0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @returns {flatbuffers.Offset}
 */
MyGame.Example2.Monster.endMonster = function(builder) {
  var offset = builder.endObject();
  return offset;
};

/**
 * @constructor
 */
MyGame.Example.Test = function() {
  /**
   * @type {flatbuffers.ByteBuffer}
   */
  this.bb = null;

  /**
   * @type {number}
   */
  this.bb_pos = 0;
};

/**
 * @param {number} i
 * @param {flatbuffers.ByteBuffer} bb
 * @returns {MyGame.Example.Test}
 */
MyGame.Example.Test.prototype.__init = function(i, bb) {
  this.bb_pos = i;
  this.bb = bb;
  return this;
};

/**
 * @returns {number}
 */
MyGame.Example.Test.prototype.a = function() {
  return this.bb.readInt16(this.bb_pos);
};

/**
 * @param {number} value
 * @returns {boolean}
 */
MyGame.Example.Test.prototype.mutate_a = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 0);

  if (offset === 0) {
    return false;
  }

  this.bb.writeInt16(this.bb_pos + offset, value);
  return true;
};

/**
 * @returns {number}
 */
MyGame.Example.Test.prototype.b = function() {
  return this.bb.readInt8(this.bb_pos + 2);
};

/**
 * @param {number} value
 * @returns {boolean}
 */
MyGame.Example.Test.prototype.mutate_b = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 2);

  if (offset === 0) {
    return false;
  }

  this.bb.writeInt8(this.bb_pos + offset, value);
  return true;
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} a
 * @param {number} b
 * @returns {flatbuffers.Offset}
 */
MyGame.Example.Test.createTest = function(builder, a, b) {
  builder.prep(2, 4);
  builder.pad(1);
  builder.writeInt8(b);
  builder.writeInt16(a);
  return builder.offset();
};

/**
 * @constructor
 */
MyGame.Example.TestSimpleTableWithEnum = function() {
  /**
   * @type {flatbuffers.ByteBuffer}
   */
  this.bb = null;

  /**
   * @type {number}
   */
  this.bb_pos = 0;
};

/**
 * @param {number} i
 * @param {flatbuffers.ByteBuffer} bb
 * @returns {MyGame.Example.TestSimpleTableWithEnum}
 */
MyGame.Example.TestSimpleTableWithEnum.prototype.__init = function(i, bb) {
  this.bb_pos = i;
  this.bb = bb;
  return this;
};

/**
 * @param {flatbuffers.ByteBuffer} bb
 * @param {MyGame.Example.TestSimpleTableWithEnum=} obj
 * @returns {MyGame.Example.TestSimpleTableWithEnum}
 */
MyGame.Example.TestSimpleTableWithEnum.getRootAsTestSimpleTableWithEnum = function(bb, obj) {
  return (obj || new MyGame.Example.TestSimpleTableWithEnum).__init(bb.readInt32(bb.position()) + bb.position(), bb);
};

/**
 * @returns {MyGame.Example.Color}
 */
MyGame.Example.TestSimpleTableWithEnum.prototype.color = function() {
  var offset = this.bb.__offset(this.bb_pos, 4);
  return offset ? /** @type {MyGame.Example.Color} */ (this.bb.readInt8(this.bb_pos + offset)) : MyGame.Example.Color.Green;
};

/**
 * @param {MyGame.Example.Color} value
 * @returns {boolean}
 */
MyGame.Example.TestSimpleTableWithEnum.prototype.mutate_color = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 4);

  if (offset === 0) {
    return false;
  }

  this.bb.writeInt8(this.bb_pos + offset, value);
  return true;
};

/**
 * @param {flatbuffers.Builder} builder
 */
MyGame.Example.TestSimpleTableWithEnum.startTestSimpleTableWithEnum = function(builder) {
  builder.startObject(1);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {MyGame.Example.Color} color
 */
MyGame.Example.TestSimpleTableWithEnum.addColor = function(builder, color) {
  builder.addFieldInt8(0, color, MyGame.Example.Color.Green);
};

/**
 * @param {flatbuffers.Builder} builder
 * @returns {flatbuffers.Offset}
 */
MyGame.Example.TestSimpleTableWithEnum.endTestSimpleTableWithEnum = function(builder) {
  var offset = builder.endObject();
  return offset;
};

/**
 * @constructor
 */
MyGame.Example.Vec3 = function() {
  /**
   * @type {flatbuffers.ByteBuffer}
   */
  this.bb = null;

  /**
   * @type {number}
   */
  this.bb_pos = 0;
};

/**
 * @param {number} i
 * @param {flatbuffers.ByteBuffer} bb
 * @returns {MyGame.Example.Vec3}
 */
MyGame.Example.Vec3.prototype.__init = function(i, bb) {
  this.bb_pos = i;
  this.bb = bb;
  return this;
};

/**
 * @returns {number}
 */
MyGame.Example.Vec3.prototype.x = function() {
  return this.bb.readFloat32(this.bb_pos);
};

/**
 * @param {number} value
 * @returns {boolean}
 */
MyGame.Example.Vec3.prototype.mutate_x = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 0);

  if (offset === 0) {
    return false;
  }

  this.bb.writeFloat32(this.bb_pos + offset, value);
  return true;
};

/**
 * @returns {number}
 */
MyGame.Example.Vec3.prototype.y = function() {
  return this.bb.readFloat32(this.bb_pos + 4);
};

/**
 * @param {number} value
 * @returns {boolean}
 */
MyGame.Example.Vec3.prototype.mutate_y = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 4);

  if (offset === 0) {
    return false;
  }

  this.bb.writeFloat32(this.bb_pos + offset, value);
  return true;
};

/**
 * @returns {number}
 */
MyGame.Example.Vec3.prototype.z = function() {
  return this.bb.readFloat32(this.bb_pos + 8);
};

/**
 * @param {number} value
 * @returns {boolean}
 */
MyGame.Example.Vec3.prototype.mutate_z = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 8);

  if (offset === 0) {
    return false;
  }

  this.bb.writeFloat32(this.bb_pos + offset, value);
  return true;
};

/**
 * @returns {number}
 */
MyGame.Example.Vec3.prototype.test1 = function() {
  return this.bb.readFloat64(this.bb_pos + 16);
};

/**
 * @param {number} value
 * @returns {boolean}
 */
MyGame.Example.Vec3.prototype.mutate_test1 = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 16);

  if (offset === 0) {
    return false;
  }

  this.bb.writeFloat64(this.bb_pos + offset, value);
  return true;
};

/**
 * @returns {MyGame.Example.Color}
 */
MyGame.Example.Vec3.prototype.test2 = function() {
  return /** @type {MyGame.Example.Color} */ (this.bb.readInt8(this.bb_pos + 24));
};

/**
 * @param {MyGame.Example.Color} value
 * @returns {boolean}
 */
MyGame.Example.Vec3.prototype.mutate_test2 = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 24);

  if (offset === 0) {
    return false;
  }

  this.bb.writeInt8(this.bb_pos + offset, value);
  return true;
};

/**
 * @param {MyGame.Example.Test=} obj
 * @returns {MyGame.Example.Test|null}
 */
MyGame.Example.Vec3.prototype.test3 = function(obj) {
  return (obj || new MyGame.Example.Test).__init(this.bb_pos + 26, this.bb);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} x
 * @param {number} y
 * @param {number} z
 * @param {number} test1
 * @param {MyGame.Example.Color} test2
 * @param {number} test3_a
 * @param {number} test3_b
 * @returns {flatbuffers.Offset}
 */
MyGame.Example.Vec3.createVec3 = function(builder, x, y, z, test1, test2, test3_a, test3_b) {
  builder.prep(16, 32);
  builder.pad(2);
  builder.prep(2, 4);
  builder.pad(1);
  builder.writeInt8(test3_b);
  builder.writeInt16(test3_a);
  builder.pad(1);
  builder.writeInt8(test2);
  builder.writeFloat64(test1);
  builder.pad(4);
  builder.writeFloat32(z);
  builder.writeFloat32(y);
  builder.writeFloat32(x);
  return builder.offset();
};

/**
 * @constructor
 */
MyGame.Example.Ability = function() {
  /**
   * @type {flatbuffers.ByteBuffer}
   */
  this.bb = null;

  /**
   * @type {number}
   */
  this.bb_pos = 0;
};

/**
 * @param {number} i
 * @param {flatbuffers.ByteBuffer} bb
 * @returns {MyGame.Example.Ability}
 */
MyGame.Example.Ability.prototype.__init = function(i, bb) {
  this.bb_pos = i;
  this.bb = bb;
  return this;
};

/**
 * @returns {number}
 */
MyGame.Example.Ability.prototype.id = function() {
  return this.bb.readUint32(this.bb_pos);
};

/**
 * @param {number} value
 * @returns {boolean}
 */
MyGame.Example.Ability.prototype.mutate_id = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 0);

  if (offset === 0) {
    return false;
  }

  this.bb.writeUint32(this.bb_pos + offset, value);
  return true;
};

/**
 * @returns {number}
 */
MyGame.Example.Ability.prototype.distance = function() {
  return this.bb.readUint32(this.bb_pos + 4);
};

/**
 * @param {number} value
 * @returns {boolean}
 */
MyGame.Example.Ability.prototype.mutate_distance = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 4);

  if (offset === 0) {
    return false;
  }

  this.bb.writeUint32(this.bb_pos + offset, value);
  return true;
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} id
 * @param {number} distance
 * @returns {flatbuffers.Offset}
 */
MyGame.Example.Ability.createAbility = function(builder, id, distance) {
  builder.prep(4, 8);
  builder.writeInt32(distance);
  builder.writeInt32(id);
  return builder.offset();
};

/**
 * @constructor
 */
MyGame.Example.Stat = function() {
  /**
   * @type {flatbuffers.ByteBuffer}
   */
  this.bb = null;

  /**
   * @type {number}
   */
  this.bb_pos = 0;
};

/**
 * @param {number} i
 * @param {flatbuffers.ByteBuffer} bb
 * @returns {MyGame.Example.Stat}
 */
MyGame.Example.Stat.prototype.__init = function(i, bb) {
  this.bb_pos = i;
  this.bb = bb;
  return this;
};

/**
 * @param {flatbuffers.ByteBuffer} bb
 * @param {MyGame.Example.Stat=} obj
 * @returns {MyGame.Example.Stat}
 */
MyGame.Example.Stat.getRootAsStat = function(bb, obj) {
  return (obj || new MyGame.Example.Stat).__init(bb.readInt32(bb.position()) + bb.position(), bb);
};

/**
 * @param {flatbuffers.Encoding=} optionalEncoding
 * @returns {string|Uint8Array|null}
 */
MyGame.Example.Stat.prototype.id = function(optionalEncoding) {
  var offset = this.bb.__offset(this.bb_pos, 4);
  return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
};

/**
 * @returns {flatbuffers.Long}
 */
MyGame.Example.Stat.prototype.val = function() {
  var offset = this.bb.__offset(this.bb_pos, 6);
  return offset ? this.bb.readInt64(this.bb_pos + offset) : this.bb.createLong(0, 0);
};

/**
 * @param {flatbuffers.Long} value
 * @returns {boolean}
 */
MyGame.Example.Stat.prototype.mutate_val = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 6);

  if (offset === 0) {
    return false;
  }

  this.bb.writeInt64(this.bb_pos + offset, value);
  return true;
};

/**
 * @returns {number}
 */
MyGame.Example.Stat.prototype.count = function() {
  var offset = this.bb.__offset(this.bb_pos, 8);
  return offset ? this.bb.readUint16(this.bb_pos + offset) : 0;
};

/**
 * @param {number} value
 * @returns {boolean}
 */
MyGame.Example.Stat.prototype.mutate_count = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 8);

  if (offset === 0) {
    return false;
  }

  this.bb.writeUint16(this.bb_pos + offset, value);
  return true;
};

/**
 * @param {flatbuffers.Builder} builder
 */
MyGame.Example.Stat.startStat = function(builder) {
  builder.startObject(3);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} idOffset
 */
MyGame.Example.Stat.addId = function(builder, idOffset) {
  builder.addFieldOffset(0, idOffset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Long} val
 */
MyGame.Example.Stat.addVal = function(builder, val) {
  builder.addFieldInt64(1, val, builder.createLong(0, 0));
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} count
 */
MyGame.Example.Stat.addCount = function(builder, count) {
  builder.addFieldInt16(2, count, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @returns {flatbuffers.Offset}
 */
MyGame.Example.Stat.endStat = function(builder) {
  var offset = builder.endObject();
  return offset;
};

/**
 * @constructor
 */
MyGame.Example.Referrable = function() {
  /**
   * @type {flatbuffers.ByteBuffer}
   */
  this.bb = null;

  /**
   * @type {number}
   */
  this.bb_pos = 0;
};

/**
 * @param {number} i
 * @param {flatbuffers.ByteBuffer} bb
 * @returns {MyGame.Example.Referrable}
 */
MyGame.Example.Referrable.prototype.__init = function(i, bb) {
  this.bb_pos = i;
  this.bb = bb;
  return this;
};

/**
 * @param {flatbuffers.ByteBuffer} bb
 * @param {MyGame.Example.Referrable=} obj
 * @returns {MyGame.Example.Referrable}
 */
MyGame.Example.Referrable.getRootAsReferrable = function(bb, obj) {
  return (obj || new MyGame.Example.Referrable).__init(bb.readInt32(bb.position()) + bb.position(), bb);
};

/**
 * @returns {flatbuffers.Long}
 */
MyGame.Example.Referrable.prototype.id = function() {
  var offset = this.bb.__offset(this.bb_pos, 4);
  return offset ? this.bb.readUint64(this.bb_pos + offset) : this.bb.createLong(0, 0);
};

/**
 * @param {flatbuffers.Long} value
 * @returns {boolean}
 */
MyGame.Example.Referrable.prototype.mutate_id = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 4);

  if (offset === 0) {
    return false;
  }

  this.bb.writeUint64(this.bb_pos + offset, value);
  return true;
};

/**
 * @param {flatbuffers.Builder} builder
 */
MyGame.Example.Referrable.startReferrable = function(builder) {
  builder.startObject(1);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Long} id
 */
MyGame.Example.Referrable.addId = function(builder, id) {
  builder.addFieldInt64(0, id, builder.createLong(0, 0));
};

/**
 * @param {flatbuffers.Builder} builder
 * @returns {flatbuffers.Offset}
 */
MyGame.Example.Referrable.endReferrable = function(builder) {
  var offset = builder.endObject();
  return offset;
};

/**
 * an example documentation comment: monster object
 *
 * @constructor
 */
MyGame.Example.Monster = function() {
  /**
   * @type {flatbuffers.ByteBuffer}
   */
  this.bb = null;

  /**
   * @type {number}
   */
  this.bb_pos = 0;
};

/**
 * @param {number} i
 * @param {flatbuffers.ByteBuffer} bb
 * @returns {MyGame.Example.Monster}
 */
MyGame.Example.Monster.prototype.__init = function(i, bb) {
  this.bb_pos = i;
  this.bb = bb;
  return this;
};

/**
 * @param {flatbuffers.ByteBuffer} bb
 * @param {MyGame.Example.Monster=} obj
 * @returns {MyGame.Example.Monster}
 */
MyGame.Example.Monster.getRootAsMonster = function(bb, obj) {
  return (obj || new MyGame.Example.Monster).__init(bb.readInt32(bb.position()) + bb.position(), bb);
};

/**
 * @param {flatbuffers.ByteBuffer} bb
 * @returns {boolean}
 */
MyGame.Example.Monster.bufferHasIdentifier = function(bb) {
  return bb.__has_identifier('MONS');
};

/**
 * @param {MyGame.Example.Vec3=} obj
 * @returns {MyGame.Example.Vec3|null}
 */
MyGame.Example.Monster.prototype.pos = function(obj) {
  var offset = this.bb.__offset(this.bb_pos, 4);
  return offset ? (obj || new MyGame.Example.Vec3).__init(this.bb_pos + offset, this.bb) : null;
};

/**
 * @returns {number}
 */
MyGame.Example.Monster.prototype.mana = function() {
  var offset = this.bb.__offset(this.bb_pos, 6);
  return offset ? this.bb.readInt16(this.bb_pos + offset) : 150;
};

/**
 * @param {number} value
 * @returns {boolean}
 */
MyGame.Example.Monster.prototype.mutate_mana = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 6);

  if (offset === 0) {
    return false;
  }

  this.bb.writeInt16(this.bb_pos + offset, value);
  return true;
};

/**
 * @returns {number}
 */
MyGame.Example.Monster.prototype.hp = function() {
  var offset = this.bb.__offset(this.bb_pos, 8);
  return offset ? this.bb.readInt16(this.bb_pos + offset) : 100;
};

/**
 * @param {number} value
 * @returns {boolean}
 */
MyGame.Example.Monster.prototype.mutate_hp = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 8);

  if (offset === 0) {
    return false;
  }

  this.bb.writeInt16(this.bb_pos + offset, value);
  return true;
};

/**
 * @param {flatbuffers.Encoding=} optionalEncoding
 * @returns {string|Uint8Array|null}
 */
MyGame.Example.Monster.prototype.name = function(optionalEncoding) {
  var offset = this.bb.__offset(this.bb_pos, 10);
  return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
};

/**
 * @param {number} index
 * @returns {number}
 */
MyGame.Example.Monster.prototype.inventory = function(index) {
  var offset = this.bb.__offset(this.bb_pos, 14);
  return offset ? this.bb.readUint8(this.bb.__vector(this.bb_pos + offset) + index) : 0;
};

/**
 * @returns {number}
 */
MyGame.Example.Monster.prototype.inventoryLength = function() {
  var offset = this.bb.__offset(this.bb_pos, 14);
  return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
};

/**
 * @returns {Uint8Array}
 */
MyGame.Example.Monster.prototype.inventoryArray = function() {
  var offset = this.bb.__offset(this.bb_pos, 14);
  return offset ? new Uint8Array(this.bb.bytes().buffer, this.bb.bytes().byteOffset + this.bb.__vector(this.bb_pos + offset), this.bb.__vector_len(this.bb_pos + offset)) : null;
};

/**
 * @returns {MyGame.Example.Color}
 */
MyGame.Example.Monster.prototype.color = function() {
  var offset = this.bb.__offset(this.bb_pos, 16);
  return offset ? /** @type {MyGame.Example.Color} */ (this.bb.readInt8(this.bb_pos + offset)) : MyGame.Example.Color.Blue;
};

/**
 * @param {MyGame.Example.Color} value
 * @returns {boolean}
 */
MyGame.Example.Monster.prototype.mutate_color = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 16);

  if (offset === 0) {
    return false;
  }

  this.bb.writeInt8(this.bb_pos + offset, value);
  return true;
};

/**
 * @returns {MyGame.Example.Any}
 */
MyGame.Example.Monster.prototype.testType = function() {
  var offset = this.bb.__offset(this.bb_pos, 18);
  return offset ? /** @type {MyGame.Example.Any} */ (this.bb.readUint8(this.bb_pos + offset)) : MyGame.Example.Any.NONE;
};

/**
 * @param {MyGame.Example.Any} value
 * @returns {boolean}
 */
MyGame.Example.Monster.prototype.mutate_test_type = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 18);

  if (offset === 0) {
    return false;
  }

  this.bb.writeUint8(this.bb_pos + offset, value);
  return true;
};

/**
 * @param {flatbuffers.Table} obj
 * @returns {?flatbuffers.Table}
 */
MyGame.Example.Monster.prototype.test = function(obj) {
  var offset = this.bb.__offset(this.bb_pos, 20);
  return offset ? this.bb.__union(obj, this.bb_pos + offset) : null;
};

/**
 * @param {number} index
 * @param {MyGame.Example.Test=} obj
 * @returns {MyGame.Example.Test}
 */
MyGame.Example.Monster.prototype.test4 = function(index, obj) {
  var offset = this.bb.__offset(this.bb_pos, 22);
  return offset ? (obj || new MyGame.Example.Test).__init(this.bb.__vector(this.bb_pos + offset) + index * 4, this.bb) : null;
};

/**
 * @returns {number}
 */
MyGame.Example.Monster.prototype.test4Length = function() {
  var offset = this.bb.__offset(this.bb_pos, 22);
  return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
};

/**
 * @param {number} index
 * @param {flatbuffers.Encoding=} optionalEncoding
 * @returns {string|Uint8Array}
 */
MyGame.Example.Monster.prototype.testarrayofstring = function(index, optionalEncoding) {
  var offset = this.bb.__offset(this.bb_pos, 24);
  return offset ? this.bb.__string(this.bb.__vector(this.bb_pos + offset) + index * 4, optionalEncoding) : null;
};

/**
 * @returns {number}
 */
MyGame.Example.Monster.prototype.testarrayofstringLength = function() {
  var offset = this.bb.__offset(this.bb_pos, 24);
  return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
};

/**
 * an example documentation comment: this will end up in the generated code
 * multiline too
 *
 * @param {number} index
 * @param {MyGame.Example.Monster=} obj
 * @returns {MyGame.Example.Monster}
 */
MyGame.Example.Monster.prototype.testarrayoftables = function(index, obj) {
  var offset = this.bb.__offset(this.bb_pos, 26);
  return offset ? (obj || new MyGame.Example.Monster).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos + offset) + index * 4), this.bb) : null;
};

/**
 * @returns {number}
 */
MyGame.Example.Monster.prototype.testarrayoftablesLength = function() {
  var offset = this.bb.__offset(this.bb_pos, 26);
  return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
};

/**
 * @param {MyGame.Example.Monster=} obj
 * @returns {MyGame.Example.Monster|null}
 */
MyGame.Example.Monster.prototype.enemy = function(obj) {
  var offset = this.bb.__offset(this.bb_pos, 28);
  return offset ? (obj || new MyGame.Example.Monster).__init(this.bb.__indirect(this.bb_pos + offset), this.bb) : null;
};

/**
 * @param {number} index
 * @returns {number}
 */
MyGame.Example.Monster.prototype.testnestedflatbuffer = function(index) {
  var offset = this.bb.__offset(this.bb_pos, 30);
  return offset ? this.bb.readUint8(this.bb.__vector(this.bb_pos + offset) + index) : 0;
};

/**
 * @returns {number}
 */
MyGame.Example.Monster.prototype.testnestedflatbufferLength = function() {
  var offset = this.bb.__offset(this.bb_pos, 30);
  return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
};

/**
 * @returns {Uint8Array}
 */
MyGame.Example.Monster.prototype.testnestedflatbufferArray = function() {
  var offset = this.bb.__offset(this.bb_pos, 30);
  return offset ? new Uint8Array(this.bb.bytes().buffer, this.bb.bytes().byteOffset + this.bb.__vector(this.bb_pos + offset), this.bb.__vector_len(this.bb_pos + offset)) : null;
};

/**
 * @param {MyGame.Example.Stat=} obj
 * @returns {MyGame.Example.Stat|null}
 */
MyGame.Example.Monster.prototype.testempty = function(obj) {
  var offset = this.bb.__offset(this.bb_pos, 32);
  return offset ? (obj || new MyGame.Example.Stat).__init(this.bb.__indirect(this.bb_pos + offset), this.bb) : null;
};

/**
 * @returns {boolean}
 */
MyGame.Example.Monster.prototype.testbool = function() {
  var offset = this.bb.__offset(this.bb_pos, 34);
  return offset ? !!this.bb.readInt8(this.bb_pos + offset) : false;
};

/**
 * @param {boolean} value
 * @returns {boolean}
 */
MyGame.Example.Monster.prototype.mutate_testbool = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 34);

  if (offset === 0) {
    return false;
  }

  this.bb.writeInt8(this.bb_pos + offset, value);
  return true;
};

/**
 * @returns {number}
 */
MyGame.Example.Monster.prototype.testhashs32Fnv1 = function() {
  var offset = this.bb.__offset(this.bb_pos, 36);
  return offset ? this.bb.readInt32(this.bb_pos + offset) : 0;
};

/**
 * @param {number} value
 * @returns {boolean}
 */
MyGame.Example.Monster.prototype.mutate_testhashs32_fnv1 = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 36);

  if (offset === 0) {
    return false;
  }

  this.bb.writeInt32(this.bb_pos + offset, value);
  return true;
};

/**
 * @returns {number}
 */
MyGame.Example.Monster.prototype.testhashu32Fnv1 = function() {
  var offset = this.bb.__offset(this.bb_pos, 38);
  return offset ? this.bb.readUint32(this.bb_pos + offset) : 0;
};

/**
 * @param {number} value
 * @returns {boolean}
 */
MyGame.Example.Monster.prototype.mutate_testhashu32_fnv1 = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 38);

  if (offset === 0) {
    return false;
  }

  this.bb.writeUint32(this.bb_pos + offset, value);
  return true;
};

/**
 * @returns {flatbuffers.Long}
 */
MyGame.Example.Monster.prototype.testhashs64Fnv1 = function() {
  var offset = this.bb.__offset(this.bb_pos, 40);
  return offset ? this.bb.readInt64(this.bb_pos + offset) : this.bb.createLong(0, 0);
};

/**
 * @param {flatbuffers.Long} value
 * @returns {boolean}
 */
MyGame.Example.Monster.prototype.mutate_testhashs64_fnv1 = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 40);

  if (offset === 0) {
    return false;
  }

  this.bb.writeInt64(this.bb_pos + offset, value);
  return true;
};

/**
 * @returns {flatbuffers.Long}
 */
MyGame.Example.Monster.prototype.testhashu64Fnv1 = function() {
  var offset = this.bb.__offset(this.bb_pos, 42);
  return offset ? this.bb.readUint64(this.bb_pos + offset) : this.bb.createLong(0, 0);
};

/**
 * @param {flatbuffers.Long} value
 * @returns {boolean}
 */
MyGame.Example.Monster.prototype.mutate_testhashu64_fnv1 = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 42);

  if (offset === 0) {
    return false;
  }

  this.bb.writeUint64(this.bb_pos + offset, value);
  return true;
};

/**
 * @returns {number}
 */
MyGame.Example.Monster.prototype.testhashs32Fnv1a = function() {
  var offset = this.bb.__offset(this.bb_pos, 44);
  return offset ? this.bb.readInt32(this.bb_pos + offset) : 0;
};

/**
 * @param {number} value
 * @returns {boolean}
 */
MyGame.Example.Monster.prototype.mutate_testhashs32_fnv1a = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 44);

  if (offset === 0) {
    return false;
  }

  this.bb.writeInt32(this.bb_pos + offset, value);
  return true;
};

/**
 * @returns {number}
 */
MyGame.Example.Monster.prototype.testhashu32Fnv1a = function() {
  var offset = this.bb.__offset(this.bb_pos, 46);
  return offset ? this.bb.readUint32(this.bb_pos + offset) : 0;
};

/**
 * @param {number} value
 * @returns {boolean}
 */
MyGame.Example.Monster.prototype.mutate_testhashu32_fnv1a = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 46);

  if (offset === 0) {
    return false;
  }

  this.bb.writeUint32(this.bb_pos + offset, value);
  return true;
};

/**
 * @returns {flatbuffers.Long}
 */
MyGame.Example.Monster.prototype.testhashs64Fnv1a = function() {
  var offset = this.bb.__offset(this.bb_pos, 48);
  return offset ? this.bb.readInt64(this.bb_pos + offset) : this.bb.createLong(0, 0);
};

/**
 * @param {flatbuffers.Long} value
 * @returns {boolean}
 */
MyGame.Example.Monster.prototype.mutate_testhashs64_fnv1a = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 48);

  if (offset === 0) {
    return false;
  }

  this.bb.writeInt64(this.bb_pos + offset, value);
  return true;
};

/**
 * @returns {flatbuffers.Long}
 */
MyGame.Example.Monster.prototype.testhashu64Fnv1a = function() {
  var offset = this.bb.__offset(this.bb_pos, 50);
  return offset ? this.bb.readUint64(this.bb_pos + offset) : this.bb.createLong(0, 0);
};

/**
 * @param {flatbuffers.Long} value
 * @returns {boolean}
 */
MyGame.Example.Monster.prototype.mutate_testhashu64_fnv1a = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 50);

  if (offset === 0) {
    return false;
  }

  this.bb.writeUint64(this.bb_pos + offset, value);
  return true;
};

/**
 * @param {number} index
 * @returns {boolean}
 */
MyGame.Example.Monster.prototype.testarrayofbools = function(index) {
  var offset = this.bb.__offset(this.bb_pos, 52);
  return offset ? !!this.bb.readInt8(this.bb.__vector(this.bb_pos + offset) + index) : false;
};

/**
 * @returns {number}
 */
MyGame.Example.Monster.prototype.testarrayofboolsLength = function() {
  var offset = this.bb.__offset(this.bb_pos, 52);
  return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
};

/**
 * @returns {Int8Array}
 */
MyGame.Example.Monster.prototype.testarrayofboolsArray = function() {
  var offset = this.bb.__offset(this.bb_pos, 52);
  return offset ? new Int8Array(this.bb.bytes().buffer, this.bb.bytes().byteOffset + this.bb.__vector(this.bb_pos + offset), this.bb.__vector_len(this.bb_pos + offset)) : null;
};

/**
 * @returns {number}
 */
MyGame.Example.Monster.prototype.testf = function() {
  var offset = this.bb.__offset(this.bb_pos, 54);
  return offset ? this.bb.readFloat32(this.bb_pos + offset) : 3.14159;
};

/**
 * @param {number} value
 * @returns {boolean}
 */
MyGame.Example.Monster.prototype.mutate_testf = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 54);

  if (offset === 0) {
    return false;
  }

  this.bb.writeFloat32(this.bb_pos + offset, value);
  return true;
};

/**
 * @returns {number}
 */
MyGame.Example.Monster.prototype.testf2 = function() {
  var offset = this.bb.__offset(this.bb_pos, 56);
  return offset ? this.bb.readFloat32(this.bb_pos + offset) : 3.0;
};

/**
 * @param {number} value
 * @returns {boolean}
 */
MyGame.Example.Monster.prototype.mutate_testf2 = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 56);

  if (offset === 0) {
    return false;
  }

  this.bb.writeFloat32(this.bb_pos + offset, value);
  return true;
};

/**
 * @returns {number}
 */
MyGame.Example.Monster.prototype.testf3 = function() {
  var offset = this.bb.__offset(this.bb_pos, 58);
  return offset ? this.bb.readFloat32(this.bb_pos + offset) : 0.0;
};

/**
 * @param {number} value
 * @returns {boolean}
 */
MyGame.Example.Monster.prototype.mutate_testf3 = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 58);

  if (offset === 0) {
    return false;
  }

  this.bb.writeFloat32(this.bb_pos + offset, value);
  return true;
};

/**
 * @param {number} index
 * @param {flatbuffers.Encoding=} optionalEncoding
 * @returns {string|Uint8Array}
 */
MyGame.Example.Monster.prototype.testarrayofstring2 = function(index, optionalEncoding) {
  var offset = this.bb.__offset(this.bb_pos, 60);
  return offset ? this.bb.__string(this.bb.__vector(this.bb_pos + offset) + index * 4, optionalEncoding) : null;
};

/**
 * @returns {number}
 */
MyGame.Example.Monster.prototype.testarrayofstring2Length = function() {
  var offset = this.bb.__offset(this.bb_pos, 60);
  return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
};

/**
 * @param {number} index
 * @param {MyGame.Example.Ability=} obj
 * @returns {MyGame.Example.Ability}
 */
MyGame.Example.Monster.prototype.testarrayofsortedstruct = function(index, obj) {
  var offset = this.bb.__offset(this.bb_pos, 62);
  return offset ? (obj || new MyGame.Example.Ability).__init(this.bb.__vector(this.bb_pos + offset) + index * 8, this.bb) : null;
};

/**
 * @returns {number}
 */
MyGame.Example.Monster.prototype.testarrayofsortedstructLength = function() {
  var offset = this.bb.__offset(this.bb_pos, 62);
  return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
};

/**
 * @param {number} index
 * @returns {number}
 */
MyGame.Example.Monster.prototype.flex = function(index) {
  var offset = this.bb.__offset(this.bb_pos, 64);
  return offset ? this.bb.readUint8(this.bb.__vector(this.bb_pos + offset) + index) : 0;
};

/**
 * @returns {number}
 */
MyGame.Example.Monster.prototype.flexLength = function() {
  var offset = this.bb.__offset(this.bb_pos, 64);
  return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
};

/**
 * @returns {Uint8Array}
 */
MyGame.Example.Monster.prototype.flexArray = function() {
  var offset = this.bb.__offset(this.bb_pos, 64);
  return offset ? new Uint8Array(this.bb.bytes().buffer, this.bb.bytes().byteOffset + this.bb.__vector(this.bb_pos + offset), this.bb.__vector_len(this.bb_pos + offset)) : null;
};

/**
 * @param {number} index
 * @param {MyGame.Example.Test=} obj
 * @returns {MyGame.Example.Test}
 */
MyGame.Example.Monster.prototype.test5 = function(index, obj) {
  var offset = this.bb.__offset(this.bb_pos, 66);
  return offset ? (obj || new MyGame.Example.Test).__init(this.bb.__vector(this.bb_pos + offset) + index * 4, this.bb) : null;
};

/**
 * @returns {number}
 */
MyGame.Example.Monster.prototype.test5Length = function() {
  var offset = this.bb.__offset(this.bb_pos, 66);
  return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
};

/**
 * @param {number} index
 * @returns {flatbuffers.Long}
 */
MyGame.Example.Monster.prototype.vectorOfLongs = function(index) {
  var offset = this.bb.__offset(this.bb_pos, 68);
  return offset ? this.bb.readInt64(this.bb.__vector(this.bb_pos + offset) + index * 8) : this.bb.createLong(0, 0);
};

/**
 * @returns {number}
 */
MyGame.Example.Monster.prototype.vectorOfLongsLength = function() {
  var offset = this.bb.__offset(this.bb_pos, 68);
  return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
};

/**
 * @param {number} index
 * @returns {number}
 */
MyGame.Example.Monster.prototype.vectorOfDoubles = function(index) {
  var offset = this.bb.__offset(this.bb_pos, 70);
  return offset ? this.bb.readFloat64(this.bb.__vector(this.bb_pos + offset) + index * 8) : 0;
};

/**
 * @returns {number}
 */
MyGame.Example.Monster.prototype.vectorOfDoublesLength = function() {
  var offset = this.bb.__offset(this.bb_pos, 70);
  return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
};

/**
 * @returns {Float64Array}
 */
MyGame.Example.Monster.prototype.vectorOfDoublesArray = function() {
  var offset = this.bb.__offset(this.bb_pos, 70);
  return offset ? new Float64Array(this.bb.bytes().buffer, this.bb.bytes().byteOffset + this.bb.__vector(this.bb_pos + offset), this.bb.__vector_len(this.bb_pos + offset)) : null;
};

/**
 * @param {MyGame.InParentNamespace=} obj
 * @returns {MyGame.InParentNamespace|null}
 */
MyGame.Example.Monster.prototype.parentNamespaceTest = function(obj) {
  var offset = this.bb.__offset(this.bb_pos, 72);
  return offset ? (obj || new MyGame.InParentNamespace).__init(this.bb.__indirect(this.bb_pos + offset), this.bb) : null;
};

/**
 * @param {number} index
 * @param {MyGame.Example.Referrable=} obj
 * @returns {MyGame.Example.Referrable}
 */
MyGame.Example.Monster.prototype.vectorOfReferrables = function(index, obj) {
  var offset = this.bb.__offset(this.bb_pos, 74);
  return offset ? (obj || new MyGame.Example.Referrable).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos + offset) + index * 4), this.bb) : null;
};

/**
 * @returns {number}
 */
MyGame.Example.Monster.prototype.vectorOfReferrablesLength = function() {
  var offset = this.bb.__offset(this.bb_pos, 74);
  return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
};

/**
 * @returns {flatbuffers.Long}
 */
MyGame.Example.Monster.prototype.singleWeakReference = function() {
  var offset = this.bb.__offset(this.bb_pos, 76);
  return offset ? this.bb.readUint64(this.bb_pos + offset) : this.bb.createLong(0, 0);
};

/**
 * @param {flatbuffers.Long} value
 * @returns {boolean}
 */
MyGame.Example.Monster.prototype.mutate_single_weak_reference = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 76);

  if (offset === 0) {
    return false;
  }

  this.bb.writeUint64(this.bb_pos + offset, value);
  return true;
};

/**
 * @param {number} index
 * @returns {flatbuffers.Long}
 */
MyGame.Example.Monster.prototype.vectorOfWeakReferences = function(index) {
  var offset = this.bb.__offset(this.bb_pos, 78);
  return offset ? this.bb.readUint64(this.bb.__vector(this.bb_pos + offset) + index * 8) : this.bb.createLong(0, 0);
};

/**
 * @returns {number}
 */
MyGame.Example.Monster.prototype.vectorOfWeakReferencesLength = function() {
  var offset = this.bb.__offset(this.bb_pos, 78);
  return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
};

/**
 * @param {number} index
 * @param {MyGame.Example.Referrable=} obj
 * @returns {MyGame.Example.Referrable}
 */
MyGame.Example.Monster.prototype.vectorOfStrongReferrables = function(index, obj) {
  var offset = this.bb.__offset(this.bb_pos, 80);
  return offset ? (obj || new MyGame.Example.Referrable).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos + offset) + index * 4), this.bb) : null;
};

/**
 * @returns {number}
 */
MyGame.Example.Monster.prototype.vectorOfStrongReferrablesLength = function() {
  var offset = this.bb.__offset(this.bb_pos, 80);
  return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
};

/**
 * @returns {flatbuffers.Long}
 */
MyGame.Example.Monster.prototype.coOwningReference = function() {
  var offset = this.bb.__offset(this.bb_pos, 82);
  return offset ? this.bb.readUint64(this.bb_pos + offset) : this.bb.createLong(0, 0);
};

/**
 * @param {flatbuffers.Long} value
 * @returns {boolean}
 */
MyGame.Example.Monster.prototype.mutate_co_owning_reference = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 82);

  if (offset === 0) {
    return false;
  }

  this.bb.writeUint64(this.bb_pos + offset, value);
  return true;
};

/**
 * @param {number} index
 * @returns {flatbuffers.Long}
 */
MyGame.Example.Monster.prototype.vectorOfCoOwningReferences = function(index) {
  var offset = this.bb.__offset(this.bb_pos, 84);
  return offset ? this.bb.readUint64(this.bb.__vector(this.bb_pos + offset) + index * 8) : this.bb.createLong(0, 0);
};

/**
 * @returns {number}
 */
MyGame.Example.Monster.prototype.vectorOfCoOwningReferencesLength = function() {
  var offset = this.bb.__offset(this.bb_pos, 84);
  return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
};

/**
 * @returns {flatbuffers.Long}
 */
MyGame.Example.Monster.prototype.nonOwningReference = function() {
  var offset = this.bb.__offset(this.bb_pos, 86);
  return offset ? this.bb.readUint64(this.bb_pos + offset) : this.bb.createLong(0, 0);
};

/**
 * @param {flatbuffers.Long} value
 * @returns {boolean}
 */
MyGame.Example.Monster.prototype.mutate_non_owning_reference = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 86);

  if (offset === 0) {
    return false;
  }

  this.bb.writeUint64(this.bb_pos + offset, value);
  return true;
};

/**
 * @param {number} index
 * @returns {flatbuffers.Long}
 */
MyGame.Example.Monster.prototype.vectorOfNonOwningReferences = function(index) {
  var offset = this.bb.__offset(this.bb_pos, 88);
  return offset ? this.bb.readUint64(this.bb.__vector(this.bb_pos + offset) + index * 8) : this.bb.createLong(0, 0);
};

/**
 * @returns {number}
 */
MyGame.Example.Monster.prototype.vectorOfNonOwningReferencesLength = function() {
  var offset = this.bb.__offset(this.bb_pos, 88);
  return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
};

/**
 * @returns {MyGame.Example.AnyUniqueAliases}
 */
MyGame.Example.Monster.prototype.anyUniqueType = function() {
  var offset = this.bb.__offset(this.bb_pos, 90);
  return offset ? /** @type {MyGame.Example.AnyUniqueAliases} */ (this.bb.readUint8(this.bb_pos + offset)) : MyGame.Example.AnyUniqueAliases.NONE;
};

/**
 * @param {MyGame.Example.AnyUniqueAliases} value
 * @returns {boolean}
 */
MyGame.Example.Monster.prototype.mutate_any_unique_type = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 90);

  if (offset === 0) {
    return false;
  }

  this.bb.writeUint8(this.bb_pos + offset, value);
  return true;
};

/**
 * @param {flatbuffers.Table} obj
 * @returns {?flatbuffers.Table}
 */
MyGame.Example.Monster.prototype.anyUnique = function(obj) {
  var offset = this.bb.__offset(this.bb_pos, 92);
  return offset ? this.bb.__union(obj, this.bb_pos + offset) : null;
};

/**
 * @returns {MyGame.Example.AnyAmbiguousAliases}
 */
MyGame.Example.Monster.prototype.anyAmbiguousType = function() {
  var offset = this.bb.__offset(this.bb_pos, 94);
  return offset ? /** @type {MyGame.Example.AnyAmbiguousAliases} */ (this.bb.readUint8(this.bb_pos + offset)) : MyGame.Example.AnyAmbiguousAliases.NONE;
};

/**
 * @param {MyGame.Example.AnyAmbiguousAliases} value
 * @returns {boolean}
 */
MyGame.Example.Monster.prototype.mutate_any_ambiguous_type = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 94);

  if (offset === 0) {
    return false;
  }

  this.bb.writeUint8(this.bb_pos + offset, value);
  return true;
};

/**
 * @param {flatbuffers.Table} obj
 * @returns {?flatbuffers.Table}
 */
MyGame.Example.Monster.prototype.anyAmbiguous = function(obj) {
  var offset = this.bb.__offset(this.bb_pos, 96);
  return offset ? this.bb.__union(obj, this.bb_pos + offset) : null;
};

/**
 * @param {number} index
 * @returns {MyGame.Example.Color}
 */
MyGame.Example.Monster.prototype.vectorOfEnums = function(index) {
  var offset = this.bb.__offset(this.bb_pos, 98);
  return offset ? /** @type {MyGame.Example.Color} */ (this.bb.readInt8(this.bb.__vector(this.bb_pos + offset) + index)) : /** @type {MyGame.Example.Color} */ (0);
};

/**
 * @returns {number}
 */
MyGame.Example.Monster.prototype.vectorOfEnumsLength = function() {
  var offset = this.bb.__offset(this.bb_pos, 98);
  return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
};

/**
 * @returns {Int8Array}
 */
MyGame.Example.Monster.prototype.vectorOfEnumsArray = function() {
  var offset = this.bb.__offset(this.bb_pos, 98);
  return offset ? new Int8Array(this.bb.bytes().buffer, this.bb.bytes().byteOffset + this.bb.__vector(this.bb_pos + offset), this.bb.__vector_len(this.bb_pos + offset)) : null;
};

/**
 * @param {flatbuffers.Builder} builder
 */
MyGame.Example.Monster.startMonster = function(builder) {
  builder.startObject(48);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} posOffset
 */
MyGame.Example.Monster.addPos = function(builder, posOffset) {
  builder.addFieldStruct(0, posOffset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} mana
 */
MyGame.Example.Monster.addMana = function(builder, mana) {
  builder.addFieldInt16(1, mana, 150);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} hp
 */
MyGame.Example.Monster.addHp = function(builder, hp) {
  builder.addFieldInt16(2, hp, 100);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} nameOffset
 */
MyGame.Example.Monster.addName = function(builder, nameOffset) {
  builder.addFieldOffset(3, nameOffset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} inventoryOffset
 */
MyGame.Example.Monster.addInventory = function(builder, inventoryOffset) {
  builder.addFieldOffset(5, inventoryOffset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {Array.<number>} data
 * @returns {flatbuffers.Offset}
 */
MyGame.Example.Monster.createInventoryVector = function(builder, data) {
  builder.startVector(1, data.length, 1);
  for (var i = data.length - 1; i >= 0; i--) {
    builder.addInt8(data[i]);
  }
  return builder.endVector();
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} numElems
 */
MyGame.Example.Monster.startInventoryVector = function(builder, numElems) {
  builder.startVector(1, numElems, 1);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {MyGame.Example.Color} color
 */
MyGame.Example.Monster.addColor = function(builder, color) {
  builder.addFieldInt8(6, color, MyGame.Example.Color.Blue);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {MyGame.Example.Any} testType
 */
MyGame.Example.Monster.addTestType = function(builder, testType) {
  builder.addFieldInt8(7, testType, MyGame.Example.Any.NONE);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} testOffset
 */
MyGame.Example.Monster.addTest = function(builder, testOffset) {
  builder.addFieldOffset(8, testOffset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} test4Offset
 */
MyGame.Example.Monster.addTest4 = function(builder, test4Offset) {
  builder.addFieldOffset(9, test4Offset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} numElems
 */
MyGame.Example.Monster.startTest4Vector = function(builder, numElems) {
  builder.startVector(4, numElems, 2);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} testarrayofstringOffset
 */
MyGame.Example.Monster.addTestarrayofstring = function(builder, testarrayofstringOffset) {
  builder.addFieldOffset(10, testarrayofstringOffset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {Array.<flatbuffers.Offset>} data
 * @returns {flatbuffers.Offset}
 */
MyGame.Example.Monster.createTestarrayofstringVector = function(builder, data) {
  builder.startVector(4, data.length, 4);
  for (var i = data.length - 1; i >= 0; i--) {
    builder.addOffset(data[i]);
  }
  return builder.endVector();
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} numElems
 */
MyGame.Example.Monster.startTestarrayofstringVector = function(builder, numElems) {
  builder.startVector(4, numElems, 4);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} testarrayoftablesOffset
 */
MyGame.Example.Monster.addTestarrayoftables = function(builder, testarrayoftablesOffset) {
  builder.addFieldOffset(11, testarrayoftablesOffset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {Array.<flatbuffers.Offset>} data
 * @returns {flatbuffers.Offset}
 */
MyGame.Example.Monster.createTestarrayoftablesVector = function(builder, data) {
  builder.startVector(4, data.length, 4);
  for (var i = data.length - 1; i >= 0; i--) {
    builder.addOffset(data[i]);
  }
  return builder.endVector();
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} numElems
 */
MyGame.Example.Monster.startTestarrayoftablesVector = function(builder, numElems) {
  builder.startVector(4, numElems, 4);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} enemyOffset
 */
MyGame.Example.Monster.addEnemy = function(builder, enemyOffset) {
  builder.addFieldOffset(12, enemyOffset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} testnestedflatbufferOffset
 */
MyGame.Example.Monster.addTestnestedflatbuffer = function(builder, testnestedflatbufferOffset) {
  builder.addFieldOffset(13, testnestedflatbufferOffset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {Array.<number>} data
 * @returns {flatbuffers.Offset}
 */
MyGame.Example.Monster.createTestnestedflatbufferVector = function(builder, data) {
  builder.startVector(1, data.length, 1);
  for (var i = data.length - 1; i >= 0; i--) {
    builder.addInt8(data[i]);
  }
  return builder.endVector();
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} numElems
 */
MyGame.Example.Monster.startTestnestedflatbufferVector = function(builder, numElems) {
  builder.startVector(1, numElems, 1);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} testemptyOffset
 */
MyGame.Example.Monster.addTestempty = function(builder, testemptyOffset) {
  builder.addFieldOffset(14, testemptyOffset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {boolean} testbool
 */
MyGame.Example.Monster.addTestbool = function(builder, testbool) {
  builder.addFieldInt8(15, +testbool, +false);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} testhashs32Fnv1
 */
MyGame.Example.Monster.addTesthashs32Fnv1 = function(builder, testhashs32Fnv1) {
  builder.addFieldInt32(16, testhashs32Fnv1, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} testhashu32Fnv1
 */
MyGame.Example.Monster.addTesthashu32Fnv1 = function(builder, testhashu32Fnv1) {
  builder.addFieldInt32(17, testhashu32Fnv1, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Long} testhashs64Fnv1
 */
MyGame.Example.Monster.addTesthashs64Fnv1 = function(builder, testhashs64Fnv1) {
  builder.addFieldInt64(18, testhashs64Fnv1, builder.createLong(0, 0));
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Long} testhashu64Fnv1
 */
MyGame.Example.Monster.addTesthashu64Fnv1 = function(builder, testhashu64Fnv1) {
  builder.addFieldInt64(19, testhashu64Fnv1, builder.createLong(0, 0));
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} testhashs32Fnv1a
 */
MyGame.Example.Monster.addTesthashs32Fnv1a = function(builder, testhashs32Fnv1a) {
  builder.addFieldInt32(20, testhashs32Fnv1a, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} testhashu32Fnv1a
 */
MyGame.Example.Monster.addTesthashu32Fnv1a = function(builder, testhashu32Fnv1a) {
  builder.addFieldInt32(21, testhashu32Fnv1a, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Long} testhashs64Fnv1a
 */
MyGame.Example.Monster.addTesthashs64Fnv1a = function(builder, testhashs64Fnv1a) {
  builder.addFieldInt64(22, testhashs64Fnv1a, builder.createLong(0, 0));
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Long} testhashu64Fnv1a
 */
MyGame.Example.Monster.addTesthashu64Fnv1a = function(builder, testhashu64Fnv1a) {
  builder.addFieldInt64(23, testhashu64Fnv1a, builder.createLong(0, 0));
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} testarrayofboolsOffset
 */
MyGame.Example.Monster.addTestarrayofbools = function(builder, testarrayofboolsOffset) {
  builder.addFieldOffset(24, testarrayofboolsOffset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {Array.<boolean>} data
 * @returns {flatbuffers.Offset}
 */
MyGame.Example.Monster.createTestarrayofboolsVector = function(builder, data) {
  builder.startVector(1, data.length, 1);
  for (var i = data.length - 1; i >= 0; i--) {
    builder.addInt8(+data[i]);
  }
  return builder.endVector();
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} numElems
 */
MyGame.Example.Monster.startTestarrayofboolsVector = function(builder, numElems) {
  builder.startVector(1, numElems, 1);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} testf
 */
MyGame.Example.Monster.addTestf = function(builder, testf) {
  builder.addFieldFloat32(25, testf, 3.14159);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} testf2
 */
MyGame.Example.Monster.addTestf2 = function(builder, testf2) {
  builder.addFieldFloat32(26, testf2, 3.0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} testf3
 */
MyGame.Example.Monster.addTestf3 = function(builder, testf3) {
  builder.addFieldFloat32(27, testf3, 0.0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} testarrayofstring2Offset
 */
MyGame.Example.Monster.addTestarrayofstring2 = function(builder, testarrayofstring2Offset) {
  builder.addFieldOffset(28, testarrayofstring2Offset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {Array.<flatbuffers.Offset>} data
 * @returns {flatbuffers.Offset}
 */
MyGame.Example.Monster.createTestarrayofstring2Vector = function(builder, data) {
  builder.startVector(4, data.length, 4);
  for (var i = data.length - 1; i >= 0; i--) {
    builder.addOffset(data[i]);
  }
  return builder.endVector();
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} numElems
 */
MyGame.Example.Monster.startTestarrayofstring2Vector = function(builder, numElems) {
  builder.startVector(4, numElems, 4);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} testarrayofsortedstructOffset
 */
MyGame.Example.Monster.addTestarrayofsortedstruct = function(builder, testarrayofsortedstructOffset) {
  builder.addFieldOffset(29, testarrayofsortedstructOffset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} numElems
 */
MyGame.Example.Monster.startTestarrayofsortedstructVector = function(builder, numElems) {
  builder.startVector(8, numElems, 4);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} flexOffset
 */
MyGame.Example.Monster.addFlex = function(builder, flexOffset) {
  builder.addFieldOffset(30, flexOffset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {Array.<number>} data
 * @returns {flatbuffers.Offset}
 */
MyGame.Example.Monster.createFlexVector = function(builder, data) {
  builder.startVector(1, data.length, 1);
  for (var i = data.length - 1; i >= 0; i--) {
    builder.addInt8(data[i]);
  }
  return builder.endVector();
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} numElems
 */
MyGame.Example.Monster.startFlexVector = function(builder, numElems) {
  builder.startVector(1, numElems, 1);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} test5Offset
 */
MyGame.Example.Monster.addTest5 = function(builder, test5Offset) {
  builder.addFieldOffset(31, test5Offset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} numElems
 */
MyGame.Example.Monster.startTest5Vector = function(builder, numElems) {
  builder.startVector(4, numElems, 2);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} vectorOfLongsOffset
 */
MyGame.Example.Monster.addVectorOfLongs = function(builder, vectorOfLongsOffset) {
  builder.addFieldOffset(32, vectorOfLongsOffset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {Array.<flatbuffers.Long>} data
 * @returns {flatbuffers.Offset}
 */
MyGame.Example.Monster.createVectorOfLongsVector = function(builder, data) {
  builder.startVector(8, data.length, 8);
  for (var i = data.length - 1; i >= 0; i--) {
    builder.addInt64(data[i]);
  }
  return builder.endVector();
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} numElems
 */
MyGame.Example.Monster.startVectorOfLongsVector = function(builder, numElems) {
  builder.startVector(8, numElems, 8);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} vectorOfDoublesOffset
 */
MyGame.Example.Monster.addVectorOfDoubles = function(builder, vectorOfDoublesOffset) {
  builder.addFieldOffset(33, vectorOfDoublesOffset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {Array.<number>} data
 * @returns {flatbuffers.Offset}
 */
MyGame.Example.Monster.createVectorOfDoublesVector = function(builder, data) {
  builder.startVector(8, data.length, 8);
  for (var i = data.length - 1; i >= 0; i--) {
    builder.addFloat64(data[i]);
  }
  return builder.endVector();
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} numElems
 */
MyGame.Example.Monster.startVectorOfDoublesVector = function(builder, numElems) {
  builder.startVector(8, numElems, 8);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} parentNamespaceTestOffset
 */
MyGame.Example.Monster.addParentNamespaceTest = function(builder, parentNamespaceTestOffset) {
  builder.addFieldOffset(34, parentNamespaceTestOffset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} vectorOfReferrablesOffset
 */
MyGame.Example.Monster.addVectorOfReferrables = function(builder, vectorOfReferrablesOffset) {
  builder.addFieldOffset(35, vectorOfReferrablesOffset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {Array.<flatbuffers.Offset>} data
 * @returns {flatbuffers.Offset}
 */
MyGame.Example.Monster.createVectorOfReferrablesVector = function(builder, data) {
  builder.startVector(4, data.length, 4);
  for (var i = data.length - 1; i >= 0; i--) {
    builder.addOffset(data[i]);
  }
  return builder.endVector();
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} numElems
 */
MyGame.Example.Monster.startVectorOfReferrablesVector = function(builder, numElems) {
  builder.startVector(4, numElems, 4);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Long} singleWeakReference
 */
MyGame.Example.Monster.addSingleWeakReference = function(builder, singleWeakReference) {
  builder.addFieldInt64(36, singleWeakReference, builder.createLong(0, 0));
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} vectorOfWeakReferencesOffset
 */
MyGame.Example.Monster.addVectorOfWeakReferences = function(builder, vectorOfWeakReferencesOffset) {
  builder.addFieldOffset(37, vectorOfWeakReferencesOffset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {Array.<flatbuffers.Long>} data
 * @returns {flatbuffers.Offset}
 */
MyGame.Example.Monster.createVectorOfWeakReferencesVector = function(builder, data) {
  builder.startVector(8, data.length, 8);
  for (var i = data.length - 1; i >= 0; i--) {
    builder.addInt64(data[i]);
  }
  return builder.endVector();
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} numElems
 */
MyGame.Example.Monster.startVectorOfWeakReferencesVector = function(builder, numElems) {
  builder.startVector(8, numElems, 8);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} vectorOfStrongReferrablesOffset
 */
MyGame.Example.Monster.addVectorOfStrongReferrables = function(builder, vectorOfStrongReferrablesOffset) {
  builder.addFieldOffset(38, vectorOfStrongReferrablesOffset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {Array.<flatbuffers.Offset>} data
 * @returns {flatbuffers.Offset}
 */
MyGame.Example.Monster.createVectorOfStrongReferrablesVector = function(builder, data) {
  builder.startVector(4, data.length, 4);
  for (var i = data.length - 1; i >= 0; i--) {
    builder.addOffset(data[i]);
  }
  return builder.endVector();
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} numElems
 */
MyGame.Example.Monster.startVectorOfStrongReferrablesVector = function(builder, numElems) {
  builder.startVector(4, numElems, 4);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Long} coOwningReference
 */
MyGame.Example.Monster.addCoOwningReference = function(builder, coOwningReference) {
  builder.addFieldInt64(39, coOwningReference, builder.createLong(0, 0));
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} vectorOfCoOwningReferencesOffset
 */
MyGame.Example.Monster.addVectorOfCoOwningReferences = function(builder, vectorOfCoOwningReferencesOffset) {
  builder.addFieldOffset(40, vectorOfCoOwningReferencesOffset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {Array.<flatbuffers.Long>} data
 * @returns {flatbuffers.Offset}
 */
MyGame.Example.Monster.createVectorOfCoOwningReferencesVector = function(builder, data) {
  builder.startVector(8, data.length, 8);
  for (var i = data.length - 1; i >= 0; i--) {
    builder.addInt64(data[i]);
  }
  return builder.endVector();
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} numElems
 */
MyGame.Example.Monster.startVectorOfCoOwningReferencesVector = function(builder, numElems) {
  builder.startVector(8, numElems, 8);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Long} nonOwningReference
 */
MyGame.Example.Monster.addNonOwningReference = function(builder, nonOwningReference) {
  builder.addFieldInt64(41, nonOwningReference, builder.createLong(0, 0));
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} vectorOfNonOwningReferencesOffset
 */
MyGame.Example.Monster.addVectorOfNonOwningReferences = function(builder, vectorOfNonOwningReferencesOffset) {
  builder.addFieldOffset(42, vectorOfNonOwningReferencesOffset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {Array.<flatbuffers.Long>} data
 * @returns {flatbuffers.Offset}
 */
MyGame.Example.Monster.createVectorOfNonOwningReferencesVector = function(builder, data) {
  builder.startVector(8, data.length, 8);
  for (var i = data.length - 1; i >= 0; i--) {
    builder.addInt64(data[i]);
  }
  return builder.endVector();
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} numElems
 */
MyGame.Example.Monster.startVectorOfNonOwningReferencesVector = function(builder, numElems) {
  builder.startVector(8, numElems, 8);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {MyGame.Example.AnyUniqueAliases} anyUniqueType
 */
MyGame.Example.Monster.addAnyUniqueType = function(builder, anyUniqueType) {
  builder.addFieldInt8(43, anyUniqueType, MyGame.Example.AnyUniqueAliases.NONE);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} anyUniqueOffset
 */
MyGame.Example.Monster.addAnyUnique = function(builder, anyUniqueOffset) {
  builder.addFieldOffset(44, anyUniqueOffset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {MyGame.Example.AnyAmbiguousAliases} anyAmbiguousType
 */
MyGame.Example.Monster.addAnyAmbiguousType = function(builder, anyAmbiguousType) {
  builder.addFieldInt8(45, anyAmbiguousType, MyGame.Example.AnyAmbiguousAliases.NONE);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} anyAmbiguousOffset
 */
MyGame.Example.Monster.addAnyAmbiguous = function(builder, anyAmbiguousOffset) {
  builder.addFieldOffset(46, anyAmbiguousOffset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} vectorOfEnumsOffset
 */
MyGame.Example.Monster.addVectorOfEnums = function(builder, vectorOfEnumsOffset) {
  builder.addFieldOffset(47, vectorOfEnumsOffset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {Array.<MyGame.Example.Color>} data
 * @returns {flatbuffers.Offset}
 */
MyGame.Example.Monster.createVectorOfEnumsVector = function(builder, data) {
  builder.startVector(1, data.length, 1);
  for (var i = data.length - 1; i >= 0; i--) {
    builder.addInt8(data[i]);
  }
  return builder.endVector();
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} numElems
 */
MyGame.Example.Monster.startVectorOfEnumsVector = function(builder, numElems) {
  builder.startVector(1, numElems, 1);
};

/**
 * @param {flatbuffers.Builder} builder
 * @returns {flatbuffers.Offset}
 */
MyGame.Example.Monster.endMonster = function(builder) {
  var offset = builder.endObject();
  builder.requiredField(offset, 10); // name
  return offset;
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} offset
 */
MyGame.Example.Monster.finishMonsterBuffer = function(builder, offset) {
  builder.finish(offset, 'MONS');
};

/**
 * @constructor
 */
MyGame.Example.TypeAliases = function() {
  /**
   * @type {flatbuffers.ByteBuffer}
   */
  this.bb = null;

  /**
   * @type {number}
   */
  this.bb_pos = 0;
};

/**
 * @param {number} i
 * @param {flatbuffers.ByteBuffer} bb
 * @returns {MyGame.Example.TypeAliases}
 */
MyGame.Example.TypeAliases.prototype.__init = function(i, bb) {
  this.bb_pos = i;
  this.bb = bb;
  return this;
};

/**
 * @param {flatbuffers.ByteBuffer} bb
 * @param {MyGame.Example.TypeAliases=} obj
 * @returns {MyGame.Example.TypeAliases}
 */
MyGame.Example.TypeAliases.getRootAsTypeAliases = function(bb, obj) {
  return (obj || new MyGame.Example.TypeAliases).__init(bb.readInt32(bb.position()) + bb.position(), bb);
};

/**
 * @returns {number}
 */
MyGame.Example.TypeAliases.prototype.i8 = function() {
  var offset = this.bb.__offset(this.bb_pos, 4);
  return offset ? this.bb.readInt8(this.bb_pos + offset) : 0;
};

/**
 * @param {number} value
 * @returns {boolean}
 */
MyGame.Example.TypeAliases.prototype.mutate_i8 = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 4);

  if (offset === 0) {
    return false;
  }

  this.bb.writeInt8(this.bb_pos + offset, value);
  return true;
};

/**
 * @returns {number}
 */
MyGame.Example.TypeAliases.prototype.u8 = function() {
  var offset = this.bb.__offset(this.bb_pos, 6);
  return offset ? this.bb.readUint8(this.bb_pos + offset) : 0;
};

/**
 * @param {number} value
 * @returns {boolean}
 */
MyGame.Example.TypeAliases.prototype.mutate_u8 = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 6);

  if (offset === 0) {
    return false;
  }

  this.bb.writeUint8(this.bb_pos + offset, value);
  return true;
};

/**
 * @returns {number}
 */
MyGame.Example.TypeAliases.prototype.i16 = function() {
  var offset = this.bb.__offset(this.bb_pos, 8);
  return offset ? this.bb.readInt16(this.bb_pos + offset) : 0;
};

/**
 * @param {number} value
 * @returns {boolean}
 */
MyGame.Example.TypeAliases.prototype.mutate_i16 = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 8);

  if (offset === 0) {
    return false;
  }

  this.bb.writeInt16(this.bb_pos + offset, value);
  return true;
};

/**
 * @returns {number}
 */
MyGame.Example.TypeAliases.prototype.u16 = function() {
  var offset = this.bb.__offset(this.bb_pos, 10);
  return offset ? this.bb.readUint16(this.bb_pos + offset) : 0;
};

/**
 * @param {number} value
 * @returns {boolean}
 */
MyGame.Example.TypeAliases.prototype.mutate_u16 = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 10);

  if (offset === 0) {
    return false;
  }

  this.bb.writeUint16(this.bb_pos + offset, value);
  return true;
};

/**
 * @returns {number}
 */
MyGame.Example.TypeAliases.prototype.i32 = function() {
  var offset = this.bb.__offset(this.bb_pos, 12);
  return offset ? this.bb.readInt32(this.bb_pos + offset) : 0;
};

/**
 * @param {number} value
 * @returns {boolean}
 */
MyGame.Example.TypeAliases.prototype.mutate_i32 = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 12);

  if (offset === 0) {
    return false;
  }

  this.bb.writeInt32(this.bb_pos + offset, value);
  return true;
};

/**
 * @returns {number}
 */
MyGame.Example.TypeAliases.prototype.u32 = function() {
  var offset = this.bb.__offset(this.bb_pos, 14);
  return offset ? this.bb.readUint32(this.bb_pos + offset) : 0;
};

/**
 * @param {number} value
 * @returns {boolean}
 */
MyGame.Example.TypeAliases.prototype.mutate_u32 = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 14);

  if (offset === 0) {
    return false;
  }

  this.bb.writeUint32(this.bb_pos + offset, value);
  return true;
};

/**
 * @returns {flatbuffers.Long}
 */
MyGame.Example.TypeAliases.prototype.i64 = function() {
  var offset = this.bb.__offset(this.bb_pos, 16);
  return offset ? this.bb.readInt64(this.bb_pos + offset) : this.bb.createLong(0, 0);
};

/**
 * @param {flatbuffers.Long} value
 * @returns {boolean}
 */
MyGame.Example.TypeAliases.prototype.mutate_i64 = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 16);

  if (offset === 0) {
    return false;
  }

  this.bb.writeInt64(this.bb_pos + offset, value);
  return true;
};

/**
 * @returns {flatbuffers.Long}
 */
MyGame.Example.TypeAliases.prototype.u64 = function() {
  var offset = this.bb.__offset(this.bb_pos, 18);
  return offset ? this.bb.readUint64(this.bb_pos + offset) : this.bb.createLong(0, 0);
};

/**
 * @param {flatbuffers.Long} value
 * @returns {boolean}
 */
MyGame.Example.TypeAliases.prototype.mutate_u64 = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 18);

  if (offset === 0) {
    return false;
  }

  this.bb.writeUint64(this.bb_pos + offset, value);
  return true;
};

/**
 * @returns {number}
 */
MyGame.Example.TypeAliases.prototype.f32 = function() {
  var offset = this.bb.__offset(this.bb_pos, 20);
  return offset ? this.bb.readFloat32(this.bb_pos + offset) : 0.0;
};

/**
 * @param {number} value
 * @returns {boolean}
 */
MyGame.Example.TypeAliases.prototype.mutate_f32 = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 20);

  if (offset === 0) {
    return false;
  }

  this.bb.writeFloat32(this.bb_pos + offset, value);
  return true;
};

/**
 * @returns {number}
 */
MyGame.Example.TypeAliases.prototype.f64 = function() {
  var offset = this.bb.__offset(this.bb_pos, 22);
  return offset ? this.bb.readFloat64(this.bb_pos + offset) : 0.0;
};

/**
 * @param {number} value
 * @returns {boolean}
 */
MyGame.Example.TypeAliases.prototype.mutate_f64 = function(value) {
  var offset = this.bb.__offset(this.bb_pos, 22);

  if (offset === 0) {
    return false;
  }

  this.bb.writeFloat64(this.bb_pos + offset, value);
  return true;
};

/**
 * @param {number} index
 * @returns {number}
 */
MyGame.Example.TypeAliases.prototype.v8 = function(index) {
  var offset = this.bb.__offset(this.bb_pos, 24);
  return offset ? this.bb.readInt8(this.bb.__vector(this.bb_pos + offset) + index) : 0;
};

/**
 * @returns {number}
 */
MyGame.Example.TypeAliases.prototype.v8Length = function() {
  var offset = this.bb.__offset(this.bb_pos, 24);
  return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
};

/**
 * @returns {Int8Array}
 */
MyGame.Example.TypeAliases.prototype.v8Array = function() {
  var offset = this.bb.__offset(this.bb_pos, 24);
  return offset ? new Int8Array(this.bb.bytes().buffer, this.bb.bytes().byteOffset + this.bb.__vector(this.bb_pos + offset), this.bb.__vector_len(this.bb_pos + offset)) : null;
};

/**
 * @param {number} index
 * @returns {number}
 */
MyGame.Example.TypeAliases.prototype.vf64 = function(index) {
  var offset = this.bb.__offset(this.bb_pos, 26);
  return offset ? this.bb.readFloat64(this.bb.__vector(this.bb_pos + offset) + index * 8) : 0;
};

/**
 * @returns {number}
 */
MyGame.Example.TypeAliases.prototype.vf64Length = function() {
  var offset = this.bb.__offset(this.bb_pos, 26);
  return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
};

/**
 * @returns {Float64Array}
 */
MyGame.Example.TypeAliases.prototype.vf64Array = function() {
  var offset = this.bb.__offset(this.bb_pos, 26);
  return offset ? new Float64Array(this.bb.bytes().buffer, this.bb.bytes().byteOffset + this.bb.__vector(this.bb_pos + offset), this.bb.__vector_len(this.bb_pos + offset)) : null;
};

/**
 * @param {flatbuffers.Builder} builder
 */
MyGame.Example.TypeAliases.startTypeAliases = function(builder) {
  builder.startObject(12);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} i8
 */
MyGame.Example.TypeAliases.addI8 = function(builder, i8) {
  builder.addFieldInt8(0, i8, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} u8
 */
MyGame.Example.TypeAliases.addU8 = function(builder, u8) {
  builder.addFieldInt8(1, u8, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} i16
 */
MyGame.Example.TypeAliases.addI16 = function(builder, i16) {
  builder.addFieldInt16(2, i16, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} u16
 */
MyGame.Example.TypeAliases.addU16 = function(builder, u16) {
  builder.addFieldInt16(3, u16, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} i32
 */
MyGame.Example.TypeAliases.addI32 = function(builder, i32) {
  builder.addFieldInt32(4, i32, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} u32
 */
MyGame.Example.TypeAliases.addU32 = function(builder, u32) {
  builder.addFieldInt32(5, u32, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Long} i64
 */
MyGame.Example.TypeAliases.addI64 = function(builder, i64) {
  builder.addFieldInt64(6, i64, builder.createLong(0, 0));
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Long} u64
 */
MyGame.Example.TypeAliases.addU64 = function(builder, u64) {
  builder.addFieldInt64(7, u64, builder.createLong(0, 0));
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} f32
 */
MyGame.Example.TypeAliases.addF32 = function(builder, f32) {
  builder.addFieldFloat32(8, f32, 0.0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} f64
 */
MyGame.Example.TypeAliases.addF64 = function(builder, f64) {
  builder.addFieldFloat64(9, f64, 0.0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} v8Offset
 */
MyGame.Example.TypeAliases.addV8 = function(builder, v8Offset) {
  builder.addFieldOffset(10, v8Offset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {Array.<number>} data
 * @returns {flatbuffers.Offset}
 */
MyGame.Example.TypeAliases.createV8Vector = function(builder, data) {
  builder.startVector(1, data.length, 1);
  for (var i = data.length - 1; i >= 0; i--) {
    builder.addInt8(data[i]);
  }
  return builder.endVector();
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} numElems
 */
MyGame.Example.TypeAliases.startV8Vector = function(builder, numElems) {
  builder.startVector(1, numElems, 1);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {flatbuffers.Offset} vf64Offset
 */
MyGame.Example.TypeAliases.addVf64 = function(builder, vf64Offset) {
  builder.addFieldOffset(11, vf64Offset, 0);
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {Array.<number>} data
 * @returns {flatbuffers.Offset}
 */
MyGame.Example.TypeAliases.createVf64Vector = function(builder, data) {
  builder.startVector(8, data.length, 8);
  for (var i = data.length - 1; i >= 0; i--) {
    builder.addFloat64(data[i]);
  }
  return builder.endVector();
};

/**
 * @param {flatbuffers.Builder} builder
 * @param {number} numElems
 */
MyGame.Example.TypeAliases.startVf64Vector = function(builder, numElems) {
  builder.startVector(8, numElems, 8);
};

/**
 * @param {flatbuffers.Builder} builder
 * @returns {flatbuffers.Offset}
 */
MyGame.Example.TypeAliases.endTypeAliases = function(builder) {
  var offset = builder.endObject();
  return offset;
};

// Exports for Node.js and RequireJS
this.MyGame = MyGame;
