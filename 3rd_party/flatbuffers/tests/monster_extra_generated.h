// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_MONSTEREXTRA_MYGAME_H_
#define FLATBUFFERS_GENERATED_MONSTEREXTRA_MYGAME_H_

#include "flatbuffers/flatbuffers.h"

namespace MyGame {

struct MonsterExra;
struct MonsterExraT;

bool operator==(const MonsterExraT &lhs, const MonsterExraT &rhs);

inline const flatbuffers::TypeTable *MonsterExraTypeTable();

struct MonsterExraT : public flatbuffers::NativeTable {
  typedef MonsterExra TableType;
  float testf_nan;
  float testf_pinf;
  float testf_ninf;
  double testd_nan;
  double testd_pinf;
  double testd_ninf;
  MonsterExraT()
      : testf_nan(std::numeric_limits<float>::quiet_NaN()),
        testf_pinf(std::numeric_limits<float>::infinity()),
        testf_ninf(-std::numeric_limits<float>::infinity()),
        testd_nan(std::numeric_limits<double>::quiet_NaN()),
        testd_pinf(std::numeric_limits<double>::infinity()),
        testd_ninf(-std::numeric_limits<double>::infinity()) {
  }
};

inline bool operator==(const MonsterExraT &lhs, const MonsterExraT &rhs) {
  return
      (lhs.testf_nan == rhs.testf_nan) &&
      (lhs.testf_pinf == rhs.testf_pinf) &&
      (lhs.testf_ninf == rhs.testf_ninf) &&
      (lhs.testd_nan == rhs.testd_nan) &&
      (lhs.testd_pinf == rhs.testd_pinf) &&
      (lhs.testd_ninf == rhs.testd_ninf);
}

struct MonsterExra FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef MonsterExraT NativeTableType;
  static const flatbuffers::TypeTable *MiniReflectTypeTable() {
    return MonsterExraTypeTable();
  }
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TESTF_NAN = 4,
    VT_TESTF_PINF = 6,
    VT_TESTF_NINF = 8,
    VT_TESTD_NAN = 10,
    VT_TESTD_PINF = 12,
    VT_TESTD_NINF = 14
  };
  float testf_nan() const {
    return GetField<float>(VT_TESTF_NAN, std::numeric_limits<float>::quiet_NaN());
  }
  bool mutate_testf_nan(float _testf_nan) {
    return SetField<float>(VT_TESTF_NAN, _testf_nan, std::numeric_limits<float>::quiet_NaN());
  }
  float testf_pinf() const {
    return GetField<float>(VT_TESTF_PINF, std::numeric_limits<float>::infinity());
  }
  bool mutate_testf_pinf(float _testf_pinf) {
    return SetField<float>(VT_TESTF_PINF, _testf_pinf, std::numeric_limits<float>::infinity());
  }
  float testf_ninf() const {
    return GetField<float>(VT_TESTF_NINF, -std::numeric_limits<float>::infinity());
  }
  bool mutate_testf_ninf(float _testf_ninf) {
    return SetField<float>(VT_TESTF_NINF, _testf_ninf, -std::numeric_limits<float>::infinity());
  }
  double testd_nan() const {
    return GetField<double>(VT_TESTD_NAN, std::numeric_limits<double>::quiet_NaN());
  }
  bool mutate_testd_nan(double _testd_nan) {
    return SetField<double>(VT_TESTD_NAN, _testd_nan, std::numeric_limits<double>::quiet_NaN());
  }
  double testd_pinf() const {
    return GetField<double>(VT_TESTD_PINF, std::numeric_limits<double>::infinity());
  }
  bool mutate_testd_pinf(double _testd_pinf) {
    return SetField<double>(VT_TESTD_PINF, _testd_pinf, std::numeric_limits<double>::infinity());
  }
  double testd_ninf() const {
    return GetField<double>(VT_TESTD_NINF, -std::numeric_limits<double>::infinity());
  }
  bool mutate_testd_ninf(double _testd_ninf) {
    return SetField<double>(VT_TESTD_NINF, _testd_ninf, -std::numeric_limits<double>::infinity());
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<float>(verifier, VT_TESTF_NAN) &&
           VerifyField<float>(verifier, VT_TESTF_PINF) &&
           VerifyField<float>(verifier, VT_TESTF_NINF) &&
           VerifyField<double>(verifier, VT_TESTD_NAN) &&
           VerifyField<double>(verifier, VT_TESTD_PINF) &&
           VerifyField<double>(verifier, VT_TESTD_NINF) &&
           verifier.EndTable();
  }
  MonsterExraT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(MonsterExraT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<MonsterExra> Pack(flatbuffers::FlatBufferBuilder &_fbb, const MonsterExraT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct MonsterExraBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_testf_nan(float testf_nan) {
    fbb_.AddElement<float>(MonsterExra::VT_TESTF_NAN, testf_nan, std::numeric_limits<float>::quiet_NaN());
  }
  void add_testf_pinf(float testf_pinf) {
    fbb_.AddElement<float>(MonsterExra::VT_TESTF_PINF, testf_pinf, std::numeric_limits<float>::infinity());
  }
  void add_testf_ninf(float testf_ninf) {
    fbb_.AddElement<float>(MonsterExra::VT_TESTF_NINF, testf_ninf, -std::numeric_limits<float>::infinity());
  }
  void add_testd_nan(double testd_nan) {
    fbb_.AddElement<double>(MonsterExra::VT_TESTD_NAN, testd_nan, std::numeric_limits<double>::quiet_NaN());
  }
  void add_testd_pinf(double testd_pinf) {
    fbb_.AddElement<double>(MonsterExra::VT_TESTD_PINF, testd_pinf, std::numeric_limits<double>::infinity());
  }
  void add_testd_ninf(double testd_ninf) {
    fbb_.AddElement<double>(MonsterExra::VT_TESTD_NINF, testd_ninf, -std::numeric_limits<double>::infinity());
  }
  explicit MonsterExraBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  MonsterExraBuilder &operator=(const MonsterExraBuilder &);
  flatbuffers::Offset<MonsterExra> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<MonsterExra>(end);
    return o;
  }
};

inline flatbuffers::Offset<MonsterExra> CreateMonsterExra(
    flatbuffers::FlatBufferBuilder &_fbb,
    float testf_nan = std::numeric_limits<float>::quiet_NaN(),
    float testf_pinf = std::numeric_limits<float>::infinity(),
    float testf_ninf = -std::numeric_limits<float>::infinity(),
    double testd_nan = std::numeric_limits<double>::quiet_NaN(),
    double testd_pinf = std::numeric_limits<double>::infinity(),
    double testd_ninf = -std::numeric_limits<double>::infinity()) {
  MonsterExraBuilder builder_(_fbb);
  builder_.add_testd_ninf(testd_ninf);
  builder_.add_testd_pinf(testd_pinf);
  builder_.add_testd_nan(testd_nan);
  builder_.add_testf_ninf(testf_ninf);
  builder_.add_testf_pinf(testf_pinf);
  builder_.add_testf_nan(testf_nan);
  return builder_.Finish();
}

flatbuffers::Offset<MonsterExra> CreateMonsterExra(flatbuffers::FlatBufferBuilder &_fbb, const MonsterExraT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

inline MonsterExraT *MonsterExra::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = new MonsterExraT();
  UnPackTo(_o, _resolver);
  return _o;
}

inline void MonsterExra::UnPackTo(MonsterExraT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = testf_nan(); _o->testf_nan = _e; };
  { auto _e = testf_pinf(); _o->testf_pinf = _e; };
  { auto _e = testf_ninf(); _o->testf_ninf = _e; };
  { auto _e = testd_nan(); _o->testd_nan = _e; };
  { auto _e = testd_pinf(); _o->testd_pinf = _e; };
  { auto _e = testd_ninf(); _o->testd_ninf = _e; };
}

inline flatbuffers::Offset<MonsterExra> MonsterExra::Pack(flatbuffers::FlatBufferBuilder &_fbb, const MonsterExraT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateMonsterExra(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<MonsterExra> CreateMonsterExra(flatbuffers::FlatBufferBuilder &_fbb, const MonsterExraT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const MonsterExraT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _testf_nan = _o->testf_nan;
  auto _testf_pinf = _o->testf_pinf;
  auto _testf_ninf = _o->testf_ninf;
  auto _testd_nan = _o->testd_nan;
  auto _testd_pinf = _o->testd_pinf;
  auto _testd_ninf = _o->testd_ninf;
  return MyGame::CreateMonsterExra(
      _fbb,
      _testf_nan,
      _testf_pinf,
      _testf_ninf,
      _testd_nan,
      _testd_pinf,
      _testd_ninf);
}

inline const flatbuffers::TypeTable *MonsterExraTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_FLOAT, 0, -1 },
    { flatbuffers::ET_FLOAT, 0, -1 },
    { flatbuffers::ET_FLOAT, 0, -1 },
    { flatbuffers::ET_DOUBLE, 0, -1 },
    { flatbuffers::ET_DOUBLE, 0, -1 },
    { flatbuffers::ET_DOUBLE, 0, -1 }
  };
  static const char * const names[] = {
    "testf_nan",
    "testf_pinf",
    "testf_ninf",
    "testd_nan",
    "testd_pinf",
    "testd_ninf"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 6, type_codes, nullptr, nullptr, names
  };
  return &tt;
}

}  // namespace MyGame

#endif  // FLATBUFFERS_GENERATED_MONSTEREXTRA_MYGAME_H_
