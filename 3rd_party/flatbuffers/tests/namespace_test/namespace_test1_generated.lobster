// automatically generated by the FlatBuffers compiler, do not modify

include "flatbuffers.lobster"

namespace NamespaceA_NamespaceB

enum + 
    EnumInNestedNS_A = 0,
    EnumInNestedNS_B = 1,
    EnumInNestedNS_C = 2

struct TableInNestedNS

struct StructInNestedNS

struct TableInNestedNS : flatbuffers_handle
    def foo():
        buf_.flatbuffers_field_int32(pos_, 4, 0)

def GetRootAsTableInNestedNS(buf:string): TableInNestedNS { buf, buf.flatbuffers_indirect(0) }

def TableInNestedNSStart(b_:flatbuffers_builder):
    b_.StartObject(1)
def TableInNestedNSAddFoo(b_:flatbuffers_builder, foo:int):
    b_.PrependInt32Slot(0, foo, 0)
def TableInNestedNSEnd(b_:flatbuffers_builder):
    b_.EndObject()

struct StructInNestedNS : flatbuffers_handle
    def a():
        buf_.read_int32_le(pos_ + 0)
    def b():
        buf_.read_int32_le(pos_ + 4)

def CreateStructInNestedNS(b_:flatbuffers_builder, a:int, b:int):
    b_.Prep(4, 8)
    b_.PrependInt32(b)
    b_.PrependInt32(a)
    return b_.Offset()

