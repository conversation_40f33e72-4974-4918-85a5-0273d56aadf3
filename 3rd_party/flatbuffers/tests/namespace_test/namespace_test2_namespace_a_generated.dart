// automatically generated by the FlatBuffers compiler, do not modify
// ignore_for_file: unused_import, unused_field, unused_local_variable

library namespace_a;

import 'dart:typed_data' show Uint8List;
import 'package:flat_buffers/flat_buffers.dart' as fb;

import 'namespace_test1_namespace_a_generated.dart';
import './namespace_test2_namespace_c_generated.dart' as namespace_c;

class TableInFirstNS {
  TableInFirstNS._(this._bc, this._bcOffset);
  factory TableInFirstNS(List<int> bytes) {
    fb.BufferContext rootRef = new fb.BufferContext.fromBytes(bytes);
    return reader.read(rootRef, 0);
  }

  static const fb.Reader<TableInFirstNS> reader = const _TableInFirstNSReader();

  final fb.BufferContext _bc;
  final int _bcOffset;

  namespace_a_namespace_b.TableInNestedNS get fooTable => namespace_a_namespace_b.TableInNestedNS.reader.vTableGet(_bc, _bcOffset, 4, null);
  EnumInNestedNS get fooEnum => new EnumInNestedNS.fromValue(const fb.Int8Reader().vTableGet(_bc, _bcOffset, 6, 0));
  namespace_a_namespace_b.StructInNestedNS get fooStruct => namespace_a_namespace_b.StructInNestedNS.reader.vTableGet(_bc, _bcOffset, 8, null);

  @override
  String toString() {
    return 'TableInFirstNS{fooTable: $fooTable, fooEnum: $fooEnum, fooStruct: $fooStruct}';
  }
}

class _TableInFirstNSReader extends fb.TableReader<TableInFirstNS> {
  const _TableInFirstNSReader();

  @override
  TableInFirstNS createObject(fb.BufferContext bc, int offset) => 
    new TableInFirstNS._(bc, offset);
}

class TableInFirstNSBuilder {
  TableInFirstNSBuilder(this.fbBuilder) {
    assert(fbBuilder != null);
  }

  final fb.Builder fbBuilder;

  void begin() {
    fbBuilder.startTable();
  }

  int addFooTableOffset(int offset) {
    fbBuilder.addOffset(0, offset);
    return fbBuilder.offset;
  }
  int addFooEnum(EnumInNestedNS fooEnum) {
    fbBuilder.addInt8(1, fooEnum?.value);
    return fbBuilder.offset;
  }
  int addFooStruct(int offset) {
    fbBuilder.addStruct(2, offset);
    return fbBuilder.offset;
  }

  int finish() {
    return fbBuilder.endTable();
  }
}

class TableInFirstNSObjectBuilder extends fb.ObjectBuilder {
  final namespace_a_namespace_b.TableInNestedNSObjectBuilder _fooTable;
  final EnumInNestedNS _fooEnum;
  final namespace_a_namespace_b.StructInNestedNSObjectBuilder _fooStruct;

  TableInFirstNSObjectBuilder({
    namespace_a_namespace_b.TableInNestedNSObjectBuilder fooTable,
    EnumInNestedNS fooEnum,
    namespace_a_namespace_b.StructInNestedNSObjectBuilder fooStruct,
  })
      : _fooTable = fooTable,
        _fooEnum = fooEnum,
        _fooStruct = fooStruct;

  /// Finish building, and store into the [fbBuilder].
  @override
  int finish(
    fb.Builder fbBuilder) {
    assert(fbBuilder != null);
    final int fooTableOffset = _fooTable?.getOrCreateOffset(fbBuilder);

    fbBuilder.startTable();
    if (fooTableOffset != null) {
      fbBuilder.addOffset(0, fooTableOffset);
    }
    fbBuilder.addInt8(1, _fooEnum?.value);
    if (_fooStruct != null) {
      fbBuilder.addStruct(2, _fooStruct.finish(fbBuilder));
    }
    return fbBuilder.endTable();
  }

  /// Convenience method to serialize to byte list.
  @override
  Uint8List toBytes([String fileIdentifier]) {
    fb.Builder fbBuilder = new fb.Builder();
    int offset = finish(fbBuilder);
    return fbBuilder.finish(offset, fileIdentifier);
  }
}
class SecondTableInA {
  SecondTableInA._(this._bc, this._bcOffset);
  factory SecondTableInA(List<int> bytes) {
    fb.BufferContext rootRef = new fb.BufferContext.fromBytes(bytes);
    return reader.read(rootRef, 0);
  }

  static const fb.Reader<SecondTableInA> reader = const _SecondTableInAReader();

  final fb.BufferContext _bc;
  final int _bcOffset;

  namespace_c.TableInC get referToC => namespace_c.TableInC.reader.vTableGet(_bc, _bcOffset, 4, null);

  @override
  String toString() {
    return 'SecondTableInA{referToC: $referToC}';
  }
}

class _SecondTableInAReader extends fb.TableReader<SecondTableInA> {
  const _SecondTableInAReader();

  @override
  SecondTableInA createObject(fb.BufferContext bc, int offset) => 
    new SecondTableInA._(bc, offset);
}

class SecondTableInABuilder {
  SecondTableInABuilder(this.fbBuilder) {
    assert(fbBuilder != null);
  }

  final fb.Builder fbBuilder;

  void begin() {
    fbBuilder.startTable();
  }

  int addReferToCOffset(int offset) {
    fbBuilder.addOffset(0, offset);
    return fbBuilder.offset;
  }

  int finish() {
    return fbBuilder.endTable();
  }
}

class SecondTableInAObjectBuilder extends fb.ObjectBuilder {
  final namespace_c.TableInCObjectBuilder _referToC;

  SecondTableInAObjectBuilder({
    namespace_c.TableInCObjectBuilder referToC,
  })
      : _referToC = referToC;

  /// Finish building, and store into the [fbBuilder].
  @override
  int finish(
    fb.Builder fbBuilder) {
    assert(fbBuilder != null);
    final int referToCOffset = _referToC?.getOrCreateOffset(fbBuilder);

    fbBuilder.startTable();
    if (referToCOffset != null) {
      fbBuilder.addOffset(0, referToCOffset);
    }
    return fbBuilder.endTable();
  }

  /// Convenience method to serialize to byte list.
  @override
  Uint8List toBytes([String fileIdentifier]) {
    fb.Builder fbBuilder = new fb.Builder();
    int offset = finish(fbBuilder);
    return fbBuilder.finish(offset, fileIdentifier);
  }
}
