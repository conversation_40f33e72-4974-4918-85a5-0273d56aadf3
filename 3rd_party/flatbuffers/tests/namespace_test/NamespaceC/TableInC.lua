-- automatically generated by the FlatBuffers compiler, do not modify

-- namespace: NamespaceC

local flatbuffers = require('flatbuffers')

local TableInC = {} -- the module
local TableInC_mt = {} -- the class metatable

function TableInC.New()
    local o = {}
    setmetatable(o, {__index = TableInC_mt})
    return o
end
function TableInC.GetRootAsTableInC(buf, offset)
    local n = flatbuffers.N.UOffsetT:Unpack(buf, offset)
    local o = TableInC.New()
    o:Init(buf, n + offset)
    return o
end
function TableInC_mt:Init(buf, pos)
    self.view = flatbuffers.view.New(buf, pos)
end
function TableInC_mt:ReferToA1()
    local o = self.view:Offset(4)
    if o ~= 0 then
        local x = self.view:Indirect(o + self.view.pos)
        local obj = require('NamespaceA.TableInFirstNS').New()
        obj:Init(self.view.bytes, x)
        return obj
    end
end
function TableInC_mt:ReferToA2()
    local o = self.view:Offset(6)
    if o ~= 0 then
        local x = self.view:Indirect(o + self.view.pos)
        local obj = require('NamespaceA.SecondTableInA').New()
        obj:Init(self.view.bytes, x)
        return obj
    end
end
function TableInC.Start(builder) builder:StartObject(2) end
function TableInC.AddReferToA1(builder, referToA1) builder:PrependUOffsetTRelativeSlot(0, referToA1, 0) end
function TableInC.AddReferToA2(builder, referToA2) builder:PrependUOffsetTRelativeSlot(1, referToA2, 0) end
function TableInC.End(builder) return builder:EndObject() end

return TableInC -- return the module