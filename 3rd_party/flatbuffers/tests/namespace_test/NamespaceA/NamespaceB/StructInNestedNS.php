<?php
// automatically generated by the FlatBuffers compiler, do not modify

namespace NamespaceA\NamespaceB;

use \Google\FlatBuffers\Struct;
use \Google\FlatBuffers\Table;
use \Google\FlatBuffers\ByteBuffer;
use \Google\FlatBuffers\FlatBufferBuilder;

class StructInNestedNS extends Struct
{
    /**
     * @param int $_i offset
     * @param ByteBuffer $_bb
     * @return StructInNestedNS
     **/
    public function init($_i, ByteBuffer $_bb)
    {
        $this->bb_pos = $_i;
        $this->bb = $_bb;
        return $this;
    }

    /**
     * @return int
     */
    public function GetA()
    {
        return $this->bb->getInt($this->bb_pos + 0);
    }

    /**
     * @return int
     */
    public function GetB()
    {
        return $this->bb->getInt($this->bb_pos + 4);
    }


    /**
     * @return int offset
     */
    public static function createStructInNestedNS(FlatBufferBuilder $builder, $a, $b)
    {
        $builder->prep(4, 8);
        $builder->putInt($b);
        $builder->putInt($a);
        return $builder->offset();
    }
}
