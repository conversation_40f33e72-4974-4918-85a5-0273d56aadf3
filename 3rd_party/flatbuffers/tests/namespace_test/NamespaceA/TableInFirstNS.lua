-- automatically generated by the FlatBuffers compiler, do not modify

-- namespace: NamespaceA

local flatbuffers = require('flatbuffers')

local TableInFirstNS = {} -- the module
local TableInFirstNS_mt = {} -- the class metatable

function TableInFirstNS.New()
    local o = {}
    setmetatable(o, {__index = TableInFirstNS_mt})
    return o
end
function TableInFirstNS.GetRootAsTableInFirstNS(buf, offset)
    local n = flatbuffers.N.UOffsetT:Unpack(buf, offset)
    local o = TableInFirstNS.New()
    o:Init(buf, n + offset)
    return o
end
function TableInFirstNS_mt:Init(buf, pos)
    self.view = flatbuffers.view.New(buf, pos)
end
function TableInFirstNS_mt:FooTable()
    local o = self.view:Offset(4)
    if o ~= 0 then
        local x = self.view:Indirect(o + self.view.pos)
        local obj = require('NamespaceA.NamespaceB.TableInNestedNS').New()
        obj:Init(self.view.bytes, x)
        return obj
    end
end
function TableInFirstNS_mt:FooEnum()
    local o = self.view:Offset(6)
    if o ~= 0 then
        return self.view:Get(flatbuffers.N.Int8, o + self.view.pos)
    end
    return 0
end
function TableInFirstNS_mt:FooStruct()
    local o = self.view:Offset(8)
    if o ~= 0 then
        local x = o + self.view.pos
        local obj = require('NamespaceA.NamespaceB.StructInNestedNS').New()
        obj:Init(self.view.bytes, x)
        return obj
    end
end
function TableInFirstNS.Start(builder) builder:StartObject(3) end
function TableInFirstNS.AddFooTable(builder, fooTable) builder:PrependUOffsetTRelativeSlot(0, fooTable, 0) end
function TableInFirstNS.AddFooEnum(builder, fooEnum) builder:PrependInt8Slot(1, fooEnum, 0) end
function TableInFirstNS.AddFooStruct(builder, fooStruct) builder:PrependStructSlot(2, fooStruct, 0) end
function TableInFirstNS.End(builder) return builder:EndObject() end

return TableInFirstNS -- return the module