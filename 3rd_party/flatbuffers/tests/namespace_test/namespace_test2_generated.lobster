// automatically generated by the FlatBuffers compiler, do not modify

include "flatbuffers.lobster"

namespace NamespaceA

struct TableInFirstNS

namespace NamespaceC

struct TableInC

namespace NamespaceA

struct SecondTableInA

struct TableInFirstNS : flatbuffers_handle
    def foo_table():
        o := buf_.flatbuffers_field_table(pos_, 4)
        if o: NamespaceA_NamespaceB_TableInNestedNS { buf_, o } else: nil
    def foo_enum():
        buf_.flatbuffers_field_int8(pos_, 6, 0)
    def foo_struct():
        o := buf_.flatbuffers_field_struct(pos_, 8)
        if o: NamespaceA_NamespaceB_StructInNestedNS { buf_, o } else: nil

def GetRootAsTableInFirstNS(buf:string): TableInFirstNS { buf, buf.flatbuffers_indirect(0) }

def TableInFirstNSStart(b_:flatbuffers_builder):
    b_.StartObject(3)
def TableInFirstNSAddFooTable(b_:flatbuffers_builder, foo_table:int):
    b_.PrependUOffsetTRelativeSlot(0, foo_table, 0)
def TableInFirstNSAddFooEnum(b_:flatbuffers_builder, foo_enum:int):
    b_.PrependInt8Slot(1, foo_enum, 0)
def TableInFirstNSAddFooStruct(b_:flatbuffers_builder, foo_struct:int):
    b_.PrependStructSlot(2, foo_struct, 0)
def TableInFirstNSEnd(b_:flatbuffers_builder):
    b_.EndObject()

namespace NamespaceC

struct TableInC : flatbuffers_handle
    def refer_to_a1():
        o := buf_.flatbuffers_field_table(pos_, 4)
        if o: NamespaceA_TableInFirstNS { buf_, o } else: nil
    def refer_to_a2():
        o := buf_.flatbuffers_field_table(pos_, 6)
        if o: NamespaceA_SecondTableInA { buf_, o } else: nil

def GetRootAsTableInC(buf:string): TableInC { buf, buf.flatbuffers_indirect(0) }

def TableInCStart(b_:flatbuffers_builder):
    b_.StartObject(2)
def TableInCAddReferToA1(b_:flatbuffers_builder, refer_to_a1:int):
    b_.PrependUOffsetTRelativeSlot(0, refer_to_a1, 0)
def TableInCAddReferToA2(b_:flatbuffers_builder, refer_to_a2:int):
    b_.PrependUOffsetTRelativeSlot(1, refer_to_a2, 0)
def TableInCEnd(b_:flatbuffers_builder):
    b_.EndObject()

namespace NamespaceA

struct SecondTableInA : flatbuffers_handle
    def refer_to_c():
        o := buf_.flatbuffers_field_table(pos_, 4)
        if o: NamespaceC_TableInC { buf_, o } else: nil

def GetRootAsSecondTableInA(buf:string): SecondTableInA { buf, buf.flatbuffers_indirect(0) }

def SecondTableInAStart(b_:flatbuffers_builder):
    b_.StartObject(1)
def SecondTableInAAddReferToC(b_:flatbuffers_builder, refer_to_c:int):
    b_.PrependUOffsetTRelativeSlot(0, refer_to_c, 0)
def SecondTableInAEnd(b_:flatbuffers_builder):
    b_.EndObject()

