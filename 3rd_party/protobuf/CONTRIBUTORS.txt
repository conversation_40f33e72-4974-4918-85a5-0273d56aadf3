This file contains a list of people who have made large contributions
to the public version of Protocol Buffers.

Original Protocol Buffers design and implementation:
  <PERSON><PERSON> <<EMAIL>>
  <PERSON> <<EMAIL>>
  <PERSON> <<EMAIL>>
  <PERSON> <<EMAIL>>
  <PERSON> <<EMAIL>>
  (and many others)

Proto2 C++ and Java primary author:
  <PERSON><PERSON> <<EMAIL>>

Proto2 Python primary authors:
  <PERSON> <robin<PERSON>@google.com>
  <PERSON><PERSON> <<EMAIL>>

Java Nano primary authors:
  <PERSON> <<EMAIL>>
  <PERSON> <<EMAIL>>
  <PERSON> <<EMAIL>>
  <PERSON><PERSON> <<EMAIL>>

Large code contributions:
  <PERSON> <<EMAIL>>
  <PERSON> <<EMAIL>>
  <PERSON><PERSON> <<EMAIL>>

Large quantity of code reviews:
  <PERSON> <<EMAIL>>
  <PERSON> <n<PERSON><PERSON>@google.com>
  <PERSON> <j<PERSON><PERSON>@google.com>
  <PERSON> <<EMAIL>>

Documentation:
  <PERSON> <l<PERSON>@google.com>

Maven packaging:
  <PERSON> <<EMAIL>>

Patch contributors:
  <PERSON> <<EMAIL>>
    * Small patch to handle trailing slashes in --proto_path flag.
  Johan Euphrosine <<EMAIL>>
    * Small patch to fix Python CallMethod().
  Ulrich Kunitz <<EMAIL>>
    * Small optimizations to Python serialization.
  Leandro Lucarella <<EMAIL>>
    * VI syntax highlighting tweaks.
    * Fix compiler to not make output executable.
  Dilip Joseph <<EMAIL>>
    * Heuristic detection of sub-messages when printing unknown fields in
      text format.
  Brian Atkinson <<EMAIL>>
    * Added @Override annotation to generated Java code where appropriate.
  Vincent Choinière <<EMAIL>>
    * Tru64 support.
  Monty Taylor <<EMAIL>>
    * Solaris 10 + Sun Studio fixes.
  Alek Storm <<EMAIL>>
    * Slicing support for repeated scalar fields for the Python API.
  Oleg Smolsky <<EMAIL>>
    * MS Visual Studio error format option.
    * Detect unordered_map in stl_hash.m4.
  Brian Olson <<EMAIL>>
    * gzip/zlib I/O support.
  Michael Poole <<EMAIL>>
    * Fixed warnings about generated constructors not explicitly initializing
      all fields (only present with certain compiler settings).
    * Added generation of field number constants.
  Wink Saville <<EMAIL>>
    * Fixed initialization ordering problem in logging code.
  Will Pierce <<EMAIL>>
    * Small patch improving performance of in Python serialization.
  Alexandre Vassalotti <<EMAIL>>
    * Emacs mode for Protocol Buffers (editors/protobuf-mode.el).
  Scott Stafford <<EMAIL>>
    * Added Swap(), SwapElements(), and RemoveLast() to Reflection interface.
  Alexander Melnikov <<EMAIL>>
    * HPUX support.
  Oliver Jowett <<EMAIL>>
    * Detect whether zlib is new enough in configure script.
    * Fixes for Solaris 10 32/64-bit confusion.
  Evan Jones <<EMAIL>>
    * Optimize Java serialization code when writing a small message to a stream.
    * Optimize Java serialization of strings so that UTF-8 encoding happens only
      once per string per serialization call.
    * Clean up some Java warnings.
    * Fix bug with permanent callbacks that delete themselves when run.
  Michael Kucharski <<EMAIL>>
    * Added CodedInputStream.getTotalBytesRead().
  Kacper Kowalik <<EMAIL>>
    * Fixed m4/acx_pthread.m4 problem for some Linux distributions.
  William Orr <<EMAIL>>
    * Fixed detection of sched_yield on Solaris.
    * Added atomicops for Solaris
  Andrew Paprocki <<EMAIL>>
    * Fixed minor IBM xlC compiler build issues
    * Added atomicops for AIX (POWER)
