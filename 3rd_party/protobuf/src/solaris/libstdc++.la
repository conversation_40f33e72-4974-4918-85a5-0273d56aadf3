# libstdc++.la - a libtool library file
# Generated by ltmain.sh - GNU libtool 1.4a-GCC3.0 (1.641.2.256 2001/05/28 20:09:07 with GCC-local changes)
#
# Please DO NOT delete this file!
# It is necessary for linking the library.

# ---
# NOTE: This file lives in /usr/sfw/lib on Solaris 10.  Unfortunately,
# due to an apparent bug in the Solaris 10 6/06 release,
# /usr/sfw/lib/libstdc++.la is empty.  Below is the correct content,
# according to
#    http://forum.java.sun.com/thread.jspa?threadID=5073150
# By passing LDFLAGS='-Lsrc/solaris' to configure, make will pick up
# this copy of the file rather than the empty copy in /usr/sfw/lib.
#
# Also see
#   http://www.technicalarticles.org/index.php/Compiling_MySQL_5.0_on_Solaris_10
#
# Note: this is for 32-bit systems.  If you have a 64-bit system,
# uncomment the appropriate dependency_libs line below.
# ----

# The name that we can dlopen(3).
dlname='libstdc++.so.6'

# Names of this library.
library_names='libstdc++.so.6.0.3 libstdc++.so.6 libstdc++.so'

# The name of the static archive.
old_library='libstdc++.a'

# Libraries that this one depends upon.
# 32-bit version:
dependency_libs='-lc -lm -L/usr/sfw/lib -lgcc_s'
# 64-bit version:
#dependency_libs='-L/lib/64 -lc -lm -L/usr/sfw/lib/64 -lgcc_s'

# Version information for libstdc++.
current=6
age=0
revision=3

# Is this an already installed library?
installed=yes

# Files to dlopen/dlpreopen
dlopen=''
dlpreopen=''

# Directory that this library needs to be installed in:
libdir='/usr/sfw/lib'
