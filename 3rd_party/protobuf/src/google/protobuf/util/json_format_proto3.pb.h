// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/util/json_format_proto3.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019000 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/any.pb.h>
#include <google/protobuf/duration.pb.h>
#include <google/protobuf/field_mask.pb.h>
#include <google/protobuf/struct.pb.h>
#include <google/protobuf/timestamp.pb.h>
#include <google/protobuf/wrappers.pb.h>
#include "google/protobuf/unittest.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[33]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto;
namespace proto3 {
class MessageType;
struct MessageTypeDefaultTypeInternal;
extern MessageTypeDefaultTypeInternal _MessageType_default_instance_;
class TestAny;
struct TestAnyDefaultTypeInternal;
extern TestAnyDefaultTypeInternal _TestAny_default_instance_;
class TestBoolValue;
struct TestBoolValueDefaultTypeInternal;
extern TestBoolValueDefaultTypeInternal _TestBoolValue_default_instance_;
class TestBoolValue_BoolMapEntry_DoNotUse;
struct TestBoolValue_BoolMapEntry_DoNotUseDefaultTypeInternal;
extern TestBoolValue_BoolMapEntry_DoNotUseDefaultTypeInternal _TestBoolValue_BoolMapEntry_DoNotUse_default_instance_;
class TestCustomJsonName;
struct TestCustomJsonNameDefaultTypeInternal;
extern TestCustomJsonNameDefaultTypeInternal _TestCustomJsonName_default_instance_;
class TestDuration;
struct TestDurationDefaultTypeInternal;
extern TestDurationDefaultTypeInternal _TestDuration_default_instance_;
class TestEnumValue;
struct TestEnumValueDefaultTypeInternal;
extern TestEnumValueDefaultTypeInternal _TestEnumValue_default_instance_;
class TestExtensions;
struct TestExtensionsDefaultTypeInternal;
extern TestExtensionsDefaultTypeInternal _TestExtensions_default_instance_;
class TestFieldMask;
struct TestFieldMaskDefaultTypeInternal;
extern TestFieldMaskDefaultTypeInternal _TestFieldMask_default_instance_;
class TestListValue;
struct TestListValueDefaultTypeInternal;
extern TestListValueDefaultTypeInternal _TestListValue_default_instance_;
class TestMap;
struct TestMapDefaultTypeInternal;
extern TestMapDefaultTypeInternal _TestMap_default_instance_;
class TestMap_BoolMapEntry_DoNotUse;
struct TestMap_BoolMapEntry_DoNotUseDefaultTypeInternal;
extern TestMap_BoolMapEntry_DoNotUseDefaultTypeInternal _TestMap_BoolMapEntry_DoNotUse_default_instance_;
class TestMap_Int32MapEntry_DoNotUse;
struct TestMap_Int32MapEntry_DoNotUseDefaultTypeInternal;
extern TestMap_Int32MapEntry_DoNotUseDefaultTypeInternal _TestMap_Int32MapEntry_DoNotUse_default_instance_;
class TestMap_Int64MapEntry_DoNotUse;
struct TestMap_Int64MapEntry_DoNotUseDefaultTypeInternal;
extern TestMap_Int64MapEntry_DoNotUseDefaultTypeInternal _TestMap_Int64MapEntry_DoNotUse_default_instance_;
class TestMap_StringMapEntry_DoNotUse;
struct TestMap_StringMapEntry_DoNotUseDefaultTypeInternal;
extern TestMap_StringMapEntry_DoNotUseDefaultTypeInternal _TestMap_StringMapEntry_DoNotUse_default_instance_;
class TestMap_Uint32MapEntry_DoNotUse;
struct TestMap_Uint32MapEntry_DoNotUseDefaultTypeInternal;
extern TestMap_Uint32MapEntry_DoNotUseDefaultTypeInternal _TestMap_Uint32MapEntry_DoNotUse_default_instance_;
class TestMap_Uint64MapEntry_DoNotUse;
struct TestMap_Uint64MapEntry_DoNotUseDefaultTypeInternal;
extern TestMap_Uint64MapEntry_DoNotUseDefaultTypeInternal _TestMap_Uint64MapEntry_DoNotUse_default_instance_;
class TestMessage;
struct TestMessageDefaultTypeInternal;
extern TestMessageDefaultTypeInternal _TestMessage_default_instance_;
class TestNestedMap;
struct TestNestedMapDefaultTypeInternal;
extern TestNestedMapDefaultTypeInternal _TestNestedMap_default_instance_;
class TestNestedMap_BoolMapEntry_DoNotUse;
struct TestNestedMap_BoolMapEntry_DoNotUseDefaultTypeInternal;
extern TestNestedMap_BoolMapEntry_DoNotUseDefaultTypeInternal _TestNestedMap_BoolMapEntry_DoNotUse_default_instance_;
class TestNestedMap_Int32MapEntry_DoNotUse;
struct TestNestedMap_Int32MapEntry_DoNotUseDefaultTypeInternal;
extern TestNestedMap_Int32MapEntry_DoNotUseDefaultTypeInternal _TestNestedMap_Int32MapEntry_DoNotUse_default_instance_;
class TestNestedMap_Int64MapEntry_DoNotUse;
struct TestNestedMap_Int64MapEntry_DoNotUseDefaultTypeInternal;
extern TestNestedMap_Int64MapEntry_DoNotUseDefaultTypeInternal _TestNestedMap_Int64MapEntry_DoNotUse_default_instance_;
class TestNestedMap_MapMapEntry_DoNotUse;
struct TestNestedMap_MapMapEntry_DoNotUseDefaultTypeInternal;
extern TestNestedMap_MapMapEntry_DoNotUseDefaultTypeInternal _TestNestedMap_MapMapEntry_DoNotUse_default_instance_;
class TestNestedMap_StringMapEntry_DoNotUse;
struct TestNestedMap_StringMapEntry_DoNotUseDefaultTypeInternal;
extern TestNestedMap_StringMapEntry_DoNotUseDefaultTypeInternal _TestNestedMap_StringMapEntry_DoNotUse_default_instance_;
class TestNestedMap_Uint32MapEntry_DoNotUse;
struct TestNestedMap_Uint32MapEntry_DoNotUseDefaultTypeInternal;
extern TestNestedMap_Uint32MapEntry_DoNotUseDefaultTypeInternal _TestNestedMap_Uint32MapEntry_DoNotUse_default_instance_;
class TestNestedMap_Uint64MapEntry_DoNotUse;
struct TestNestedMap_Uint64MapEntry_DoNotUseDefaultTypeInternal;
extern TestNestedMap_Uint64MapEntry_DoNotUseDefaultTypeInternal _TestNestedMap_Uint64MapEntry_DoNotUse_default_instance_;
class TestOneof;
struct TestOneofDefaultTypeInternal;
extern TestOneofDefaultTypeInternal _TestOneof_default_instance_;
class TestStringMap;
struct TestStringMapDefaultTypeInternal;
extern TestStringMapDefaultTypeInternal _TestStringMap_default_instance_;
class TestStringMap_StringMapEntry_DoNotUse;
struct TestStringMap_StringMapEntry_DoNotUseDefaultTypeInternal;
extern TestStringMap_StringMapEntry_DoNotUseDefaultTypeInternal _TestStringMap_StringMapEntry_DoNotUse_default_instance_;
class TestStruct;
struct TestStructDefaultTypeInternal;
extern TestStructDefaultTypeInternal _TestStruct_default_instance_;
class TestTimestamp;
struct TestTimestampDefaultTypeInternal;
extern TestTimestampDefaultTypeInternal _TestTimestamp_default_instance_;
class TestValue;
struct TestValueDefaultTypeInternal;
extern TestValueDefaultTypeInternal _TestValue_default_instance_;
class TestWrapper;
struct TestWrapperDefaultTypeInternal;
extern TestWrapperDefaultTypeInternal _TestWrapper_default_instance_;
}  // namespace proto3
PROTOBUF_NAMESPACE_OPEN
template<> ::proto3::MessageType* Arena::CreateMaybeMessage<::proto3::MessageType>(Arena*);
template<> ::proto3::TestAny* Arena::CreateMaybeMessage<::proto3::TestAny>(Arena*);
template<> ::proto3::TestBoolValue* Arena::CreateMaybeMessage<::proto3::TestBoolValue>(Arena*);
template<> ::proto3::TestBoolValue_BoolMapEntry_DoNotUse* Arena::CreateMaybeMessage<::proto3::TestBoolValue_BoolMapEntry_DoNotUse>(Arena*);
template<> ::proto3::TestCustomJsonName* Arena::CreateMaybeMessage<::proto3::TestCustomJsonName>(Arena*);
template<> ::proto3::TestDuration* Arena::CreateMaybeMessage<::proto3::TestDuration>(Arena*);
template<> ::proto3::TestEnumValue* Arena::CreateMaybeMessage<::proto3::TestEnumValue>(Arena*);
template<> ::proto3::TestExtensions* Arena::CreateMaybeMessage<::proto3::TestExtensions>(Arena*);
template<> ::proto3::TestFieldMask* Arena::CreateMaybeMessage<::proto3::TestFieldMask>(Arena*);
template<> ::proto3::TestListValue* Arena::CreateMaybeMessage<::proto3::TestListValue>(Arena*);
template<> ::proto3::TestMap* Arena::CreateMaybeMessage<::proto3::TestMap>(Arena*);
template<> ::proto3::TestMap_BoolMapEntry_DoNotUse* Arena::CreateMaybeMessage<::proto3::TestMap_BoolMapEntry_DoNotUse>(Arena*);
template<> ::proto3::TestMap_Int32MapEntry_DoNotUse* Arena::CreateMaybeMessage<::proto3::TestMap_Int32MapEntry_DoNotUse>(Arena*);
template<> ::proto3::TestMap_Int64MapEntry_DoNotUse* Arena::CreateMaybeMessage<::proto3::TestMap_Int64MapEntry_DoNotUse>(Arena*);
template<> ::proto3::TestMap_StringMapEntry_DoNotUse* Arena::CreateMaybeMessage<::proto3::TestMap_StringMapEntry_DoNotUse>(Arena*);
template<> ::proto3::TestMap_Uint32MapEntry_DoNotUse* Arena::CreateMaybeMessage<::proto3::TestMap_Uint32MapEntry_DoNotUse>(Arena*);
template<> ::proto3::TestMap_Uint64MapEntry_DoNotUse* Arena::CreateMaybeMessage<::proto3::TestMap_Uint64MapEntry_DoNotUse>(Arena*);
template<> ::proto3::TestMessage* Arena::CreateMaybeMessage<::proto3::TestMessage>(Arena*);
template<> ::proto3::TestNestedMap* Arena::CreateMaybeMessage<::proto3::TestNestedMap>(Arena*);
template<> ::proto3::TestNestedMap_BoolMapEntry_DoNotUse* Arena::CreateMaybeMessage<::proto3::TestNestedMap_BoolMapEntry_DoNotUse>(Arena*);
template<> ::proto3::TestNestedMap_Int32MapEntry_DoNotUse* Arena::CreateMaybeMessage<::proto3::TestNestedMap_Int32MapEntry_DoNotUse>(Arena*);
template<> ::proto3::TestNestedMap_Int64MapEntry_DoNotUse* Arena::CreateMaybeMessage<::proto3::TestNestedMap_Int64MapEntry_DoNotUse>(Arena*);
template<> ::proto3::TestNestedMap_MapMapEntry_DoNotUse* Arena::CreateMaybeMessage<::proto3::TestNestedMap_MapMapEntry_DoNotUse>(Arena*);
template<> ::proto3::TestNestedMap_StringMapEntry_DoNotUse* Arena::CreateMaybeMessage<::proto3::TestNestedMap_StringMapEntry_DoNotUse>(Arena*);
template<> ::proto3::TestNestedMap_Uint32MapEntry_DoNotUse* Arena::CreateMaybeMessage<::proto3::TestNestedMap_Uint32MapEntry_DoNotUse>(Arena*);
template<> ::proto3::TestNestedMap_Uint64MapEntry_DoNotUse* Arena::CreateMaybeMessage<::proto3::TestNestedMap_Uint64MapEntry_DoNotUse>(Arena*);
template<> ::proto3::TestOneof* Arena::CreateMaybeMessage<::proto3::TestOneof>(Arena*);
template<> ::proto3::TestStringMap* Arena::CreateMaybeMessage<::proto3::TestStringMap>(Arena*);
template<> ::proto3::TestStringMap_StringMapEntry_DoNotUse* Arena::CreateMaybeMessage<::proto3::TestStringMap_StringMapEntry_DoNotUse>(Arena*);
template<> ::proto3::TestStruct* Arena::CreateMaybeMessage<::proto3::TestStruct>(Arena*);
template<> ::proto3::TestTimestamp* Arena::CreateMaybeMessage<::proto3::TestTimestamp>(Arena*);
template<> ::proto3::TestValue* Arena::CreateMaybeMessage<::proto3::TestValue>(Arena*);
template<> ::proto3::TestWrapper* Arena::CreateMaybeMessage<::proto3::TestWrapper>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace proto3 {

enum EnumType : int {
  FOO = 0,
  BAR = 1,
  EnumType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  EnumType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool EnumType_IsValid(int value);
constexpr EnumType EnumType_MIN = FOO;
constexpr EnumType EnumType_MAX = BAR;
constexpr int EnumType_ARRAYSIZE = EnumType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* EnumType_descriptor();
template<typename T>
inline const std::string& EnumType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, EnumType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function EnumType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    EnumType_descriptor(), enum_t_value);
}
inline bool EnumType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, EnumType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<EnumType>(
    EnumType_descriptor(), name, value);
}
// ===================================================================

class MessageType final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:proto3.MessageType) */ {
 public:
  inline MessageType() : MessageType(nullptr) {}
  ~MessageType() override;
  explicit constexpr MessageType(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MessageType(const MessageType& from);
  MessageType(MessageType&& from) noexcept
    : MessageType() {
    *this = ::std::move(from);
  }

  inline MessageType& operator=(const MessageType& from) {
    CopyFrom(from);
    return *this;
  }
  inline MessageType& operator=(MessageType&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MessageType& default_instance() {
    return *internal_default_instance();
  }
  static inline const MessageType* internal_default_instance() {
    return reinterpret_cast<const MessageType*>(
               &_MessageType_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(MessageType& a, MessageType& b) {
    a.Swap(&b);
  }
  inline void Swap(MessageType* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MessageType* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MessageType* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MessageType>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MessageType& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MessageType& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MessageType* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "proto3.MessageType";
  }
  protected:
  explicit MessageType(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValueFieldNumber = 1,
  };
  // int32 value = 1;
  void clear_value();
  int32_t value() const;
  void set_value(int32_t value);
  private:
  int32_t _internal_value() const;
  void _internal_set_value(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:proto3.MessageType)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int32_t value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto;
};
// -------------------------------------------------------------------

class TestMessage final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:proto3.TestMessage) */ {
 public:
  inline TestMessage() : TestMessage(nullptr) {}
  ~TestMessage() override;
  explicit constexpr TestMessage(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestMessage(const TestMessage& from);
  TestMessage(TestMessage&& from) noexcept
    : TestMessage() {
    *this = ::std::move(from);
  }

  inline TestMessage& operator=(const TestMessage& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestMessage& operator=(TestMessage&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestMessage& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestMessage* internal_default_instance() {
    return reinterpret_cast<const TestMessage*>(
               &_TestMessage_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(TestMessage& a, TestMessage& b) {
    a.Swap(&b);
  }
  inline void Swap(TestMessage* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestMessage* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestMessage* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestMessage>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestMessage& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestMessage& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestMessage* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "proto3.TestMessage";
  }
  protected:
  explicit TestMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRepeatedBoolValueFieldNumber = 21,
    kRepeatedInt32ValueFieldNumber = 22,
    kRepeatedInt64ValueFieldNumber = 23,
    kRepeatedUint32ValueFieldNumber = 24,
    kRepeatedUint64ValueFieldNumber = 25,
    kRepeatedFloatValueFieldNumber = 26,
    kRepeatedDoubleValueFieldNumber = 27,
    kRepeatedStringValueFieldNumber = 28,
    kRepeatedBytesValueFieldNumber = 29,
    kRepeatedEnumValueFieldNumber = 30,
    kRepeatedMessageValueFieldNumber = 31,
    kStringValueFieldNumber = 8,
    kBytesValueFieldNumber = 9,
    kMessageValueFieldNumber = 11,
    kBoolValueFieldNumber = 1,
    kInt32ValueFieldNumber = 2,
    kInt64ValueFieldNumber = 3,
    kUint64ValueFieldNumber = 5,
    kUint32ValueFieldNumber = 4,
    kFloatValueFieldNumber = 6,
    kDoubleValueFieldNumber = 7,
    kEnumValueFieldNumber = 10,
  };
  // repeated bool repeated_bool_value = 21;
  int repeated_bool_value_size() const;
  private:
  int _internal_repeated_bool_value_size() const;
  public:
  void clear_repeated_bool_value();
  private:
  bool _internal_repeated_bool_value(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
      _internal_repeated_bool_value() const;
  void _internal_add_repeated_bool_value(bool value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
      _internal_mutable_repeated_bool_value();
  public:
  bool repeated_bool_value(int index) const;
  void set_repeated_bool_value(int index, bool value);
  void add_repeated_bool_value(bool value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
      repeated_bool_value() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
      mutable_repeated_bool_value();

  // repeated int32 repeated_int32_value = 22;
  int repeated_int32_value_size() const;
  private:
  int _internal_repeated_int32_value_size() const;
  public:
  void clear_repeated_int32_value();
  private:
  int32_t _internal_repeated_int32_value(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      _internal_repeated_int32_value() const;
  void _internal_add_repeated_int32_value(int32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      _internal_mutable_repeated_int32_value();
  public:
  int32_t repeated_int32_value(int index) const;
  void set_repeated_int32_value(int index, int32_t value);
  void add_repeated_int32_value(int32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      repeated_int32_value() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      mutable_repeated_int32_value();

  // repeated int64 repeated_int64_value = 23;
  int repeated_int64_value_size() const;
  private:
  int _internal_repeated_int64_value_size() const;
  public:
  void clear_repeated_int64_value();
  private:
  int64_t _internal_repeated_int64_value(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_repeated_int64_value() const;
  void _internal_add_repeated_int64_value(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_repeated_int64_value();
  public:
  int64_t repeated_int64_value(int index) const;
  void set_repeated_int64_value(int index, int64_t value);
  void add_repeated_int64_value(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      repeated_int64_value() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_repeated_int64_value();

  // repeated uint32 repeated_uint32_value = 24;
  int repeated_uint32_value_size() const;
  private:
  int _internal_repeated_uint32_value_size() const;
  public:
  void clear_repeated_uint32_value();
  private:
  uint32_t _internal_repeated_uint32_value(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
      _internal_repeated_uint32_value() const;
  void _internal_add_repeated_uint32_value(uint32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
      _internal_mutable_repeated_uint32_value();
  public:
  uint32_t repeated_uint32_value(int index) const;
  void set_repeated_uint32_value(int index, uint32_t value);
  void add_repeated_uint32_value(uint32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
      repeated_uint32_value() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
      mutable_repeated_uint32_value();

  // repeated uint64 repeated_uint64_value = 25;
  int repeated_uint64_value_size() const;
  private:
  int _internal_repeated_uint64_value_size() const;
  public:
  void clear_repeated_uint64_value();
  private:
  uint64_t _internal_repeated_uint64_value(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      _internal_repeated_uint64_value() const;
  void _internal_add_repeated_uint64_value(uint64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      _internal_mutable_repeated_uint64_value();
  public:
  uint64_t repeated_uint64_value(int index) const;
  void set_repeated_uint64_value(int index, uint64_t value);
  void add_repeated_uint64_value(uint64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      repeated_uint64_value() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      mutable_repeated_uint64_value();

  // repeated float repeated_float_value = 26;
  int repeated_float_value_size() const;
  private:
  int _internal_repeated_float_value_size() const;
  public:
  void clear_repeated_float_value();
  private:
  float _internal_repeated_float_value(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      _internal_repeated_float_value() const;
  void _internal_add_repeated_float_value(float value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      _internal_mutable_repeated_float_value();
  public:
  float repeated_float_value(int index) const;
  void set_repeated_float_value(int index, float value);
  void add_repeated_float_value(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      repeated_float_value() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_repeated_float_value();

  // repeated double repeated_double_value = 27;
  int repeated_double_value_size() const;
  private:
  int _internal_repeated_double_value_size() const;
  public:
  void clear_repeated_double_value();
  private:
  double _internal_repeated_double_value(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
      _internal_repeated_double_value() const;
  void _internal_add_repeated_double_value(double value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
      _internal_mutable_repeated_double_value();
  public:
  double repeated_double_value(int index) const;
  void set_repeated_double_value(int index, double value);
  void add_repeated_double_value(double value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
      repeated_double_value() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
      mutable_repeated_double_value();

  // repeated string repeated_string_value = 28;
  int repeated_string_value_size() const;
  private:
  int _internal_repeated_string_value_size() const;
  public:
  void clear_repeated_string_value();
  const std::string& repeated_string_value(int index) const;
  std::string* mutable_repeated_string_value(int index);
  void set_repeated_string_value(int index, const std::string& value);
  void set_repeated_string_value(int index, std::string&& value);
  void set_repeated_string_value(int index, const char* value);
  void set_repeated_string_value(int index, const char* value, size_t size);
  std::string* add_repeated_string_value();
  void add_repeated_string_value(const std::string& value);
  void add_repeated_string_value(std::string&& value);
  void add_repeated_string_value(const char* value);
  void add_repeated_string_value(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& repeated_string_value() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_repeated_string_value();
  private:
  const std::string& _internal_repeated_string_value(int index) const;
  std::string* _internal_add_repeated_string_value();
  public:

  // repeated bytes repeated_bytes_value = 29;
  int repeated_bytes_value_size() const;
  private:
  int _internal_repeated_bytes_value_size() const;
  public:
  void clear_repeated_bytes_value();
  const std::string& repeated_bytes_value(int index) const;
  std::string* mutable_repeated_bytes_value(int index);
  void set_repeated_bytes_value(int index, const std::string& value);
  void set_repeated_bytes_value(int index, std::string&& value);
  void set_repeated_bytes_value(int index, const char* value);
  void set_repeated_bytes_value(int index, const void* value, size_t size);
  std::string* add_repeated_bytes_value();
  void add_repeated_bytes_value(const std::string& value);
  void add_repeated_bytes_value(std::string&& value);
  void add_repeated_bytes_value(const char* value);
  void add_repeated_bytes_value(const void* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& repeated_bytes_value() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_repeated_bytes_value();
  private:
  const std::string& _internal_repeated_bytes_value(int index) const;
  std::string* _internal_add_repeated_bytes_value();
  public:

  // repeated .proto3.EnumType repeated_enum_value = 30;
  int repeated_enum_value_size() const;
  private:
  int _internal_repeated_enum_value_size() const;
  public:
  void clear_repeated_enum_value();
  private:
  ::proto3::EnumType _internal_repeated_enum_value(int index) const;
  void _internal_add_repeated_enum_value(::proto3::EnumType value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>* _internal_mutable_repeated_enum_value();
  public:
  ::proto3::EnumType repeated_enum_value(int index) const;
  void set_repeated_enum_value(int index, ::proto3::EnumType value);
  void add_repeated_enum_value(::proto3::EnumType value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>& repeated_enum_value() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>* mutable_repeated_enum_value();

  // repeated .proto3.MessageType repeated_message_value = 31;
  int repeated_message_value_size() const;
  private:
  int _internal_repeated_message_value_size() const;
  public:
  void clear_repeated_message_value();
  ::proto3::MessageType* mutable_repeated_message_value(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::proto3::MessageType >*
      mutable_repeated_message_value();
  private:
  const ::proto3::MessageType& _internal_repeated_message_value(int index) const;
  ::proto3::MessageType* _internal_add_repeated_message_value();
  public:
  const ::proto3::MessageType& repeated_message_value(int index) const;
  ::proto3::MessageType* add_repeated_message_value();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::proto3::MessageType >&
      repeated_message_value() const;

  // string string_value = 8;
  void clear_string_value();
  const std::string& string_value() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_string_value(ArgT0&& arg0, ArgT... args);
  std::string* mutable_string_value();
  PROTOBUF_NODISCARD std::string* release_string_value();
  void set_allocated_string_value(std::string* string_value);
  private:
  const std::string& _internal_string_value() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_string_value(const std::string& value);
  std::string* _internal_mutable_string_value();
  public:

  // bytes bytes_value = 9;
  void clear_bytes_value();
  const std::string& bytes_value() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_bytes_value(ArgT0&& arg0, ArgT... args);
  std::string* mutable_bytes_value();
  PROTOBUF_NODISCARD std::string* release_bytes_value();
  void set_allocated_bytes_value(std::string* bytes_value);
  private:
  const std::string& _internal_bytes_value() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_bytes_value(const std::string& value);
  std::string* _internal_mutable_bytes_value();
  public:

  // .proto3.MessageType message_value = 11;
  bool has_message_value() const;
  private:
  bool _internal_has_message_value() const;
  public:
  void clear_message_value();
  const ::proto3::MessageType& message_value() const;
  PROTOBUF_NODISCARD ::proto3::MessageType* release_message_value();
  ::proto3::MessageType* mutable_message_value();
  void set_allocated_message_value(::proto3::MessageType* message_value);
  private:
  const ::proto3::MessageType& _internal_message_value() const;
  ::proto3::MessageType* _internal_mutable_message_value();
  public:
  void unsafe_arena_set_allocated_message_value(
      ::proto3::MessageType* message_value);
  ::proto3::MessageType* unsafe_arena_release_message_value();

  // bool bool_value = 1;
  void clear_bool_value();
  bool bool_value() const;
  void set_bool_value(bool value);
  private:
  bool _internal_bool_value() const;
  void _internal_set_bool_value(bool value);
  public:

  // int32 int32_value = 2;
  void clear_int32_value();
  int32_t int32_value() const;
  void set_int32_value(int32_t value);
  private:
  int32_t _internal_int32_value() const;
  void _internal_set_int32_value(int32_t value);
  public:

  // int64 int64_value = 3;
  void clear_int64_value();
  int64_t int64_value() const;
  void set_int64_value(int64_t value);
  private:
  int64_t _internal_int64_value() const;
  void _internal_set_int64_value(int64_t value);
  public:

  // uint64 uint64_value = 5;
  void clear_uint64_value();
  uint64_t uint64_value() const;
  void set_uint64_value(uint64_t value);
  private:
  uint64_t _internal_uint64_value() const;
  void _internal_set_uint64_value(uint64_t value);
  public:

  // uint32 uint32_value = 4;
  void clear_uint32_value();
  uint32_t uint32_value() const;
  void set_uint32_value(uint32_t value);
  private:
  uint32_t _internal_uint32_value() const;
  void _internal_set_uint32_value(uint32_t value);
  public:

  // float float_value = 6;
  void clear_float_value();
  float float_value() const;
  void set_float_value(float value);
  private:
  float _internal_float_value() const;
  void _internal_set_float_value(float value);
  public:

  // double double_value = 7;
  void clear_double_value();
  double double_value() const;
  void set_double_value(double value);
  private:
  double _internal_double_value() const;
  void _internal_set_double_value(double value);
  public:

  // .proto3.EnumType enum_value = 10;
  void clear_enum_value();
  ::proto3::EnumType enum_value() const;
  void set_enum_value(::proto3::EnumType value);
  private:
  ::proto3::EnumType _internal_enum_value() const;
  void _internal_set_enum_value(::proto3::EnumType value);
  public:

  // @@protoc_insertion_point(class_scope:proto3.TestMessage)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool > repeated_bool_value_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t > repeated_int32_value_;
  mutable std::atomic<int> _repeated_int32_value_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > repeated_int64_value_;
  mutable std::atomic<int> _repeated_int64_value_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t > repeated_uint32_value_;
  mutable std::atomic<int> _repeated_uint32_value_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t > repeated_uint64_value_;
  mutable std::atomic<int> _repeated_uint64_value_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > repeated_float_value_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double > repeated_double_value_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> repeated_string_value_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> repeated_bytes_value_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int> repeated_enum_value_;
  mutable std::atomic<int> _repeated_enum_value_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::proto3::MessageType > repeated_message_value_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr string_value_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr bytes_value_;
  ::proto3::MessageType* message_value_;
  bool bool_value_;
  int32_t int32_value_;
  int64_t int64_value_;
  uint64_t uint64_value_;
  uint32_t uint32_value_;
  float float_value_;
  double double_value_;
  int enum_value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto;
};
// -------------------------------------------------------------------

class TestOneof final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:proto3.TestOneof) */ {
 public:
  inline TestOneof() : TestOneof(nullptr) {}
  ~TestOneof() override;
  explicit constexpr TestOneof(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestOneof(const TestOneof& from);
  TestOneof(TestOneof&& from) noexcept
    : TestOneof() {
    *this = ::std::move(from);
  }

  inline TestOneof& operator=(const TestOneof& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestOneof& operator=(TestOneof&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestOneof& default_instance() {
    return *internal_default_instance();
  }
  enum OneofValueCase {
    kOneofInt32Value = 1,
    kOneofStringValue = 2,
    kOneofBytesValue = 3,
    kOneofEnumValue = 4,
    kOneofMessageValue = 5,
    kOneofNullValue = 6,
    ONEOF_VALUE_NOT_SET = 0,
  };

  static inline const TestOneof* internal_default_instance() {
    return reinterpret_cast<const TestOneof*>(
               &_TestOneof_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(TestOneof& a, TestOneof& b) {
    a.Swap(&b);
  }
  inline void Swap(TestOneof* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestOneof* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestOneof* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestOneof>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestOneof& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestOneof& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestOneof* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "proto3.TestOneof";
  }
  protected:
  explicit TestOneof(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOneofInt32ValueFieldNumber = 1,
    kOneofStringValueFieldNumber = 2,
    kOneofBytesValueFieldNumber = 3,
    kOneofEnumValueFieldNumber = 4,
    kOneofMessageValueFieldNumber = 5,
    kOneofNullValueFieldNumber = 6,
  };
  // int32 oneof_int32_value = 1;
  bool has_oneof_int32_value() const;
  private:
  bool _internal_has_oneof_int32_value() const;
  public:
  void clear_oneof_int32_value();
  int32_t oneof_int32_value() const;
  void set_oneof_int32_value(int32_t value);
  private:
  int32_t _internal_oneof_int32_value() const;
  void _internal_set_oneof_int32_value(int32_t value);
  public:

  // string oneof_string_value = 2;
  bool has_oneof_string_value() const;
  private:
  bool _internal_has_oneof_string_value() const;
  public:
  void clear_oneof_string_value();
  const std::string& oneof_string_value() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_oneof_string_value(ArgT0&& arg0, ArgT... args);
  std::string* mutable_oneof_string_value();
  PROTOBUF_NODISCARD std::string* release_oneof_string_value();
  void set_allocated_oneof_string_value(std::string* oneof_string_value);
  private:
  const std::string& _internal_oneof_string_value() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_oneof_string_value(const std::string& value);
  std::string* _internal_mutable_oneof_string_value();
  public:

  // bytes oneof_bytes_value = 3;
  bool has_oneof_bytes_value() const;
  private:
  bool _internal_has_oneof_bytes_value() const;
  public:
  void clear_oneof_bytes_value();
  const std::string& oneof_bytes_value() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_oneof_bytes_value(ArgT0&& arg0, ArgT... args);
  std::string* mutable_oneof_bytes_value();
  PROTOBUF_NODISCARD std::string* release_oneof_bytes_value();
  void set_allocated_oneof_bytes_value(std::string* oneof_bytes_value);
  private:
  const std::string& _internal_oneof_bytes_value() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_oneof_bytes_value(const std::string& value);
  std::string* _internal_mutable_oneof_bytes_value();
  public:

  // .proto3.EnumType oneof_enum_value = 4;
  bool has_oneof_enum_value() const;
  private:
  bool _internal_has_oneof_enum_value() const;
  public:
  void clear_oneof_enum_value();
  ::proto3::EnumType oneof_enum_value() const;
  void set_oneof_enum_value(::proto3::EnumType value);
  private:
  ::proto3::EnumType _internal_oneof_enum_value() const;
  void _internal_set_oneof_enum_value(::proto3::EnumType value);
  public:

  // .proto3.MessageType oneof_message_value = 5;
  bool has_oneof_message_value() const;
  private:
  bool _internal_has_oneof_message_value() const;
  public:
  void clear_oneof_message_value();
  const ::proto3::MessageType& oneof_message_value() const;
  PROTOBUF_NODISCARD ::proto3::MessageType* release_oneof_message_value();
  ::proto3::MessageType* mutable_oneof_message_value();
  void set_allocated_oneof_message_value(::proto3::MessageType* oneof_message_value);
  private:
  const ::proto3::MessageType& _internal_oneof_message_value() const;
  ::proto3::MessageType* _internal_mutable_oneof_message_value();
  public:
  void unsafe_arena_set_allocated_oneof_message_value(
      ::proto3::MessageType* oneof_message_value);
  ::proto3::MessageType* unsafe_arena_release_oneof_message_value();

  // .google.protobuf.NullValue oneof_null_value = 6;
  bool has_oneof_null_value() const;
  private:
  bool _internal_has_oneof_null_value() const;
  public:
  void clear_oneof_null_value();
  ::PROTOBUF_NAMESPACE_ID::NullValue oneof_null_value() const;
  void set_oneof_null_value(::PROTOBUF_NAMESPACE_ID::NullValue value);
  private:
  ::PROTOBUF_NAMESPACE_ID::NullValue _internal_oneof_null_value() const;
  void _internal_set_oneof_null_value(::PROTOBUF_NAMESPACE_ID::NullValue value);
  public:

  void clear_oneof_value();
  OneofValueCase oneof_value_case() const;
  // @@protoc_insertion_point(class_scope:proto3.TestOneof)
 private:
  class _Internal;
  void set_has_oneof_int32_value();
  void set_has_oneof_string_value();
  void set_has_oneof_bytes_value();
  void set_has_oneof_enum_value();
  void set_has_oneof_message_value();
  void set_has_oneof_null_value();

  inline bool has_oneof_value() const;
  inline void clear_has_oneof_value();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  union OneofValueUnion {
    constexpr OneofValueUnion() : _constinit_{} {}
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
    int32_t oneof_int32_value_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr oneof_string_value_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr oneof_bytes_value_;
    int oneof_enum_value_;
    ::proto3::MessageType* oneof_message_value_;
    int oneof_null_value_;
  } oneof_value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  uint32_t _oneof_case_[1];

  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto;
};
// -------------------------------------------------------------------

class TestMap_BoolMapEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestMap_BoolMapEntry_DoNotUse, 
    bool, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_BOOL,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestMap_BoolMapEntry_DoNotUse, 
    bool, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_BOOL,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> SuperType;
  TestMap_BoolMapEntry_DoNotUse();
  explicit constexpr TestMap_BoolMapEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit TestMap_BoolMapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const TestMap_BoolMapEntry_DoNotUse& other);
  static const TestMap_BoolMapEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const TestMap_BoolMapEntry_DoNotUse*>(&_TestMap_BoolMapEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class TestMap_Int32MapEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestMap_Int32MapEntry_DoNotUse, 
    int32_t, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestMap_Int32MapEntry_DoNotUse, 
    int32_t, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> SuperType;
  TestMap_Int32MapEntry_DoNotUse();
  explicit constexpr TestMap_Int32MapEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit TestMap_Int32MapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const TestMap_Int32MapEntry_DoNotUse& other);
  static const TestMap_Int32MapEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const TestMap_Int32MapEntry_DoNotUse*>(&_TestMap_Int32MapEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class TestMap_Int64MapEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestMap_Int64MapEntry_DoNotUse, 
    int64_t, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestMap_Int64MapEntry_DoNotUse, 
    int64_t, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> SuperType;
  TestMap_Int64MapEntry_DoNotUse();
  explicit constexpr TestMap_Int64MapEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit TestMap_Int64MapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const TestMap_Int64MapEntry_DoNotUse& other);
  static const TestMap_Int64MapEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const TestMap_Int64MapEntry_DoNotUse*>(&_TestMap_Int64MapEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class TestMap_Uint32MapEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestMap_Uint32MapEntry_DoNotUse, 
    uint32_t, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestMap_Uint32MapEntry_DoNotUse, 
    uint32_t, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> SuperType;
  TestMap_Uint32MapEntry_DoNotUse();
  explicit constexpr TestMap_Uint32MapEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit TestMap_Uint32MapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const TestMap_Uint32MapEntry_DoNotUse& other);
  static const TestMap_Uint32MapEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const TestMap_Uint32MapEntry_DoNotUse*>(&_TestMap_Uint32MapEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class TestMap_Uint64MapEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestMap_Uint64MapEntry_DoNotUse, 
    uint64_t, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestMap_Uint64MapEntry_DoNotUse, 
    uint64_t, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> SuperType;
  TestMap_Uint64MapEntry_DoNotUse();
  explicit constexpr TestMap_Uint64MapEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit TestMap_Uint64MapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const TestMap_Uint64MapEntry_DoNotUse& other);
  static const TestMap_Uint64MapEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const TestMap_Uint64MapEntry_DoNotUse*>(&_TestMap_Uint64MapEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class TestMap_StringMapEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestMap_StringMapEntry_DoNotUse, 
    std::string, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestMap_StringMapEntry_DoNotUse, 
    std::string, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> SuperType;
  TestMap_StringMapEntry_DoNotUse();
  explicit constexpr TestMap_StringMapEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit TestMap_StringMapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const TestMap_StringMapEntry_DoNotUse& other);
  static const TestMap_StringMapEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const TestMap_StringMapEntry_DoNotUse*>(&_TestMap_StringMapEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "proto3.TestMap.StringMapEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class TestMap final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:proto3.TestMap) */ {
 public:
  inline TestMap() : TestMap(nullptr) {}
  ~TestMap() override;
  explicit constexpr TestMap(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestMap(const TestMap& from);
  TestMap(TestMap&& from) noexcept
    : TestMap() {
    *this = ::std::move(from);
  }

  inline TestMap& operator=(const TestMap& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestMap& operator=(TestMap&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestMap& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestMap* internal_default_instance() {
    return reinterpret_cast<const TestMap*>(
               &_TestMap_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(TestMap& a, TestMap& b) {
    a.Swap(&b);
  }
  inline void Swap(TestMap* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestMap* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestMap* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestMap>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestMap& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestMap& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestMap* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "proto3.TestMap";
  }
  protected:
  explicit TestMap(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kBoolMapFieldNumber = 1,
    kInt32MapFieldNumber = 2,
    kInt64MapFieldNumber = 3,
    kUint32MapFieldNumber = 4,
    kUint64MapFieldNumber = 5,
    kStringMapFieldNumber = 6,
  };
  // map<bool, int32> bool_map = 1;
  int bool_map_size() const;
  private:
  int _internal_bool_map_size() const;
  public:
  void clear_bool_map();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >&
      _internal_bool_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >*
      _internal_mutable_bool_map();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >&
      bool_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >*
      mutable_bool_map();

  // map<int32, int32> int32_map = 2;
  int int32_map_size() const;
  private:
  int _internal_int32_map_size() const;
  public:
  void clear_int32_map();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int32_t >&
      _internal_int32_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int32_t >*
      _internal_mutable_int32_map();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int32_t >&
      int32_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int32_t >*
      mutable_int32_map();

  // map<int64, int32> int64_map = 3;
  int int64_map_size() const;
  private:
  int _internal_int64_map_size() const;
  public:
  void clear_int64_map();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >&
      _internal_int64_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >*
      _internal_mutable_int64_map();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >&
      int64_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >*
      mutable_int64_map();

  // map<uint32, int32> uint32_map = 4;
  int uint32_map_size() const;
  private:
  int _internal_uint32_map_size() const;
  public:
  void clear_uint32_map();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, int32_t >&
      _internal_uint32_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, int32_t >*
      _internal_mutable_uint32_map();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, int32_t >&
      uint32_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, int32_t >*
      mutable_uint32_map();

  // map<uint64, int32> uint64_map = 5;
  int uint64_map_size() const;
  private:
  int _internal_uint64_map_size() const;
  public:
  void clear_uint64_map();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, int32_t >&
      _internal_uint64_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, int32_t >*
      _internal_mutable_uint64_map();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, int32_t >&
      uint64_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, int32_t >*
      mutable_uint64_map();

  // map<string, int32> string_map = 6;
  int string_map_size() const;
  private:
  int _internal_string_map_size() const;
  public:
  void clear_string_map();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >&
      _internal_string_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >*
      _internal_mutable_string_map();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >&
      string_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >*
      mutable_string_map();

  // @@protoc_insertion_point(class_scope:proto3.TestMap)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      TestMap_BoolMapEntry_DoNotUse,
      bool, int32_t,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_BOOL,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> bool_map_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      TestMap_Int32MapEntry_DoNotUse,
      int32_t, int32_t,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> int32_map_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      TestMap_Int64MapEntry_DoNotUse,
      int64_t, int32_t,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> int64_map_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      TestMap_Uint32MapEntry_DoNotUse,
      uint32_t, int32_t,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> uint32_map_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      TestMap_Uint64MapEntry_DoNotUse,
      uint64_t, int32_t,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT64,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> uint64_map_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      TestMap_StringMapEntry_DoNotUse,
      std::string, int32_t,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> string_map_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto;
};
// -------------------------------------------------------------------

class TestNestedMap_BoolMapEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestNestedMap_BoolMapEntry_DoNotUse, 
    bool, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_BOOL,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestNestedMap_BoolMapEntry_DoNotUse, 
    bool, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_BOOL,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> SuperType;
  TestNestedMap_BoolMapEntry_DoNotUse();
  explicit constexpr TestNestedMap_BoolMapEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit TestNestedMap_BoolMapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const TestNestedMap_BoolMapEntry_DoNotUse& other);
  static const TestNestedMap_BoolMapEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const TestNestedMap_BoolMapEntry_DoNotUse*>(&_TestNestedMap_BoolMapEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class TestNestedMap_Int32MapEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestNestedMap_Int32MapEntry_DoNotUse, 
    int32_t, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestNestedMap_Int32MapEntry_DoNotUse, 
    int32_t, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> SuperType;
  TestNestedMap_Int32MapEntry_DoNotUse();
  explicit constexpr TestNestedMap_Int32MapEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit TestNestedMap_Int32MapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const TestNestedMap_Int32MapEntry_DoNotUse& other);
  static const TestNestedMap_Int32MapEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const TestNestedMap_Int32MapEntry_DoNotUse*>(&_TestNestedMap_Int32MapEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class TestNestedMap_Int64MapEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestNestedMap_Int64MapEntry_DoNotUse, 
    int64_t, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestNestedMap_Int64MapEntry_DoNotUse, 
    int64_t, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> SuperType;
  TestNestedMap_Int64MapEntry_DoNotUse();
  explicit constexpr TestNestedMap_Int64MapEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit TestNestedMap_Int64MapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const TestNestedMap_Int64MapEntry_DoNotUse& other);
  static const TestNestedMap_Int64MapEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const TestNestedMap_Int64MapEntry_DoNotUse*>(&_TestNestedMap_Int64MapEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class TestNestedMap_Uint32MapEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestNestedMap_Uint32MapEntry_DoNotUse, 
    uint32_t, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestNestedMap_Uint32MapEntry_DoNotUse, 
    uint32_t, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> SuperType;
  TestNestedMap_Uint32MapEntry_DoNotUse();
  explicit constexpr TestNestedMap_Uint32MapEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit TestNestedMap_Uint32MapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const TestNestedMap_Uint32MapEntry_DoNotUse& other);
  static const TestNestedMap_Uint32MapEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const TestNestedMap_Uint32MapEntry_DoNotUse*>(&_TestNestedMap_Uint32MapEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class TestNestedMap_Uint64MapEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestNestedMap_Uint64MapEntry_DoNotUse, 
    uint64_t, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestNestedMap_Uint64MapEntry_DoNotUse, 
    uint64_t, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> SuperType;
  TestNestedMap_Uint64MapEntry_DoNotUse();
  explicit constexpr TestNestedMap_Uint64MapEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit TestNestedMap_Uint64MapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const TestNestedMap_Uint64MapEntry_DoNotUse& other);
  static const TestNestedMap_Uint64MapEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const TestNestedMap_Uint64MapEntry_DoNotUse*>(&_TestNestedMap_Uint64MapEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class TestNestedMap_StringMapEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestNestedMap_StringMapEntry_DoNotUse, 
    std::string, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestNestedMap_StringMapEntry_DoNotUse, 
    std::string, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> SuperType;
  TestNestedMap_StringMapEntry_DoNotUse();
  explicit constexpr TestNestedMap_StringMapEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit TestNestedMap_StringMapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const TestNestedMap_StringMapEntry_DoNotUse& other);
  static const TestNestedMap_StringMapEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const TestNestedMap_StringMapEntry_DoNotUse*>(&_TestNestedMap_StringMapEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "proto3.TestNestedMap.StringMapEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class TestNestedMap_MapMapEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestNestedMap_MapMapEntry_DoNotUse, 
    std::string, ::proto3::TestNestedMap,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestNestedMap_MapMapEntry_DoNotUse, 
    std::string, ::proto3::TestNestedMap,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  TestNestedMap_MapMapEntry_DoNotUse();
  explicit constexpr TestNestedMap_MapMapEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit TestNestedMap_MapMapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const TestNestedMap_MapMapEntry_DoNotUse& other);
  static const TestNestedMap_MapMapEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const TestNestedMap_MapMapEntry_DoNotUse*>(&_TestNestedMap_MapMapEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "proto3.TestNestedMap.MapMapEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class TestNestedMap final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:proto3.TestNestedMap) */ {
 public:
  inline TestNestedMap() : TestNestedMap(nullptr) {}
  ~TestNestedMap() override;
  explicit constexpr TestNestedMap(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestNestedMap(const TestNestedMap& from);
  TestNestedMap(TestNestedMap&& from) noexcept
    : TestNestedMap() {
    *this = ::std::move(from);
  }

  inline TestNestedMap& operator=(const TestNestedMap& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestNestedMap& operator=(TestNestedMap&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestNestedMap& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestNestedMap* internal_default_instance() {
    return reinterpret_cast<const TestNestedMap*>(
               &_TestNestedMap_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(TestNestedMap& a, TestNestedMap& b) {
    a.Swap(&b);
  }
  inline void Swap(TestNestedMap* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestNestedMap* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestNestedMap* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestNestedMap>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestNestedMap& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestNestedMap& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestNestedMap* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "proto3.TestNestedMap";
  }
  protected:
  explicit TestNestedMap(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kBoolMapFieldNumber = 1,
    kInt32MapFieldNumber = 2,
    kInt64MapFieldNumber = 3,
    kUint32MapFieldNumber = 4,
    kUint64MapFieldNumber = 5,
    kStringMapFieldNumber = 6,
    kMapMapFieldNumber = 7,
  };
  // map<bool, int32> bool_map = 1;
  int bool_map_size() const;
  private:
  int _internal_bool_map_size() const;
  public:
  void clear_bool_map();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >&
      _internal_bool_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >*
      _internal_mutable_bool_map();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >&
      bool_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >*
      mutable_bool_map();

  // map<int32, int32> int32_map = 2;
  int int32_map_size() const;
  private:
  int _internal_int32_map_size() const;
  public:
  void clear_int32_map();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int32_t >&
      _internal_int32_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int32_t >*
      _internal_mutable_int32_map();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int32_t >&
      int32_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int32_t >*
      mutable_int32_map();

  // map<int64, int32> int64_map = 3;
  int int64_map_size() const;
  private:
  int _internal_int64_map_size() const;
  public:
  void clear_int64_map();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >&
      _internal_int64_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >*
      _internal_mutable_int64_map();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >&
      int64_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >*
      mutable_int64_map();

  // map<uint32, int32> uint32_map = 4;
  int uint32_map_size() const;
  private:
  int _internal_uint32_map_size() const;
  public:
  void clear_uint32_map();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, int32_t >&
      _internal_uint32_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, int32_t >*
      _internal_mutable_uint32_map();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, int32_t >&
      uint32_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, int32_t >*
      mutable_uint32_map();

  // map<uint64, int32> uint64_map = 5;
  int uint64_map_size() const;
  private:
  int _internal_uint64_map_size() const;
  public:
  void clear_uint64_map();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, int32_t >&
      _internal_uint64_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, int32_t >*
      _internal_mutable_uint64_map();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, int32_t >&
      uint64_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, int32_t >*
      mutable_uint64_map();

  // map<string, int32> string_map = 6;
  int string_map_size() const;
  private:
  int _internal_string_map_size() const;
  public:
  void clear_string_map();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >&
      _internal_string_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >*
      _internal_mutable_string_map();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >&
      string_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >*
      mutable_string_map();

  // map<string, .proto3.TestNestedMap> map_map = 7;
  int map_map_size() const;
  private:
  int _internal_map_map_size() const;
  public:
  void clear_map_map();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::proto3::TestNestedMap >&
      _internal_map_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::proto3::TestNestedMap >*
      _internal_mutable_map_map();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::proto3::TestNestedMap >&
      map_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::proto3::TestNestedMap >*
      mutable_map_map();

  // @@protoc_insertion_point(class_scope:proto3.TestNestedMap)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      TestNestedMap_BoolMapEntry_DoNotUse,
      bool, int32_t,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_BOOL,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> bool_map_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      TestNestedMap_Int32MapEntry_DoNotUse,
      int32_t, int32_t,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> int32_map_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      TestNestedMap_Int64MapEntry_DoNotUse,
      int64_t, int32_t,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> int64_map_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      TestNestedMap_Uint32MapEntry_DoNotUse,
      uint32_t, int32_t,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> uint32_map_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      TestNestedMap_Uint64MapEntry_DoNotUse,
      uint64_t, int32_t,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT64,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> uint64_map_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      TestNestedMap_StringMapEntry_DoNotUse,
      std::string, int32_t,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> string_map_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      TestNestedMap_MapMapEntry_DoNotUse,
      std::string, ::proto3::TestNestedMap,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> map_map_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto;
};
// -------------------------------------------------------------------

class TestStringMap_StringMapEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestStringMap_StringMapEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestStringMap_StringMapEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  TestStringMap_StringMapEntry_DoNotUse();
  explicit constexpr TestStringMap_StringMapEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit TestStringMap_StringMapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const TestStringMap_StringMapEntry_DoNotUse& other);
  static const TestStringMap_StringMapEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const TestStringMap_StringMapEntry_DoNotUse*>(&_TestStringMap_StringMapEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "proto3.TestStringMap.StringMapEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "proto3.TestStringMap.StringMapEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class TestStringMap final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:proto3.TestStringMap) */ {
 public:
  inline TestStringMap() : TestStringMap(nullptr) {}
  ~TestStringMap() override;
  explicit constexpr TestStringMap(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestStringMap(const TestStringMap& from);
  TestStringMap(TestStringMap&& from) noexcept
    : TestStringMap() {
    *this = ::std::move(from);
  }

  inline TestStringMap& operator=(const TestStringMap& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestStringMap& operator=(TestStringMap&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestStringMap& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestStringMap* internal_default_instance() {
    return reinterpret_cast<const TestStringMap*>(
               &_TestStringMap_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(TestStringMap& a, TestStringMap& b) {
    a.Swap(&b);
  }
  inline void Swap(TestStringMap* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestStringMap* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestStringMap* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestStringMap>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestStringMap& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestStringMap& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestStringMap* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "proto3.TestStringMap";
  }
  protected:
  explicit TestStringMap(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kStringMapFieldNumber = 1,
  };
  // map<string, string> string_map = 1;
  int string_map_size() const;
  private:
  int _internal_string_map_size() const;
  public:
  void clear_string_map();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      _internal_string_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      _internal_mutable_string_map();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      string_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_string_map();

  // @@protoc_insertion_point(class_scope:proto3.TestStringMap)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      TestStringMap_StringMapEntry_DoNotUse,
      std::string, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> string_map_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto;
};
// -------------------------------------------------------------------

class TestWrapper final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:proto3.TestWrapper) */ {
 public:
  inline TestWrapper() : TestWrapper(nullptr) {}
  ~TestWrapper() override;
  explicit constexpr TestWrapper(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestWrapper(const TestWrapper& from);
  TestWrapper(TestWrapper&& from) noexcept
    : TestWrapper() {
    *this = ::std::move(from);
  }

  inline TestWrapper& operator=(const TestWrapper& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestWrapper& operator=(TestWrapper&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestWrapper& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestWrapper* internal_default_instance() {
    return reinterpret_cast<const TestWrapper*>(
               &_TestWrapper_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    20;

  friend void swap(TestWrapper& a, TestWrapper& b) {
    a.Swap(&b);
  }
  inline void Swap(TestWrapper* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestWrapper* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestWrapper* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestWrapper>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestWrapper& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestWrapper& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestWrapper* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "proto3.TestWrapper";
  }
  protected:
  explicit TestWrapper(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRepeatedBoolValueFieldNumber = 11,
    kRepeatedInt32ValueFieldNumber = 12,
    kRepeatedInt64ValueFieldNumber = 13,
    kRepeatedUint32ValueFieldNumber = 14,
    kRepeatedUint64ValueFieldNumber = 15,
    kRepeatedFloatValueFieldNumber = 16,
    kRepeatedDoubleValueFieldNumber = 17,
    kRepeatedStringValueFieldNumber = 18,
    kRepeatedBytesValueFieldNumber = 19,
    kBoolValueFieldNumber = 1,
    kInt32ValueFieldNumber = 2,
    kInt64ValueFieldNumber = 3,
    kUint32ValueFieldNumber = 4,
    kUint64ValueFieldNumber = 5,
    kFloatValueFieldNumber = 6,
    kDoubleValueFieldNumber = 7,
    kStringValueFieldNumber = 8,
    kBytesValueFieldNumber = 9,
  };
  // repeated .google.protobuf.BoolValue repeated_bool_value = 11;
  int repeated_bool_value_size() const;
  private:
  int _internal_repeated_bool_value_size() const;
  public:
  void clear_repeated_bool_value();
  ::PROTOBUF_NAMESPACE_ID::BoolValue* mutable_repeated_bool_value(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::BoolValue >*
      mutable_repeated_bool_value();
  private:
  const ::PROTOBUF_NAMESPACE_ID::BoolValue& _internal_repeated_bool_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::BoolValue* _internal_add_repeated_bool_value();
  public:
  const ::PROTOBUF_NAMESPACE_ID::BoolValue& repeated_bool_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::BoolValue* add_repeated_bool_value();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::BoolValue >&
      repeated_bool_value() const;

  // repeated .google.protobuf.Int32Value repeated_int32_value = 12;
  int repeated_int32_value_size() const;
  private:
  int _internal_repeated_int32_value_size() const;
  public:
  void clear_repeated_int32_value();
  ::PROTOBUF_NAMESPACE_ID::Int32Value* mutable_repeated_int32_value(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Int32Value >*
      mutable_repeated_int32_value();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Int32Value& _internal_repeated_int32_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::Int32Value* _internal_add_repeated_int32_value();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Int32Value& repeated_int32_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::Int32Value* add_repeated_int32_value();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Int32Value >&
      repeated_int32_value() const;

  // repeated .google.protobuf.Int64Value repeated_int64_value = 13;
  int repeated_int64_value_size() const;
  private:
  int _internal_repeated_int64_value_size() const;
  public:
  void clear_repeated_int64_value();
  ::PROTOBUF_NAMESPACE_ID::Int64Value* mutable_repeated_int64_value(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Int64Value >*
      mutable_repeated_int64_value();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Int64Value& _internal_repeated_int64_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::Int64Value* _internal_add_repeated_int64_value();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Int64Value& repeated_int64_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::Int64Value* add_repeated_int64_value();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Int64Value >&
      repeated_int64_value() const;

  // repeated .google.protobuf.UInt32Value repeated_uint32_value = 14;
  int repeated_uint32_value_size() const;
  private:
  int _internal_repeated_uint32_value_size() const;
  public:
  void clear_repeated_uint32_value();
  ::PROTOBUF_NAMESPACE_ID::UInt32Value* mutable_repeated_uint32_value(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::UInt32Value >*
      mutable_repeated_uint32_value();
  private:
  const ::PROTOBUF_NAMESPACE_ID::UInt32Value& _internal_repeated_uint32_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::UInt32Value* _internal_add_repeated_uint32_value();
  public:
  const ::PROTOBUF_NAMESPACE_ID::UInt32Value& repeated_uint32_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::UInt32Value* add_repeated_uint32_value();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::UInt32Value >&
      repeated_uint32_value() const;

  // repeated .google.protobuf.UInt64Value repeated_uint64_value = 15;
  int repeated_uint64_value_size() const;
  private:
  int _internal_repeated_uint64_value_size() const;
  public:
  void clear_repeated_uint64_value();
  ::PROTOBUF_NAMESPACE_ID::UInt64Value* mutable_repeated_uint64_value(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::UInt64Value >*
      mutable_repeated_uint64_value();
  private:
  const ::PROTOBUF_NAMESPACE_ID::UInt64Value& _internal_repeated_uint64_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::UInt64Value* _internal_add_repeated_uint64_value();
  public:
  const ::PROTOBUF_NAMESPACE_ID::UInt64Value& repeated_uint64_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::UInt64Value* add_repeated_uint64_value();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::UInt64Value >&
      repeated_uint64_value() const;

  // repeated .google.protobuf.FloatValue repeated_float_value = 16;
  int repeated_float_value_size() const;
  private:
  int _internal_repeated_float_value_size() const;
  public:
  void clear_repeated_float_value();
  ::PROTOBUF_NAMESPACE_ID::FloatValue* mutable_repeated_float_value(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::FloatValue >*
      mutable_repeated_float_value();
  private:
  const ::PROTOBUF_NAMESPACE_ID::FloatValue& _internal_repeated_float_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::FloatValue* _internal_add_repeated_float_value();
  public:
  const ::PROTOBUF_NAMESPACE_ID::FloatValue& repeated_float_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::FloatValue* add_repeated_float_value();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::FloatValue >&
      repeated_float_value() const;

  // repeated .google.protobuf.DoubleValue repeated_double_value = 17;
  int repeated_double_value_size() const;
  private:
  int _internal_repeated_double_value_size() const;
  public:
  void clear_repeated_double_value();
  ::PROTOBUF_NAMESPACE_ID::DoubleValue* mutable_repeated_double_value(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::DoubleValue >*
      mutable_repeated_double_value();
  private:
  const ::PROTOBUF_NAMESPACE_ID::DoubleValue& _internal_repeated_double_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::DoubleValue* _internal_add_repeated_double_value();
  public:
  const ::PROTOBUF_NAMESPACE_ID::DoubleValue& repeated_double_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::DoubleValue* add_repeated_double_value();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::DoubleValue >&
      repeated_double_value() const;

  // repeated .google.protobuf.StringValue repeated_string_value = 18;
  int repeated_string_value_size() const;
  private:
  int _internal_repeated_string_value_size() const;
  public:
  void clear_repeated_string_value();
  ::PROTOBUF_NAMESPACE_ID::StringValue* mutable_repeated_string_value(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::StringValue >*
      mutable_repeated_string_value();
  private:
  const ::PROTOBUF_NAMESPACE_ID::StringValue& _internal_repeated_string_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::StringValue* _internal_add_repeated_string_value();
  public:
  const ::PROTOBUF_NAMESPACE_ID::StringValue& repeated_string_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::StringValue* add_repeated_string_value();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::StringValue >&
      repeated_string_value() const;

  // repeated .google.protobuf.BytesValue repeated_bytes_value = 19;
  int repeated_bytes_value_size() const;
  private:
  int _internal_repeated_bytes_value_size() const;
  public:
  void clear_repeated_bytes_value();
  ::PROTOBUF_NAMESPACE_ID::BytesValue* mutable_repeated_bytes_value(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::BytesValue >*
      mutable_repeated_bytes_value();
  private:
  const ::PROTOBUF_NAMESPACE_ID::BytesValue& _internal_repeated_bytes_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::BytesValue* _internal_add_repeated_bytes_value();
  public:
  const ::PROTOBUF_NAMESPACE_ID::BytesValue& repeated_bytes_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::BytesValue* add_repeated_bytes_value();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::BytesValue >&
      repeated_bytes_value() const;

  // .google.protobuf.BoolValue bool_value = 1;
  bool has_bool_value() const;
  private:
  bool _internal_has_bool_value() const;
  public:
  void clear_bool_value();
  const ::PROTOBUF_NAMESPACE_ID::BoolValue& bool_value() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::BoolValue* release_bool_value();
  ::PROTOBUF_NAMESPACE_ID::BoolValue* mutable_bool_value();
  void set_allocated_bool_value(::PROTOBUF_NAMESPACE_ID::BoolValue* bool_value);
  private:
  const ::PROTOBUF_NAMESPACE_ID::BoolValue& _internal_bool_value() const;
  ::PROTOBUF_NAMESPACE_ID::BoolValue* _internal_mutable_bool_value();
  public:
  void unsafe_arena_set_allocated_bool_value(
      ::PROTOBUF_NAMESPACE_ID::BoolValue* bool_value);
  ::PROTOBUF_NAMESPACE_ID::BoolValue* unsafe_arena_release_bool_value();

  // .google.protobuf.Int32Value int32_value = 2;
  bool has_int32_value() const;
  private:
  bool _internal_has_int32_value() const;
  public:
  void clear_int32_value();
  const ::PROTOBUF_NAMESPACE_ID::Int32Value& int32_value() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::Int32Value* release_int32_value();
  ::PROTOBUF_NAMESPACE_ID::Int32Value* mutable_int32_value();
  void set_allocated_int32_value(::PROTOBUF_NAMESPACE_ID::Int32Value* int32_value);
  private:
  const ::PROTOBUF_NAMESPACE_ID::Int32Value& _internal_int32_value() const;
  ::PROTOBUF_NAMESPACE_ID::Int32Value* _internal_mutable_int32_value();
  public:
  void unsafe_arena_set_allocated_int32_value(
      ::PROTOBUF_NAMESPACE_ID::Int32Value* int32_value);
  ::PROTOBUF_NAMESPACE_ID::Int32Value* unsafe_arena_release_int32_value();

  // .google.protobuf.Int64Value int64_value = 3;
  bool has_int64_value() const;
  private:
  bool _internal_has_int64_value() const;
  public:
  void clear_int64_value();
  const ::PROTOBUF_NAMESPACE_ID::Int64Value& int64_value() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::Int64Value* release_int64_value();
  ::PROTOBUF_NAMESPACE_ID::Int64Value* mutable_int64_value();
  void set_allocated_int64_value(::PROTOBUF_NAMESPACE_ID::Int64Value* int64_value);
  private:
  const ::PROTOBUF_NAMESPACE_ID::Int64Value& _internal_int64_value() const;
  ::PROTOBUF_NAMESPACE_ID::Int64Value* _internal_mutable_int64_value();
  public:
  void unsafe_arena_set_allocated_int64_value(
      ::PROTOBUF_NAMESPACE_ID::Int64Value* int64_value);
  ::PROTOBUF_NAMESPACE_ID::Int64Value* unsafe_arena_release_int64_value();

  // .google.protobuf.UInt32Value uint32_value = 4;
  bool has_uint32_value() const;
  private:
  bool _internal_has_uint32_value() const;
  public:
  void clear_uint32_value();
  const ::PROTOBUF_NAMESPACE_ID::UInt32Value& uint32_value() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::UInt32Value* release_uint32_value();
  ::PROTOBUF_NAMESPACE_ID::UInt32Value* mutable_uint32_value();
  void set_allocated_uint32_value(::PROTOBUF_NAMESPACE_ID::UInt32Value* uint32_value);
  private:
  const ::PROTOBUF_NAMESPACE_ID::UInt32Value& _internal_uint32_value() const;
  ::PROTOBUF_NAMESPACE_ID::UInt32Value* _internal_mutable_uint32_value();
  public:
  void unsafe_arena_set_allocated_uint32_value(
      ::PROTOBUF_NAMESPACE_ID::UInt32Value* uint32_value);
  ::PROTOBUF_NAMESPACE_ID::UInt32Value* unsafe_arena_release_uint32_value();

  // .google.protobuf.UInt64Value uint64_value = 5;
  bool has_uint64_value() const;
  private:
  bool _internal_has_uint64_value() const;
  public:
  void clear_uint64_value();
  const ::PROTOBUF_NAMESPACE_ID::UInt64Value& uint64_value() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::UInt64Value* release_uint64_value();
  ::PROTOBUF_NAMESPACE_ID::UInt64Value* mutable_uint64_value();
  void set_allocated_uint64_value(::PROTOBUF_NAMESPACE_ID::UInt64Value* uint64_value);
  private:
  const ::PROTOBUF_NAMESPACE_ID::UInt64Value& _internal_uint64_value() const;
  ::PROTOBUF_NAMESPACE_ID::UInt64Value* _internal_mutable_uint64_value();
  public:
  void unsafe_arena_set_allocated_uint64_value(
      ::PROTOBUF_NAMESPACE_ID::UInt64Value* uint64_value);
  ::PROTOBUF_NAMESPACE_ID::UInt64Value* unsafe_arena_release_uint64_value();

  // .google.protobuf.FloatValue float_value = 6;
  bool has_float_value() const;
  private:
  bool _internal_has_float_value() const;
  public:
  void clear_float_value();
  const ::PROTOBUF_NAMESPACE_ID::FloatValue& float_value() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::FloatValue* release_float_value();
  ::PROTOBUF_NAMESPACE_ID::FloatValue* mutable_float_value();
  void set_allocated_float_value(::PROTOBUF_NAMESPACE_ID::FloatValue* float_value);
  private:
  const ::PROTOBUF_NAMESPACE_ID::FloatValue& _internal_float_value() const;
  ::PROTOBUF_NAMESPACE_ID::FloatValue* _internal_mutable_float_value();
  public:
  void unsafe_arena_set_allocated_float_value(
      ::PROTOBUF_NAMESPACE_ID::FloatValue* float_value);
  ::PROTOBUF_NAMESPACE_ID::FloatValue* unsafe_arena_release_float_value();

  // .google.protobuf.DoubleValue double_value = 7;
  bool has_double_value() const;
  private:
  bool _internal_has_double_value() const;
  public:
  void clear_double_value();
  const ::PROTOBUF_NAMESPACE_ID::DoubleValue& double_value() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::DoubleValue* release_double_value();
  ::PROTOBUF_NAMESPACE_ID::DoubleValue* mutable_double_value();
  void set_allocated_double_value(::PROTOBUF_NAMESPACE_ID::DoubleValue* double_value);
  private:
  const ::PROTOBUF_NAMESPACE_ID::DoubleValue& _internal_double_value() const;
  ::PROTOBUF_NAMESPACE_ID::DoubleValue* _internal_mutable_double_value();
  public:
  void unsafe_arena_set_allocated_double_value(
      ::PROTOBUF_NAMESPACE_ID::DoubleValue* double_value);
  ::PROTOBUF_NAMESPACE_ID::DoubleValue* unsafe_arena_release_double_value();

  // .google.protobuf.StringValue string_value = 8;
  bool has_string_value() const;
  private:
  bool _internal_has_string_value() const;
  public:
  void clear_string_value();
  const ::PROTOBUF_NAMESPACE_ID::StringValue& string_value() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::StringValue* release_string_value();
  ::PROTOBUF_NAMESPACE_ID::StringValue* mutable_string_value();
  void set_allocated_string_value(::PROTOBUF_NAMESPACE_ID::StringValue* string_value);
  private:
  const ::PROTOBUF_NAMESPACE_ID::StringValue& _internal_string_value() const;
  ::PROTOBUF_NAMESPACE_ID::StringValue* _internal_mutable_string_value();
  public:
  void unsafe_arena_set_allocated_string_value(
      ::PROTOBUF_NAMESPACE_ID::StringValue* string_value);
  ::PROTOBUF_NAMESPACE_ID::StringValue* unsafe_arena_release_string_value();

  // .google.protobuf.BytesValue bytes_value = 9;
  bool has_bytes_value() const;
  private:
  bool _internal_has_bytes_value() const;
  public:
  void clear_bytes_value();
  const ::PROTOBUF_NAMESPACE_ID::BytesValue& bytes_value() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::BytesValue* release_bytes_value();
  ::PROTOBUF_NAMESPACE_ID::BytesValue* mutable_bytes_value();
  void set_allocated_bytes_value(::PROTOBUF_NAMESPACE_ID::BytesValue* bytes_value);
  private:
  const ::PROTOBUF_NAMESPACE_ID::BytesValue& _internal_bytes_value() const;
  ::PROTOBUF_NAMESPACE_ID::BytesValue* _internal_mutable_bytes_value();
  public:
  void unsafe_arena_set_allocated_bytes_value(
      ::PROTOBUF_NAMESPACE_ID::BytesValue* bytes_value);
  ::PROTOBUF_NAMESPACE_ID::BytesValue* unsafe_arena_release_bytes_value();

  // @@protoc_insertion_point(class_scope:proto3.TestWrapper)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::BoolValue > repeated_bool_value_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Int32Value > repeated_int32_value_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Int64Value > repeated_int64_value_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::UInt32Value > repeated_uint32_value_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::UInt64Value > repeated_uint64_value_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::FloatValue > repeated_float_value_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::DoubleValue > repeated_double_value_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::StringValue > repeated_string_value_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::BytesValue > repeated_bytes_value_;
  ::PROTOBUF_NAMESPACE_ID::BoolValue* bool_value_;
  ::PROTOBUF_NAMESPACE_ID::Int32Value* int32_value_;
  ::PROTOBUF_NAMESPACE_ID::Int64Value* int64_value_;
  ::PROTOBUF_NAMESPACE_ID::UInt32Value* uint32_value_;
  ::PROTOBUF_NAMESPACE_ID::UInt64Value* uint64_value_;
  ::PROTOBUF_NAMESPACE_ID::FloatValue* float_value_;
  ::PROTOBUF_NAMESPACE_ID::DoubleValue* double_value_;
  ::PROTOBUF_NAMESPACE_ID::StringValue* string_value_;
  ::PROTOBUF_NAMESPACE_ID::BytesValue* bytes_value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto;
};
// -------------------------------------------------------------------

class TestTimestamp final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:proto3.TestTimestamp) */ {
 public:
  inline TestTimestamp() : TestTimestamp(nullptr) {}
  ~TestTimestamp() override;
  explicit constexpr TestTimestamp(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestTimestamp(const TestTimestamp& from);
  TestTimestamp(TestTimestamp&& from) noexcept
    : TestTimestamp() {
    *this = ::std::move(from);
  }

  inline TestTimestamp& operator=(const TestTimestamp& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestTimestamp& operator=(TestTimestamp&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestTimestamp& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestTimestamp* internal_default_instance() {
    return reinterpret_cast<const TestTimestamp*>(
               &_TestTimestamp_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  friend void swap(TestTimestamp& a, TestTimestamp& b) {
    a.Swap(&b);
  }
  inline void Swap(TestTimestamp* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestTimestamp* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestTimestamp* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestTimestamp>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestTimestamp& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestTimestamp& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestTimestamp* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "proto3.TestTimestamp";
  }
  protected:
  explicit TestTimestamp(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRepeatedValueFieldNumber = 2,
    kValueFieldNumber = 1,
  };
  // repeated .google.protobuf.Timestamp repeated_value = 2;
  int repeated_value_size() const;
  private:
  int _internal_repeated_value_size() const;
  public:
  void clear_repeated_value();
  ::PROTOBUF_NAMESPACE_ID::Timestamp* mutable_repeated_value(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Timestamp >*
      mutable_repeated_value();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Timestamp& _internal_repeated_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::Timestamp* _internal_add_repeated_value();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Timestamp& repeated_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::Timestamp* add_repeated_value();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Timestamp >&
      repeated_value() const;

  // .google.protobuf.Timestamp value = 1;
  bool has_value() const;
  private:
  bool _internal_has_value() const;
  public:
  void clear_value();
  const ::PROTOBUF_NAMESPACE_ID::Timestamp& value() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::Timestamp* release_value();
  ::PROTOBUF_NAMESPACE_ID::Timestamp* mutable_value();
  void set_allocated_value(::PROTOBUF_NAMESPACE_ID::Timestamp* value);
  private:
  const ::PROTOBUF_NAMESPACE_ID::Timestamp& _internal_value() const;
  ::PROTOBUF_NAMESPACE_ID::Timestamp* _internal_mutable_value();
  public:
  void unsafe_arena_set_allocated_value(
      ::PROTOBUF_NAMESPACE_ID::Timestamp* value);
  ::PROTOBUF_NAMESPACE_ID::Timestamp* unsafe_arena_release_value();

  // @@protoc_insertion_point(class_scope:proto3.TestTimestamp)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Timestamp > repeated_value_;
  ::PROTOBUF_NAMESPACE_ID::Timestamp* value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto;
};
// -------------------------------------------------------------------

class TestDuration final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:proto3.TestDuration) */ {
 public:
  inline TestDuration() : TestDuration(nullptr) {}
  ~TestDuration() override;
  explicit constexpr TestDuration(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestDuration(const TestDuration& from);
  TestDuration(TestDuration&& from) noexcept
    : TestDuration() {
    *this = ::std::move(from);
  }

  inline TestDuration& operator=(const TestDuration& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestDuration& operator=(TestDuration&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestDuration& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestDuration* internal_default_instance() {
    return reinterpret_cast<const TestDuration*>(
               &_TestDuration_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    22;

  friend void swap(TestDuration& a, TestDuration& b) {
    a.Swap(&b);
  }
  inline void Swap(TestDuration* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestDuration* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestDuration* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestDuration>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestDuration& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestDuration& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestDuration* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "proto3.TestDuration";
  }
  protected:
  explicit TestDuration(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRepeatedValueFieldNumber = 2,
    kValueFieldNumber = 1,
  };
  // repeated .google.protobuf.Duration repeated_value = 2;
  int repeated_value_size() const;
  private:
  int _internal_repeated_value_size() const;
  public:
  void clear_repeated_value();
  ::PROTOBUF_NAMESPACE_ID::Duration* mutable_repeated_value(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Duration >*
      mutable_repeated_value();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Duration& _internal_repeated_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::Duration* _internal_add_repeated_value();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Duration& repeated_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::Duration* add_repeated_value();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Duration >&
      repeated_value() const;

  // .google.protobuf.Duration value = 1;
  bool has_value() const;
  private:
  bool _internal_has_value() const;
  public:
  void clear_value();
  const ::PROTOBUF_NAMESPACE_ID::Duration& value() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::Duration* release_value();
  ::PROTOBUF_NAMESPACE_ID::Duration* mutable_value();
  void set_allocated_value(::PROTOBUF_NAMESPACE_ID::Duration* value);
  private:
  const ::PROTOBUF_NAMESPACE_ID::Duration& _internal_value() const;
  ::PROTOBUF_NAMESPACE_ID::Duration* _internal_mutable_value();
  public:
  void unsafe_arena_set_allocated_value(
      ::PROTOBUF_NAMESPACE_ID::Duration* value);
  ::PROTOBUF_NAMESPACE_ID::Duration* unsafe_arena_release_value();

  // @@protoc_insertion_point(class_scope:proto3.TestDuration)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Duration > repeated_value_;
  ::PROTOBUF_NAMESPACE_ID::Duration* value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto;
};
// -------------------------------------------------------------------

class TestFieldMask final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:proto3.TestFieldMask) */ {
 public:
  inline TestFieldMask() : TestFieldMask(nullptr) {}
  ~TestFieldMask() override;
  explicit constexpr TestFieldMask(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestFieldMask(const TestFieldMask& from);
  TestFieldMask(TestFieldMask&& from) noexcept
    : TestFieldMask() {
    *this = ::std::move(from);
  }

  inline TestFieldMask& operator=(const TestFieldMask& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestFieldMask& operator=(TestFieldMask&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestFieldMask& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestFieldMask* internal_default_instance() {
    return reinterpret_cast<const TestFieldMask*>(
               &_TestFieldMask_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    23;

  friend void swap(TestFieldMask& a, TestFieldMask& b) {
    a.Swap(&b);
  }
  inline void Swap(TestFieldMask* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestFieldMask* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestFieldMask* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestFieldMask>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestFieldMask& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestFieldMask& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestFieldMask* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "proto3.TestFieldMask";
  }
  protected:
  explicit TestFieldMask(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValueFieldNumber = 1,
  };
  // .google.protobuf.FieldMask value = 1;
  bool has_value() const;
  private:
  bool _internal_has_value() const;
  public:
  void clear_value();
  const ::PROTOBUF_NAMESPACE_ID::FieldMask& value() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::FieldMask* release_value();
  ::PROTOBUF_NAMESPACE_ID::FieldMask* mutable_value();
  void set_allocated_value(::PROTOBUF_NAMESPACE_ID::FieldMask* value);
  private:
  const ::PROTOBUF_NAMESPACE_ID::FieldMask& _internal_value() const;
  ::PROTOBUF_NAMESPACE_ID::FieldMask* _internal_mutable_value();
  public:
  void unsafe_arena_set_allocated_value(
      ::PROTOBUF_NAMESPACE_ID::FieldMask* value);
  ::PROTOBUF_NAMESPACE_ID::FieldMask* unsafe_arena_release_value();

  // @@protoc_insertion_point(class_scope:proto3.TestFieldMask)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::FieldMask* value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto;
};
// -------------------------------------------------------------------

class TestStruct final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:proto3.TestStruct) */ {
 public:
  inline TestStruct() : TestStruct(nullptr) {}
  ~TestStruct() override;
  explicit constexpr TestStruct(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestStruct(const TestStruct& from);
  TestStruct(TestStruct&& from) noexcept
    : TestStruct() {
    *this = ::std::move(from);
  }

  inline TestStruct& operator=(const TestStruct& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestStruct& operator=(TestStruct&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestStruct& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestStruct* internal_default_instance() {
    return reinterpret_cast<const TestStruct*>(
               &_TestStruct_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    24;

  friend void swap(TestStruct& a, TestStruct& b) {
    a.Swap(&b);
  }
  inline void Swap(TestStruct* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestStruct* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestStruct* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestStruct>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestStruct& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestStruct& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestStruct* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "proto3.TestStruct";
  }
  protected:
  explicit TestStruct(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRepeatedValueFieldNumber = 2,
    kValueFieldNumber = 1,
  };
  // repeated .google.protobuf.Struct repeated_value = 2;
  int repeated_value_size() const;
  private:
  int _internal_repeated_value_size() const;
  public:
  void clear_repeated_value();
  ::PROTOBUF_NAMESPACE_ID::Struct* mutable_repeated_value(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Struct >*
      mutable_repeated_value();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Struct& _internal_repeated_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::Struct* _internal_add_repeated_value();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Struct& repeated_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::Struct* add_repeated_value();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Struct >&
      repeated_value() const;

  // .google.protobuf.Struct value = 1;
  bool has_value() const;
  private:
  bool _internal_has_value() const;
  public:
  void clear_value();
  const ::PROTOBUF_NAMESPACE_ID::Struct& value() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::Struct* release_value();
  ::PROTOBUF_NAMESPACE_ID::Struct* mutable_value();
  void set_allocated_value(::PROTOBUF_NAMESPACE_ID::Struct* value);
  private:
  const ::PROTOBUF_NAMESPACE_ID::Struct& _internal_value() const;
  ::PROTOBUF_NAMESPACE_ID::Struct* _internal_mutable_value();
  public:
  void unsafe_arena_set_allocated_value(
      ::PROTOBUF_NAMESPACE_ID::Struct* value);
  ::PROTOBUF_NAMESPACE_ID::Struct* unsafe_arena_release_value();

  // @@protoc_insertion_point(class_scope:proto3.TestStruct)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Struct > repeated_value_;
  ::PROTOBUF_NAMESPACE_ID::Struct* value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto;
};
// -------------------------------------------------------------------

class TestAny final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:proto3.TestAny) */ {
 public:
  inline TestAny() : TestAny(nullptr) {}
  ~TestAny() override;
  explicit constexpr TestAny(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestAny(const TestAny& from);
  TestAny(TestAny&& from) noexcept
    : TestAny() {
    *this = ::std::move(from);
  }

  inline TestAny& operator=(const TestAny& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestAny& operator=(TestAny&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestAny& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestAny* internal_default_instance() {
    return reinterpret_cast<const TestAny*>(
               &_TestAny_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    25;

  friend void swap(TestAny& a, TestAny& b) {
    a.Swap(&b);
  }
  inline void Swap(TestAny* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestAny* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestAny* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestAny>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestAny& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestAny& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestAny* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "proto3.TestAny";
  }
  protected:
  explicit TestAny(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRepeatedValueFieldNumber = 2,
    kValueFieldNumber = 1,
  };
  // repeated .google.protobuf.Any repeated_value = 2;
  int repeated_value_size() const;
  private:
  int _internal_repeated_value_size() const;
  public:
  void clear_repeated_value();
  ::PROTOBUF_NAMESPACE_ID::Any* mutable_repeated_value(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Any >*
      mutable_repeated_value();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Any& _internal_repeated_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::Any* _internal_add_repeated_value();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Any& repeated_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::Any* add_repeated_value();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Any >&
      repeated_value() const;

  // .google.protobuf.Any value = 1;
  bool has_value() const;
  private:
  bool _internal_has_value() const;
  public:
  void clear_value();
  const ::PROTOBUF_NAMESPACE_ID::Any& value() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::Any* release_value();
  ::PROTOBUF_NAMESPACE_ID::Any* mutable_value();
  void set_allocated_value(::PROTOBUF_NAMESPACE_ID::Any* value);
  private:
  const ::PROTOBUF_NAMESPACE_ID::Any& _internal_value() const;
  ::PROTOBUF_NAMESPACE_ID::Any* _internal_mutable_value();
  public:
  void unsafe_arena_set_allocated_value(
      ::PROTOBUF_NAMESPACE_ID::Any* value);
  ::PROTOBUF_NAMESPACE_ID::Any* unsafe_arena_release_value();

  // @@protoc_insertion_point(class_scope:proto3.TestAny)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Any > repeated_value_;
  ::PROTOBUF_NAMESPACE_ID::Any* value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto;
};
// -------------------------------------------------------------------

class TestValue final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:proto3.TestValue) */ {
 public:
  inline TestValue() : TestValue(nullptr) {}
  ~TestValue() override;
  explicit constexpr TestValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestValue(const TestValue& from);
  TestValue(TestValue&& from) noexcept
    : TestValue() {
    *this = ::std::move(from);
  }

  inline TestValue& operator=(const TestValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestValue& operator=(TestValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestValue& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestValue* internal_default_instance() {
    return reinterpret_cast<const TestValue*>(
               &_TestValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    26;

  friend void swap(TestValue& a, TestValue& b) {
    a.Swap(&b);
  }
  inline void Swap(TestValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestValue>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestValue& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestValue& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "proto3.TestValue";
  }
  protected:
  explicit TestValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRepeatedValueFieldNumber = 2,
    kValueFieldNumber = 1,
  };
  // repeated .google.protobuf.Value repeated_value = 2;
  int repeated_value_size() const;
  private:
  int _internal_repeated_value_size() const;
  public:
  void clear_repeated_value();
  ::PROTOBUF_NAMESPACE_ID::Value* mutable_repeated_value(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Value >*
      mutable_repeated_value();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Value& _internal_repeated_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::Value* _internal_add_repeated_value();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Value& repeated_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::Value* add_repeated_value();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Value >&
      repeated_value() const;

  // .google.protobuf.Value value = 1;
  bool has_value() const;
  private:
  bool _internal_has_value() const;
  public:
  void clear_value();
  const ::PROTOBUF_NAMESPACE_ID::Value& value() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::Value* release_value();
  ::PROTOBUF_NAMESPACE_ID::Value* mutable_value();
  void set_allocated_value(::PROTOBUF_NAMESPACE_ID::Value* value);
  private:
  const ::PROTOBUF_NAMESPACE_ID::Value& _internal_value() const;
  ::PROTOBUF_NAMESPACE_ID::Value* _internal_mutable_value();
  public:
  void unsafe_arena_set_allocated_value(
      ::PROTOBUF_NAMESPACE_ID::Value* value);
  ::PROTOBUF_NAMESPACE_ID::Value* unsafe_arena_release_value();

  // @@protoc_insertion_point(class_scope:proto3.TestValue)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Value > repeated_value_;
  ::PROTOBUF_NAMESPACE_ID::Value* value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto;
};
// -------------------------------------------------------------------

class TestListValue final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:proto3.TestListValue) */ {
 public:
  inline TestListValue() : TestListValue(nullptr) {}
  ~TestListValue() override;
  explicit constexpr TestListValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestListValue(const TestListValue& from);
  TestListValue(TestListValue&& from) noexcept
    : TestListValue() {
    *this = ::std::move(from);
  }

  inline TestListValue& operator=(const TestListValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestListValue& operator=(TestListValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestListValue& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestListValue* internal_default_instance() {
    return reinterpret_cast<const TestListValue*>(
               &_TestListValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    27;

  friend void swap(TestListValue& a, TestListValue& b) {
    a.Swap(&b);
  }
  inline void Swap(TestListValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestListValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestListValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestListValue>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestListValue& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestListValue& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestListValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "proto3.TestListValue";
  }
  protected:
  explicit TestListValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRepeatedValueFieldNumber = 2,
    kValueFieldNumber = 1,
  };
  // repeated .google.protobuf.ListValue repeated_value = 2;
  int repeated_value_size() const;
  private:
  int _internal_repeated_value_size() const;
  public:
  void clear_repeated_value();
  ::PROTOBUF_NAMESPACE_ID::ListValue* mutable_repeated_value(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::ListValue >*
      mutable_repeated_value();
  private:
  const ::PROTOBUF_NAMESPACE_ID::ListValue& _internal_repeated_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::ListValue* _internal_add_repeated_value();
  public:
  const ::PROTOBUF_NAMESPACE_ID::ListValue& repeated_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::ListValue* add_repeated_value();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::ListValue >&
      repeated_value() const;

  // .google.protobuf.ListValue value = 1;
  bool has_value() const;
  private:
  bool _internal_has_value() const;
  public:
  void clear_value();
  const ::PROTOBUF_NAMESPACE_ID::ListValue& value() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::ListValue* release_value();
  ::PROTOBUF_NAMESPACE_ID::ListValue* mutable_value();
  void set_allocated_value(::PROTOBUF_NAMESPACE_ID::ListValue* value);
  private:
  const ::PROTOBUF_NAMESPACE_ID::ListValue& _internal_value() const;
  ::PROTOBUF_NAMESPACE_ID::ListValue* _internal_mutable_value();
  public:
  void unsafe_arena_set_allocated_value(
      ::PROTOBUF_NAMESPACE_ID::ListValue* value);
  ::PROTOBUF_NAMESPACE_ID::ListValue* unsafe_arena_release_value();

  // @@protoc_insertion_point(class_scope:proto3.TestListValue)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::ListValue > repeated_value_;
  ::PROTOBUF_NAMESPACE_ID::ListValue* value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto;
};
// -------------------------------------------------------------------

class TestBoolValue_BoolMapEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestBoolValue_BoolMapEntry_DoNotUse, 
    bool, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_BOOL,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestBoolValue_BoolMapEntry_DoNotUse, 
    bool, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_BOOL,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> SuperType;
  TestBoolValue_BoolMapEntry_DoNotUse();
  explicit constexpr TestBoolValue_BoolMapEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit TestBoolValue_BoolMapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const TestBoolValue_BoolMapEntry_DoNotUse& other);
  static const TestBoolValue_BoolMapEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const TestBoolValue_BoolMapEntry_DoNotUse*>(&_TestBoolValue_BoolMapEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class TestBoolValue final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:proto3.TestBoolValue) */ {
 public:
  inline TestBoolValue() : TestBoolValue(nullptr) {}
  ~TestBoolValue() override;
  explicit constexpr TestBoolValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestBoolValue(const TestBoolValue& from);
  TestBoolValue(TestBoolValue&& from) noexcept
    : TestBoolValue() {
    *this = ::std::move(from);
  }

  inline TestBoolValue& operator=(const TestBoolValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestBoolValue& operator=(TestBoolValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestBoolValue& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestBoolValue* internal_default_instance() {
    return reinterpret_cast<const TestBoolValue*>(
               &_TestBoolValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    29;

  friend void swap(TestBoolValue& a, TestBoolValue& b) {
    a.Swap(&b);
  }
  inline void Swap(TestBoolValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestBoolValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestBoolValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestBoolValue>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestBoolValue& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestBoolValue& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestBoolValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "proto3.TestBoolValue";
  }
  protected:
  explicit TestBoolValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kBoolMapFieldNumber = 2,
    kBoolValueFieldNumber = 1,
  };
  // map<bool, int32> bool_map = 2;
  int bool_map_size() const;
  private:
  int _internal_bool_map_size() const;
  public:
  void clear_bool_map();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >&
      _internal_bool_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >*
      _internal_mutable_bool_map();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >&
      bool_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >*
      mutable_bool_map();

  // bool bool_value = 1;
  void clear_bool_value();
  bool bool_value() const;
  void set_bool_value(bool value);
  private:
  bool _internal_bool_value() const;
  void _internal_set_bool_value(bool value);
  public:

  // @@protoc_insertion_point(class_scope:proto3.TestBoolValue)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      TestBoolValue_BoolMapEntry_DoNotUse,
      bool, int32_t,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_BOOL,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> bool_map_;
  bool bool_value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto;
};
// -------------------------------------------------------------------

class TestCustomJsonName final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:proto3.TestCustomJsonName) */ {
 public:
  inline TestCustomJsonName() : TestCustomJsonName(nullptr) {}
  ~TestCustomJsonName() override;
  explicit constexpr TestCustomJsonName(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestCustomJsonName(const TestCustomJsonName& from);
  TestCustomJsonName(TestCustomJsonName&& from) noexcept
    : TestCustomJsonName() {
    *this = ::std::move(from);
  }

  inline TestCustomJsonName& operator=(const TestCustomJsonName& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestCustomJsonName& operator=(TestCustomJsonName&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestCustomJsonName& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestCustomJsonName* internal_default_instance() {
    return reinterpret_cast<const TestCustomJsonName*>(
               &_TestCustomJsonName_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    30;

  friend void swap(TestCustomJsonName& a, TestCustomJsonName& b) {
    a.Swap(&b);
  }
  inline void Swap(TestCustomJsonName* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestCustomJsonName* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestCustomJsonName* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestCustomJsonName>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestCustomJsonName& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestCustomJsonName& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestCustomJsonName* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "proto3.TestCustomJsonName";
  }
  protected:
  explicit TestCustomJsonName(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValueFieldNumber = 1,
  };
  // int32 value = 1 [json_name = "@value"];
  void clear_value();
  int32_t value() const;
  void set_value(int32_t value);
  private:
  int32_t _internal_value() const;
  void _internal_set_value(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:proto3.TestCustomJsonName)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int32_t value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto;
};
// -------------------------------------------------------------------

class TestExtensions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:proto3.TestExtensions) */ {
 public:
  inline TestExtensions() : TestExtensions(nullptr) {}
  ~TestExtensions() override;
  explicit constexpr TestExtensions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestExtensions(const TestExtensions& from);
  TestExtensions(TestExtensions&& from) noexcept
    : TestExtensions() {
    *this = ::std::move(from);
  }

  inline TestExtensions& operator=(const TestExtensions& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestExtensions& operator=(TestExtensions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestExtensions& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestExtensions* internal_default_instance() {
    return reinterpret_cast<const TestExtensions*>(
               &_TestExtensions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    31;

  friend void swap(TestExtensions& a, TestExtensions& b) {
    a.Swap(&b);
  }
  inline void Swap(TestExtensions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestExtensions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestExtensions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestExtensions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestExtensions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestExtensions& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestExtensions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "proto3.TestExtensions";
  }
  protected:
  explicit TestExtensions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kExtensionsFieldNumber = 1,
  };
  // .protobuf_unittest.TestAllExtensions extensions = 1;
  bool has_extensions() const;
  private:
  bool _internal_has_extensions() const;
  public:
  void clear_extensions();
  const ::protobuf_unittest::TestAllExtensions& extensions() const;
  PROTOBUF_NODISCARD ::protobuf_unittest::TestAllExtensions* release_extensions();
  ::protobuf_unittest::TestAllExtensions* mutable_extensions();
  void set_allocated_extensions(::protobuf_unittest::TestAllExtensions* extensions);
  private:
  const ::protobuf_unittest::TestAllExtensions& _internal_extensions() const;
  ::protobuf_unittest::TestAllExtensions* _internal_mutable_extensions();
  public:
  void unsafe_arena_set_allocated_extensions(
      ::protobuf_unittest::TestAllExtensions* extensions);
  ::protobuf_unittest::TestAllExtensions* unsafe_arena_release_extensions();

  // @@protoc_insertion_point(class_scope:proto3.TestExtensions)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::protobuf_unittest::TestAllExtensions* extensions_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto;
};
// -------------------------------------------------------------------

class TestEnumValue final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:proto3.TestEnumValue) */ {
 public:
  inline TestEnumValue() : TestEnumValue(nullptr) {}
  ~TestEnumValue() override;
  explicit constexpr TestEnumValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestEnumValue(const TestEnumValue& from);
  TestEnumValue(TestEnumValue&& from) noexcept
    : TestEnumValue() {
    *this = ::std::move(from);
  }

  inline TestEnumValue& operator=(const TestEnumValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestEnumValue& operator=(TestEnumValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestEnumValue& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestEnumValue* internal_default_instance() {
    return reinterpret_cast<const TestEnumValue*>(
               &_TestEnumValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    32;

  friend void swap(TestEnumValue& a, TestEnumValue& b) {
    a.Swap(&b);
  }
  inline void Swap(TestEnumValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestEnumValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestEnumValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestEnumValue>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestEnumValue& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestEnumValue& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestEnumValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "proto3.TestEnumValue";
  }
  protected:
  explicit TestEnumValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEnumValue1FieldNumber = 1,
    kEnumValue2FieldNumber = 2,
    kEnumValue3FieldNumber = 3,
  };
  // .proto3.EnumType enum_value1 = 1;
  void clear_enum_value1();
  ::proto3::EnumType enum_value1() const;
  void set_enum_value1(::proto3::EnumType value);
  private:
  ::proto3::EnumType _internal_enum_value1() const;
  void _internal_set_enum_value1(::proto3::EnumType value);
  public:

  // .proto3.EnumType enum_value2 = 2;
  void clear_enum_value2();
  ::proto3::EnumType enum_value2() const;
  void set_enum_value2(::proto3::EnumType value);
  private:
  ::proto3::EnumType _internal_enum_value2() const;
  void _internal_set_enum_value2(::proto3::EnumType value);
  public:

  // .proto3.EnumType enum_value3 = 3;
  void clear_enum_value3();
  ::proto3::EnumType enum_value3() const;
  void set_enum_value3(::proto3::EnumType value);
  private:
  ::proto3::EnumType _internal_enum_value3() const;
  void _internal_set_enum_value3(::proto3::EnumType value);
  public:

  // @@protoc_insertion_point(class_scope:proto3.TestEnumValue)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int enum_value1_;
  int enum_value2_;
  int enum_value3_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// MessageType

// int32 value = 1;
inline void MessageType::clear_value() {
  value_ = 0;
}
inline int32_t MessageType::_internal_value() const {
  return value_;
}
inline int32_t MessageType::value() const {
  // @@protoc_insertion_point(field_get:proto3.MessageType.value)
  return _internal_value();
}
inline void MessageType::_internal_set_value(int32_t value) {
  
  value_ = value;
}
inline void MessageType::set_value(int32_t value) {
  _internal_set_value(value);
  // @@protoc_insertion_point(field_set:proto3.MessageType.value)
}

// -------------------------------------------------------------------

// TestMessage

// bool bool_value = 1;
inline void TestMessage::clear_bool_value() {
  bool_value_ = false;
}
inline bool TestMessage::_internal_bool_value() const {
  return bool_value_;
}
inline bool TestMessage::bool_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.bool_value)
  return _internal_bool_value();
}
inline void TestMessage::_internal_set_bool_value(bool value) {
  
  bool_value_ = value;
}
inline void TestMessage::set_bool_value(bool value) {
  _internal_set_bool_value(value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.bool_value)
}

// int32 int32_value = 2;
inline void TestMessage::clear_int32_value() {
  int32_value_ = 0;
}
inline int32_t TestMessage::_internal_int32_value() const {
  return int32_value_;
}
inline int32_t TestMessage::int32_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.int32_value)
  return _internal_int32_value();
}
inline void TestMessage::_internal_set_int32_value(int32_t value) {
  
  int32_value_ = value;
}
inline void TestMessage::set_int32_value(int32_t value) {
  _internal_set_int32_value(value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.int32_value)
}

// int64 int64_value = 3;
inline void TestMessage::clear_int64_value() {
  int64_value_ = int64_t{0};
}
inline int64_t TestMessage::_internal_int64_value() const {
  return int64_value_;
}
inline int64_t TestMessage::int64_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.int64_value)
  return _internal_int64_value();
}
inline void TestMessage::_internal_set_int64_value(int64_t value) {
  
  int64_value_ = value;
}
inline void TestMessage::set_int64_value(int64_t value) {
  _internal_set_int64_value(value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.int64_value)
}

// uint32 uint32_value = 4;
inline void TestMessage::clear_uint32_value() {
  uint32_value_ = 0u;
}
inline uint32_t TestMessage::_internal_uint32_value() const {
  return uint32_value_;
}
inline uint32_t TestMessage::uint32_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.uint32_value)
  return _internal_uint32_value();
}
inline void TestMessage::_internal_set_uint32_value(uint32_t value) {
  
  uint32_value_ = value;
}
inline void TestMessage::set_uint32_value(uint32_t value) {
  _internal_set_uint32_value(value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.uint32_value)
}

// uint64 uint64_value = 5;
inline void TestMessage::clear_uint64_value() {
  uint64_value_ = uint64_t{0u};
}
inline uint64_t TestMessage::_internal_uint64_value() const {
  return uint64_value_;
}
inline uint64_t TestMessage::uint64_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.uint64_value)
  return _internal_uint64_value();
}
inline void TestMessage::_internal_set_uint64_value(uint64_t value) {
  
  uint64_value_ = value;
}
inline void TestMessage::set_uint64_value(uint64_t value) {
  _internal_set_uint64_value(value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.uint64_value)
}

// float float_value = 6;
inline void TestMessage::clear_float_value() {
  float_value_ = 0;
}
inline float TestMessage::_internal_float_value() const {
  return float_value_;
}
inline float TestMessage::float_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.float_value)
  return _internal_float_value();
}
inline void TestMessage::_internal_set_float_value(float value) {
  
  float_value_ = value;
}
inline void TestMessage::set_float_value(float value) {
  _internal_set_float_value(value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.float_value)
}

// double double_value = 7;
inline void TestMessage::clear_double_value() {
  double_value_ = 0;
}
inline double TestMessage::_internal_double_value() const {
  return double_value_;
}
inline double TestMessage::double_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.double_value)
  return _internal_double_value();
}
inline void TestMessage::_internal_set_double_value(double value) {
  
  double_value_ = value;
}
inline void TestMessage::set_double_value(double value) {
  _internal_set_double_value(value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.double_value)
}

// string string_value = 8;
inline void TestMessage::clear_string_value() {
  string_value_.ClearToEmpty();
}
inline const std::string& TestMessage::string_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.string_value)
  return _internal_string_value();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TestMessage::set_string_value(ArgT0&& arg0, ArgT... args) {
 
 string_value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:proto3.TestMessage.string_value)
}
inline std::string* TestMessage::mutable_string_value() {
  std::string* _s = _internal_mutable_string_value();
  // @@protoc_insertion_point(field_mutable:proto3.TestMessage.string_value)
  return _s;
}
inline const std::string& TestMessage::_internal_string_value() const {
  return string_value_.Get();
}
inline void TestMessage::_internal_set_string_value(const std::string& value) {
  
  string_value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* TestMessage::_internal_mutable_string_value() {
  
  return string_value_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* TestMessage::release_string_value() {
  // @@protoc_insertion_point(field_release:proto3.TestMessage.string_value)
  return string_value_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void TestMessage::set_allocated_string_value(std::string* string_value) {
  if (string_value != nullptr) {
    
  } else {
    
  }
  string_value_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), string_value,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (string_value_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    string_value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:proto3.TestMessage.string_value)
}

// bytes bytes_value = 9;
inline void TestMessage::clear_bytes_value() {
  bytes_value_.ClearToEmpty();
}
inline const std::string& TestMessage::bytes_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.bytes_value)
  return _internal_bytes_value();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TestMessage::set_bytes_value(ArgT0&& arg0, ArgT... args) {
 
 bytes_value_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:proto3.TestMessage.bytes_value)
}
inline std::string* TestMessage::mutable_bytes_value() {
  std::string* _s = _internal_mutable_bytes_value();
  // @@protoc_insertion_point(field_mutable:proto3.TestMessage.bytes_value)
  return _s;
}
inline const std::string& TestMessage::_internal_bytes_value() const {
  return bytes_value_.Get();
}
inline void TestMessage::_internal_set_bytes_value(const std::string& value) {
  
  bytes_value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* TestMessage::_internal_mutable_bytes_value() {
  
  return bytes_value_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* TestMessage::release_bytes_value() {
  // @@protoc_insertion_point(field_release:proto3.TestMessage.bytes_value)
  return bytes_value_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void TestMessage::set_allocated_bytes_value(std::string* bytes_value) {
  if (bytes_value != nullptr) {
    
  } else {
    
  }
  bytes_value_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), bytes_value,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (bytes_value_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    bytes_value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:proto3.TestMessage.bytes_value)
}

// .proto3.EnumType enum_value = 10;
inline void TestMessage::clear_enum_value() {
  enum_value_ = 0;
}
inline ::proto3::EnumType TestMessage::_internal_enum_value() const {
  return static_cast< ::proto3::EnumType >(enum_value_);
}
inline ::proto3::EnumType TestMessage::enum_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.enum_value)
  return _internal_enum_value();
}
inline void TestMessage::_internal_set_enum_value(::proto3::EnumType value) {
  
  enum_value_ = value;
}
inline void TestMessage::set_enum_value(::proto3::EnumType value) {
  _internal_set_enum_value(value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.enum_value)
}

// .proto3.MessageType message_value = 11;
inline bool TestMessage::_internal_has_message_value() const {
  return this != internal_default_instance() && message_value_ != nullptr;
}
inline bool TestMessage::has_message_value() const {
  return _internal_has_message_value();
}
inline void TestMessage::clear_message_value() {
  if (GetArenaForAllocation() == nullptr && message_value_ != nullptr) {
    delete message_value_;
  }
  message_value_ = nullptr;
}
inline const ::proto3::MessageType& TestMessage::_internal_message_value() const {
  const ::proto3::MessageType* p = message_value_;
  return p != nullptr ? *p : reinterpret_cast<const ::proto3::MessageType&>(
      ::proto3::_MessageType_default_instance_);
}
inline const ::proto3::MessageType& TestMessage::message_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.message_value)
  return _internal_message_value();
}
inline void TestMessage::unsafe_arena_set_allocated_message_value(
    ::proto3::MessageType* message_value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(message_value_);
  }
  message_value_ = message_value;
  if (message_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3.TestMessage.message_value)
}
inline ::proto3::MessageType* TestMessage::release_message_value() {
  
  ::proto3::MessageType* temp = message_value_;
  message_value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::proto3::MessageType* TestMessage::unsafe_arena_release_message_value() {
  // @@protoc_insertion_point(field_release:proto3.TestMessage.message_value)
  
  ::proto3::MessageType* temp = message_value_;
  message_value_ = nullptr;
  return temp;
}
inline ::proto3::MessageType* TestMessage::_internal_mutable_message_value() {
  
  if (message_value_ == nullptr) {
    auto* p = CreateMaybeMessage<::proto3::MessageType>(GetArenaForAllocation());
    message_value_ = p;
  }
  return message_value_;
}
inline ::proto3::MessageType* TestMessage::mutable_message_value() {
  ::proto3::MessageType* _msg = _internal_mutable_message_value();
  // @@protoc_insertion_point(field_mutable:proto3.TestMessage.message_value)
  return _msg;
}
inline void TestMessage::set_allocated_message_value(::proto3::MessageType* message_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete message_value_;
  }
  if (message_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::proto3::MessageType>::GetOwningArena(message_value);
    if (message_arena != submessage_arena) {
      message_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, message_value, submessage_arena);
    }
    
  } else {
    
  }
  message_value_ = message_value;
  // @@protoc_insertion_point(field_set_allocated:proto3.TestMessage.message_value)
}

// repeated bool repeated_bool_value = 21;
inline int TestMessage::_internal_repeated_bool_value_size() const {
  return repeated_bool_value_.size();
}
inline int TestMessage::repeated_bool_value_size() const {
  return _internal_repeated_bool_value_size();
}
inline void TestMessage::clear_repeated_bool_value() {
  repeated_bool_value_.Clear();
}
inline bool TestMessage::_internal_repeated_bool_value(int index) const {
  return repeated_bool_value_.Get(index);
}
inline bool TestMessage::repeated_bool_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_bool_value)
  return _internal_repeated_bool_value(index);
}
inline void TestMessage::set_repeated_bool_value(int index, bool value) {
  repeated_bool_value_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_bool_value)
}
inline void TestMessage::_internal_add_repeated_bool_value(bool value) {
  repeated_bool_value_.Add(value);
}
inline void TestMessage::add_repeated_bool_value(bool value) {
  _internal_add_repeated_bool_value(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_bool_value)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
TestMessage::_internal_repeated_bool_value() const {
  return repeated_bool_value_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
TestMessage::repeated_bool_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_bool_value)
  return _internal_repeated_bool_value();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
TestMessage::_internal_mutable_repeated_bool_value() {
  return &repeated_bool_value_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
TestMessage::mutable_repeated_bool_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_bool_value)
  return _internal_mutable_repeated_bool_value();
}

// repeated int32 repeated_int32_value = 22;
inline int TestMessage::_internal_repeated_int32_value_size() const {
  return repeated_int32_value_.size();
}
inline int TestMessage::repeated_int32_value_size() const {
  return _internal_repeated_int32_value_size();
}
inline void TestMessage::clear_repeated_int32_value() {
  repeated_int32_value_.Clear();
}
inline int32_t TestMessage::_internal_repeated_int32_value(int index) const {
  return repeated_int32_value_.Get(index);
}
inline int32_t TestMessage::repeated_int32_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_int32_value)
  return _internal_repeated_int32_value(index);
}
inline void TestMessage::set_repeated_int32_value(int index, int32_t value) {
  repeated_int32_value_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_int32_value)
}
inline void TestMessage::_internal_add_repeated_int32_value(int32_t value) {
  repeated_int32_value_.Add(value);
}
inline void TestMessage::add_repeated_int32_value(int32_t value) {
  _internal_add_repeated_int32_value(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_int32_value)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
TestMessage::_internal_repeated_int32_value() const {
  return repeated_int32_value_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
TestMessage::repeated_int32_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_int32_value)
  return _internal_repeated_int32_value();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
TestMessage::_internal_mutable_repeated_int32_value() {
  return &repeated_int32_value_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
TestMessage::mutable_repeated_int32_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_int32_value)
  return _internal_mutable_repeated_int32_value();
}

// repeated int64 repeated_int64_value = 23;
inline int TestMessage::_internal_repeated_int64_value_size() const {
  return repeated_int64_value_.size();
}
inline int TestMessage::repeated_int64_value_size() const {
  return _internal_repeated_int64_value_size();
}
inline void TestMessage::clear_repeated_int64_value() {
  repeated_int64_value_.Clear();
}
inline int64_t TestMessage::_internal_repeated_int64_value(int index) const {
  return repeated_int64_value_.Get(index);
}
inline int64_t TestMessage::repeated_int64_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_int64_value)
  return _internal_repeated_int64_value(index);
}
inline void TestMessage::set_repeated_int64_value(int index, int64_t value) {
  repeated_int64_value_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_int64_value)
}
inline void TestMessage::_internal_add_repeated_int64_value(int64_t value) {
  repeated_int64_value_.Add(value);
}
inline void TestMessage::add_repeated_int64_value(int64_t value) {
  _internal_add_repeated_int64_value(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_int64_value)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
TestMessage::_internal_repeated_int64_value() const {
  return repeated_int64_value_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
TestMessage::repeated_int64_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_int64_value)
  return _internal_repeated_int64_value();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
TestMessage::_internal_mutable_repeated_int64_value() {
  return &repeated_int64_value_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
TestMessage::mutable_repeated_int64_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_int64_value)
  return _internal_mutable_repeated_int64_value();
}

// repeated uint32 repeated_uint32_value = 24;
inline int TestMessage::_internal_repeated_uint32_value_size() const {
  return repeated_uint32_value_.size();
}
inline int TestMessage::repeated_uint32_value_size() const {
  return _internal_repeated_uint32_value_size();
}
inline void TestMessage::clear_repeated_uint32_value() {
  repeated_uint32_value_.Clear();
}
inline uint32_t TestMessage::_internal_repeated_uint32_value(int index) const {
  return repeated_uint32_value_.Get(index);
}
inline uint32_t TestMessage::repeated_uint32_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_uint32_value)
  return _internal_repeated_uint32_value(index);
}
inline void TestMessage::set_repeated_uint32_value(int index, uint32_t value) {
  repeated_uint32_value_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_uint32_value)
}
inline void TestMessage::_internal_add_repeated_uint32_value(uint32_t value) {
  repeated_uint32_value_.Add(value);
}
inline void TestMessage::add_repeated_uint32_value(uint32_t value) {
  _internal_add_repeated_uint32_value(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_uint32_value)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
TestMessage::_internal_repeated_uint32_value() const {
  return repeated_uint32_value_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
TestMessage::repeated_uint32_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_uint32_value)
  return _internal_repeated_uint32_value();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
TestMessage::_internal_mutable_repeated_uint32_value() {
  return &repeated_uint32_value_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
TestMessage::mutable_repeated_uint32_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_uint32_value)
  return _internal_mutable_repeated_uint32_value();
}

// repeated uint64 repeated_uint64_value = 25;
inline int TestMessage::_internal_repeated_uint64_value_size() const {
  return repeated_uint64_value_.size();
}
inline int TestMessage::repeated_uint64_value_size() const {
  return _internal_repeated_uint64_value_size();
}
inline void TestMessage::clear_repeated_uint64_value() {
  repeated_uint64_value_.Clear();
}
inline uint64_t TestMessage::_internal_repeated_uint64_value(int index) const {
  return repeated_uint64_value_.Get(index);
}
inline uint64_t TestMessage::repeated_uint64_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_uint64_value)
  return _internal_repeated_uint64_value(index);
}
inline void TestMessage::set_repeated_uint64_value(int index, uint64_t value) {
  repeated_uint64_value_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_uint64_value)
}
inline void TestMessage::_internal_add_repeated_uint64_value(uint64_t value) {
  repeated_uint64_value_.Add(value);
}
inline void TestMessage::add_repeated_uint64_value(uint64_t value) {
  _internal_add_repeated_uint64_value(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_uint64_value)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
TestMessage::_internal_repeated_uint64_value() const {
  return repeated_uint64_value_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
TestMessage::repeated_uint64_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_uint64_value)
  return _internal_repeated_uint64_value();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
TestMessage::_internal_mutable_repeated_uint64_value() {
  return &repeated_uint64_value_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
TestMessage::mutable_repeated_uint64_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_uint64_value)
  return _internal_mutable_repeated_uint64_value();
}

// repeated float repeated_float_value = 26;
inline int TestMessage::_internal_repeated_float_value_size() const {
  return repeated_float_value_.size();
}
inline int TestMessage::repeated_float_value_size() const {
  return _internal_repeated_float_value_size();
}
inline void TestMessage::clear_repeated_float_value() {
  repeated_float_value_.Clear();
}
inline float TestMessage::_internal_repeated_float_value(int index) const {
  return repeated_float_value_.Get(index);
}
inline float TestMessage::repeated_float_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_float_value)
  return _internal_repeated_float_value(index);
}
inline void TestMessage::set_repeated_float_value(int index, float value) {
  repeated_float_value_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_float_value)
}
inline void TestMessage::_internal_add_repeated_float_value(float value) {
  repeated_float_value_.Add(value);
}
inline void TestMessage::add_repeated_float_value(float value) {
  _internal_add_repeated_float_value(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_float_value)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
TestMessage::_internal_repeated_float_value() const {
  return repeated_float_value_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
TestMessage::repeated_float_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_float_value)
  return _internal_repeated_float_value();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
TestMessage::_internal_mutable_repeated_float_value() {
  return &repeated_float_value_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
TestMessage::mutable_repeated_float_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_float_value)
  return _internal_mutable_repeated_float_value();
}

// repeated double repeated_double_value = 27;
inline int TestMessage::_internal_repeated_double_value_size() const {
  return repeated_double_value_.size();
}
inline int TestMessage::repeated_double_value_size() const {
  return _internal_repeated_double_value_size();
}
inline void TestMessage::clear_repeated_double_value() {
  repeated_double_value_.Clear();
}
inline double TestMessage::_internal_repeated_double_value(int index) const {
  return repeated_double_value_.Get(index);
}
inline double TestMessage::repeated_double_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_double_value)
  return _internal_repeated_double_value(index);
}
inline void TestMessage::set_repeated_double_value(int index, double value) {
  repeated_double_value_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_double_value)
}
inline void TestMessage::_internal_add_repeated_double_value(double value) {
  repeated_double_value_.Add(value);
}
inline void TestMessage::add_repeated_double_value(double value) {
  _internal_add_repeated_double_value(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_double_value)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
TestMessage::_internal_repeated_double_value() const {
  return repeated_double_value_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
TestMessage::repeated_double_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_double_value)
  return _internal_repeated_double_value();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
TestMessage::_internal_mutable_repeated_double_value() {
  return &repeated_double_value_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
TestMessage::mutable_repeated_double_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_double_value)
  return _internal_mutable_repeated_double_value();
}

// repeated string repeated_string_value = 28;
inline int TestMessage::_internal_repeated_string_value_size() const {
  return repeated_string_value_.size();
}
inline int TestMessage::repeated_string_value_size() const {
  return _internal_repeated_string_value_size();
}
inline void TestMessage::clear_repeated_string_value() {
  repeated_string_value_.Clear();
}
inline std::string* TestMessage::add_repeated_string_value() {
  std::string* _s = _internal_add_repeated_string_value();
  // @@protoc_insertion_point(field_add_mutable:proto3.TestMessage.repeated_string_value)
  return _s;
}
inline const std::string& TestMessage::_internal_repeated_string_value(int index) const {
  return repeated_string_value_.Get(index);
}
inline const std::string& TestMessage::repeated_string_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_string_value)
  return _internal_repeated_string_value(index);
}
inline std::string* TestMessage::mutable_repeated_string_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestMessage.repeated_string_value)
  return repeated_string_value_.Mutable(index);
}
inline void TestMessage::set_repeated_string_value(int index, const std::string& value) {
  repeated_string_value_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_string_value)
}
inline void TestMessage::set_repeated_string_value(int index, std::string&& value) {
  repeated_string_value_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_string_value)
}
inline void TestMessage::set_repeated_string_value(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  repeated_string_value_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:proto3.TestMessage.repeated_string_value)
}
inline void TestMessage::set_repeated_string_value(int index, const char* value, size_t size) {
  repeated_string_value_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:proto3.TestMessage.repeated_string_value)
}
inline std::string* TestMessage::_internal_add_repeated_string_value() {
  return repeated_string_value_.Add();
}
inline void TestMessage::add_repeated_string_value(const std::string& value) {
  repeated_string_value_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_string_value)
}
inline void TestMessage::add_repeated_string_value(std::string&& value) {
  repeated_string_value_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_string_value)
}
inline void TestMessage::add_repeated_string_value(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  repeated_string_value_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:proto3.TestMessage.repeated_string_value)
}
inline void TestMessage::add_repeated_string_value(const char* value, size_t size) {
  repeated_string_value_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:proto3.TestMessage.repeated_string_value)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
TestMessage::repeated_string_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_string_value)
  return repeated_string_value_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
TestMessage::mutable_repeated_string_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_string_value)
  return &repeated_string_value_;
}

// repeated bytes repeated_bytes_value = 29;
inline int TestMessage::_internal_repeated_bytes_value_size() const {
  return repeated_bytes_value_.size();
}
inline int TestMessage::repeated_bytes_value_size() const {
  return _internal_repeated_bytes_value_size();
}
inline void TestMessage::clear_repeated_bytes_value() {
  repeated_bytes_value_.Clear();
}
inline std::string* TestMessage::add_repeated_bytes_value() {
  std::string* _s = _internal_add_repeated_bytes_value();
  // @@protoc_insertion_point(field_add_mutable:proto3.TestMessage.repeated_bytes_value)
  return _s;
}
inline const std::string& TestMessage::_internal_repeated_bytes_value(int index) const {
  return repeated_bytes_value_.Get(index);
}
inline const std::string& TestMessage::repeated_bytes_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_bytes_value)
  return _internal_repeated_bytes_value(index);
}
inline std::string* TestMessage::mutable_repeated_bytes_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestMessage.repeated_bytes_value)
  return repeated_bytes_value_.Mutable(index);
}
inline void TestMessage::set_repeated_bytes_value(int index, const std::string& value) {
  repeated_bytes_value_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_bytes_value)
}
inline void TestMessage::set_repeated_bytes_value(int index, std::string&& value) {
  repeated_bytes_value_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_bytes_value)
}
inline void TestMessage::set_repeated_bytes_value(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  repeated_bytes_value_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:proto3.TestMessage.repeated_bytes_value)
}
inline void TestMessage::set_repeated_bytes_value(int index, const void* value, size_t size) {
  repeated_bytes_value_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:proto3.TestMessage.repeated_bytes_value)
}
inline std::string* TestMessage::_internal_add_repeated_bytes_value() {
  return repeated_bytes_value_.Add();
}
inline void TestMessage::add_repeated_bytes_value(const std::string& value) {
  repeated_bytes_value_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_bytes_value)
}
inline void TestMessage::add_repeated_bytes_value(std::string&& value) {
  repeated_bytes_value_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_bytes_value)
}
inline void TestMessage::add_repeated_bytes_value(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  repeated_bytes_value_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:proto3.TestMessage.repeated_bytes_value)
}
inline void TestMessage::add_repeated_bytes_value(const void* value, size_t size) {
  repeated_bytes_value_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:proto3.TestMessage.repeated_bytes_value)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
TestMessage::repeated_bytes_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_bytes_value)
  return repeated_bytes_value_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
TestMessage::mutable_repeated_bytes_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_bytes_value)
  return &repeated_bytes_value_;
}

// repeated .proto3.EnumType repeated_enum_value = 30;
inline int TestMessage::_internal_repeated_enum_value_size() const {
  return repeated_enum_value_.size();
}
inline int TestMessage::repeated_enum_value_size() const {
  return _internal_repeated_enum_value_size();
}
inline void TestMessage::clear_repeated_enum_value() {
  repeated_enum_value_.Clear();
}
inline ::proto3::EnumType TestMessage::_internal_repeated_enum_value(int index) const {
  return static_cast< ::proto3::EnumType >(repeated_enum_value_.Get(index));
}
inline ::proto3::EnumType TestMessage::repeated_enum_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_enum_value)
  return _internal_repeated_enum_value(index);
}
inline void TestMessage::set_repeated_enum_value(int index, ::proto3::EnumType value) {
  repeated_enum_value_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_enum_value)
}
inline void TestMessage::_internal_add_repeated_enum_value(::proto3::EnumType value) {
  repeated_enum_value_.Add(value);
}
inline void TestMessage::add_repeated_enum_value(::proto3::EnumType value) {
  _internal_add_repeated_enum_value(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_enum_value)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>&
TestMessage::repeated_enum_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_enum_value)
  return repeated_enum_value_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>*
TestMessage::_internal_mutable_repeated_enum_value() {
  return &repeated_enum_value_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>*
TestMessage::mutable_repeated_enum_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_enum_value)
  return _internal_mutable_repeated_enum_value();
}

// repeated .proto3.MessageType repeated_message_value = 31;
inline int TestMessage::_internal_repeated_message_value_size() const {
  return repeated_message_value_.size();
}
inline int TestMessage::repeated_message_value_size() const {
  return _internal_repeated_message_value_size();
}
inline void TestMessage::clear_repeated_message_value() {
  repeated_message_value_.Clear();
}
inline ::proto3::MessageType* TestMessage::mutable_repeated_message_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestMessage.repeated_message_value)
  return repeated_message_value_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::proto3::MessageType >*
TestMessage::mutable_repeated_message_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_message_value)
  return &repeated_message_value_;
}
inline const ::proto3::MessageType& TestMessage::_internal_repeated_message_value(int index) const {
  return repeated_message_value_.Get(index);
}
inline const ::proto3::MessageType& TestMessage::repeated_message_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_message_value)
  return _internal_repeated_message_value(index);
}
inline ::proto3::MessageType* TestMessage::_internal_add_repeated_message_value() {
  return repeated_message_value_.Add();
}
inline ::proto3::MessageType* TestMessage::add_repeated_message_value() {
  ::proto3::MessageType* _add = _internal_add_repeated_message_value();
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_message_value)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::proto3::MessageType >&
TestMessage::repeated_message_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_message_value)
  return repeated_message_value_;
}

// -------------------------------------------------------------------

// TestOneof

// int32 oneof_int32_value = 1;
inline bool TestOneof::_internal_has_oneof_int32_value() const {
  return oneof_value_case() == kOneofInt32Value;
}
inline bool TestOneof::has_oneof_int32_value() const {
  return _internal_has_oneof_int32_value();
}
inline void TestOneof::set_has_oneof_int32_value() {
  _oneof_case_[0] = kOneofInt32Value;
}
inline void TestOneof::clear_oneof_int32_value() {
  if (_internal_has_oneof_int32_value()) {
    oneof_value_.oneof_int32_value_ = 0;
    clear_has_oneof_value();
  }
}
inline int32_t TestOneof::_internal_oneof_int32_value() const {
  if (_internal_has_oneof_int32_value()) {
    return oneof_value_.oneof_int32_value_;
  }
  return 0;
}
inline void TestOneof::_internal_set_oneof_int32_value(int32_t value) {
  if (!_internal_has_oneof_int32_value()) {
    clear_oneof_value();
    set_has_oneof_int32_value();
  }
  oneof_value_.oneof_int32_value_ = value;
}
inline int32_t TestOneof::oneof_int32_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestOneof.oneof_int32_value)
  return _internal_oneof_int32_value();
}
inline void TestOneof::set_oneof_int32_value(int32_t value) {
  _internal_set_oneof_int32_value(value);
  // @@protoc_insertion_point(field_set:proto3.TestOneof.oneof_int32_value)
}

// string oneof_string_value = 2;
inline bool TestOneof::_internal_has_oneof_string_value() const {
  return oneof_value_case() == kOneofStringValue;
}
inline bool TestOneof::has_oneof_string_value() const {
  return _internal_has_oneof_string_value();
}
inline void TestOneof::set_has_oneof_string_value() {
  _oneof_case_[0] = kOneofStringValue;
}
inline void TestOneof::clear_oneof_string_value() {
  if (_internal_has_oneof_string_value()) {
    oneof_value_.oneof_string_value_.Destroy(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
    clear_has_oneof_value();
  }
}
inline const std::string& TestOneof::oneof_string_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestOneof.oneof_string_value)
  return _internal_oneof_string_value();
}
template <typename ArgT0, typename... ArgT>
inline void TestOneof::set_oneof_string_value(ArgT0&& arg0, ArgT... args) {
  if (!_internal_has_oneof_string_value()) {
    clear_oneof_value();
    set_has_oneof_string_value();
    oneof_value_.oneof_string_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  oneof_value_.oneof_string_value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:proto3.TestOneof.oneof_string_value)
}
inline std::string* TestOneof::mutable_oneof_string_value() {
  std::string* _s = _internal_mutable_oneof_string_value();
  // @@protoc_insertion_point(field_mutable:proto3.TestOneof.oneof_string_value)
  return _s;
}
inline const std::string& TestOneof::_internal_oneof_string_value() const {
  if (_internal_has_oneof_string_value()) {
    return oneof_value_.oneof_string_value_.Get();
  }
  return ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void TestOneof::_internal_set_oneof_string_value(const std::string& value) {
  if (!_internal_has_oneof_string_value()) {
    clear_oneof_value();
    set_has_oneof_string_value();
    oneof_value_.oneof_string_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  oneof_value_.oneof_string_value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* TestOneof::_internal_mutable_oneof_string_value() {
  if (!_internal_has_oneof_string_value()) {
    clear_oneof_value();
    set_has_oneof_string_value();
    oneof_value_.oneof_string_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  return oneof_value_.oneof_string_value_.Mutable(
      ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* TestOneof::release_oneof_string_value() {
  // @@protoc_insertion_point(field_release:proto3.TestOneof.oneof_string_value)
  if (_internal_has_oneof_string_value()) {
    clear_has_oneof_value();
    return oneof_value_.oneof_string_value_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
  } else {
    return nullptr;
  }
}
inline void TestOneof::set_allocated_oneof_string_value(std::string* oneof_string_value) {
  if (has_oneof_value()) {
    clear_oneof_value();
  }
  if (oneof_string_value != nullptr) {
    set_has_oneof_string_value();
    oneof_value_.oneof_string_value_.UnsafeSetDefault(oneof_string_value);
    ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArenaForAllocation();
    if (arena != nullptr) {
      arena->Own(oneof_string_value);
    }
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestOneof.oneof_string_value)
}

// bytes oneof_bytes_value = 3;
inline bool TestOneof::_internal_has_oneof_bytes_value() const {
  return oneof_value_case() == kOneofBytesValue;
}
inline bool TestOneof::has_oneof_bytes_value() const {
  return _internal_has_oneof_bytes_value();
}
inline void TestOneof::set_has_oneof_bytes_value() {
  _oneof_case_[0] = kOneofBytesValue;
}
inline void TestOneof::clear_oneof_bytes_value() {
  if (_internal_has_oneof_bytes_value()) {
    oneof_value_.oneof_bytes_value_.Destroy(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
    clear_has_oneof_value();
  }
}
inline const std::string& TestOneof::oneof_bytes_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestOneof.oneof_bytes_value)
  return _internal_oneof_bytes_value();
}
template <typename ArgT0, typename... ArgT>
inline void TestOneof::set_oneof_bytes_value(ArgT0&& arg0, ArgT... args) {
  if (!_internal_has_oneof_bytes_value()) {
    clear_oneof_value();
    set_has_oneof_bytes_value();
    oneof_value_.oneof_bytes_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  oneof_value_.oneof_bytes_value_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:proto3.TestOneof.oneof_bytes_value)
}
inline std::string* TestOneof::mutable_oneof_bytes_value() {
  std::string* _s = _internal_mutable_oneof_bytes_value();
  // @@protoc_insertion_point(field_mutable:proto3.TestOneof.oneof_bytes_value)
  return _s;
}
inline const std::string& TestOneof::_internal_oneof_bytes_value() const {
  if (_internal_has_oneof_bytes_value()) {
    return oneof_value_.oneof_bytes_value_.Get();
  }
  return ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void TestOneof::_internal_set_oneof_bytes_value(const std::string& value) {
  if (!_internal_has_oneof_bytes_value()) {
    clear_oneof_value();
    set_has_oneof_bytes_value();
    oneof_value_.oneof_bytes_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  oneof_value_.oneof_bytes_value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* TestOneof::_internal_mutable_oneof_bytes_value() {
  if (!_internal_has_oneof_bytes_value()) {
    clear_oneof_value();
    set_has_oneof_bytes_value();
    oneof_value_.oneof_bytes_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  return oneof_value_.oneof_bytes_value_.Mutable(
      ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* TestOneof::release_oneof_bytes_value() {
  // @@protoc_insertion_point(field_release:proto3.TestOneof.oneof_bytes_value)
  if (_internal_has_oneof_bytes_value()) {
    clear_has_oneof_value();
    return oneof_value_.oneof_bytes_value_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
  } else {
    return nullptr;
  }
}
inline void TestOneof::set_allocated_oneof_bytes_value(std::string* oneof_bytes_value) {
  if (has_oneof_value()) {
    clear_oneof_value();
  }
  if (oneof_bytes_value != nullptr) {
    set_has_oneof_bytes_value();
    oneof_value_.oneof_bytes_value_.UnsafeSetDefault(oneof_bytes_value);
    ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArenaForAllocation();
    if (arena != nullptr) {
      arena->Own(oneof_bytes_value);
    }
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestOneof.oneof_bytes_value)
}

// .proto3.EnumType oneof_enum_value = 4;
inline bool TestOneof::_internal_has_oneof_enum_value() const {
  return oneof_value_case() == kOneofEnumValue;
}
inline bool TestOneof::has_oneof_enum_value() const {
  return _internal_has_oneof_enum_value();
}
inline void TestOneof::set_has_oneof_enum_value() {
  _oneof_case_[0] = kOneofEnumValue;
}
inline void TestOneof::clear_oneof_enum_value() {
  if (_internal_has_oneof_enum_value()) {
    oneof_value_.oneof_enum_value_ = 0;
    clear_has_oneof_value();
  }
}
inline ::proto3::EnumType TestOneof::_internal_oneof_enum_value() const {
  if (_internal_has_oneof_enum_value()) {
    return static_cast< ::proto3::EnumType >(oneof_value_.oneof_enum_value_);
  }
  return static_cast< ::proto3::EnumType >(0);
}
inline ::proto3::EnumType TestOneof::oneof_enum_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestOneof.oneof_enum_value)
  return _internal_oneof_enum_value();
}
inline void TestOneof::_internal_set_oneof_enum_value(::proto3::EnumType value) {
  if (!_internal_has_oneof_enum_value()) {
    clear_oneof_value();
    set_has_oneof_enum_value();
  }
  oneof_value_.oneof_enum_value_ = value;
}
inline void TestOneof::set_oneof_enum_value(::proto3::EnumType value) {
  _internal_set_oneof_enum_value(value);
  // @@protoc_insertion_point(field_set:proto3.TestOneof.oneof_enum_value)
}

// .proto3.MessageType oneof_message_value = 5;
inline bool TestOneof::_internal_has_oneof_message_value() const {
  return oneof_value_case() == kOneofMessageValue;
}
inline bool TestOneof::has_oneof_message_value() const {
  return _internal_has_oneof_message_value();
}
inline void TestOneof::set_has_oneof_message_value() {
  _oneof_case_[0] = kOneofMessageValue;
}
inline void TestOneof::clear_oneof_message_value() {
  if (_internal_has_oneof_message_value()) {
    if (GetArenaForAllocation() == nullptr) {
      delete oneof_value_.oneof_message_value_;
    }
    clear_has_oneof_value();
  }
}
inline ::proto3::MessageType* TestOneof::release_oneof_message_value() {
  // @@protoc_insertion_point(field_release:proto3.TestOneof.oneof_message_value)
  if (_internal_has_oneof_message_value()) {
    clear_has_oneof_value();
      ::proto3::MessageType* temp = oneof_value_.oneof_message_value_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    oneof_value_.oneof_message_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::proto3::MessageType& TestOneof::_internal_oneof_message_value() const {
  return _internal_has_oneof_message_value()
      ? *oneof_value_.oneof_message_value_
      : reinterpret_cast< ::proto3::MessageType&>(::proto3::_MessageType_default_instance_);
}
inline const ::proto3::MessageType& TestOneof::oneof_message_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestOneof.oneof_message_value)
  return _internal_oneof_message_value();
}
inline ::proto3::MessageType* TestOneof::unsafe_arena_release_oneof_message_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:proto3.TestOneof.oneof_message_value)
  if (_internal_has_oneof_message_value()) {
    clear_has_oneof_value();
    ::proto3::MessageType* temp = oneof_value_.oneof_message_value_;
    oneof_value_.oneof_message_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void TestOneof::unsafe_arena_set_allocated_oneof_message_value(::proto3::MessageType* oneof_message_value) {
  clear_oneof_value();
  if (oneof_message_value) {
    set_has_oneof_message_value();
    oneof_value_.oneof_message_value_ = oneof_message_value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3.TestOneof.oneof_message_value)
}
inline ::proto3::MessageType* TestOneof::_internal_mutable_oneof_message_value() {
  if (!_internal_has_oneof_message_value()) {
    clear_oneof_value();
    set_has_oneof_message_value();
    oneof_value_.oneof_message_value_ = CreateMaybeMessage< ::proto3::MessageType >(GetArenaForAllocation());
  }
  return oneof_value_.oneof_message_value_;
}
inline ::proto3::MessageType* TestOneof::mutable_oneof_message_value() {
  ::proto3::MessageType* _msg = _internal_mutable_oneof_message_value();
  // @@protoc_insertion_point(field_mutable:proto3.TestOneof.oneof_message_value)
  return _msg;
}

// .google.protobuf.NullValue oneof_null_value = 6;
inline bool TestOneof::_internal_has_oneof_null_value() const {
  return oneof_value_case() == kOneofNullValue;
}
inline bool TestOneof::has_oneof_null_value() const {
  return _internal_has_oneof_null_value();
}
inline void TestOneof::set_has_oneof_null_value() {
  _oneof_case_[0] = kOneofNullValue;
}
inline void TestOneof::clear_oneof_null_value() {
  if (_internal_has_oneof_null_value()) {
    oneof_value_.oneof_null_value_ = 0;
    clear_has_oneof_value();
  }
}
inline ::PROTOBUF_NAMESPACE_ID::NullValue TestOneof::_internal_oneof_null_value() const {
  if (_internal_has_oneof_null_value()) {
    return static_cast< ::PROTOBUF_NAMESPACE_ID::NullValue >(oneof_value_.oneof_null_value_);
  }
  return static_cast< ::PROTOBUF_NAMESPACE_ID::NullValue >(0);
}
inline ::PROTOBUF_NAMESPACE_ID::NullValue TestOneof::oneof_null_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestOneof.oneof_null_value)
  return _internal_oneof_null_value();
}
inline void TestOneof::_internal_set_oneof_null_value(::PROTOBUF_NAMESPACE_ID::NullValue value) {
  if (!_internal_has_oneof_null_value()) {
    clear_oneof_value();
    set_has_oneof_null_value();
  }
  oneof_value_.oneof_null_value_ = value;
}
inline void TestOneof::set_oneof_null_value(::PROTOBUF_NAMESPACE_ID::NullValue value) {
  _internal_set_oneof_null_value(value);
  // @@protoc_insertion_point(field_set:proto3.TestOneof.oneof_null_value)
}

inline bool TestOneof::has_oneof_value() const {
  return oneof_value_case() != ONEOF_VALUE_NOT_SET;
}
inline void TestOneof::clear_has_oneof_value() {
  _oneof_case_[0] = ONEOF_VALUE_NOT_SET;
}
inline TestOneof::OneofValueCase TestOneof::oneof_value_case() const {
  return TestOneof::OneofValueCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// TestMap

// map<bool, int32> bool_map = 1;
inline int TestMap::_internal_bool_map_size() const {
  return bool_map_.size();
}
inline int TestMap::bool_map_size() const {
  return _internal_bool_map_size();
}
inline void TestMap::clear_bool_map() {
  bool_map_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >&
TestMap::_internal_bool_map() const {
  return bool_map_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >&
TestMap::bool_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestMap.bool_map)
  return _internal_bool_map();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >*
TestMap::_internal_mutable_bool_map() {
  return bool_map_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >*
TestMap::mutable_bool_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestMap.bool_map)
  return _internal_mutable_bool_map();
}

// map<int32, int32> int32_map = 2;
inline int TestMap::_internal_int32_map_size() const {
  return int32_map_.size();
}
inline int TestMap::int32_map_size() const {
  return _internal_int32_map_size();
}
inline void TestMap::clear_int32_map() {
  int32_map_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int32_t >&
TestMap::_internal_int32_map() const {
  return int32_map_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int32_t >&
TestMap::int32_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestMap.int32_map)
  return _internal_int32_map();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int32_t >*
TestMap::_internal_mutable_int32_map() {
  return int32_map_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int32_t >*
TestMap::mutable_int32_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestMap.int32_map)
  return _internal_mutable_int32_map();
}

// map<int64, int32> int64_map = 3;
inline int TestMap::_internal_int64_map_size() const {
  return int64_map_.size();
}
inline int TestMap::int64_map_size() const {
  return _internal_int64_map_size();
}
inline void TestMap::clear_int64_map() {
  int64_map_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >&
TestMap::_internal_int64_map() const {
  return int64_map_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >&
TestMap::int64_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestMap.int64_map)
  return _internal_int64_map();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >*
TestMap::_internal_mutable_int64_map() {
  return int64_map_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >*
TestMap::mutable_int64_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestMap.int64_map)
  return _internal_mutable_int64_map();
}

// map<uint32, int32> uint32_map = 4;
inline int TestMap::_internal_uint32_map_size() const {
  return uint32_map_.size();
}
inline int TestMap::uint32_map_size() const {
  return _internal_uint32_map_size();
}
inline void TestMap::clear_uint32_map() {
  uint32_map_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, int32_t >&
TestMap::_internal_uint32_map() const {
  return uint32_map_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, int32_t >&
TestMap::uint32_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestMap.uint32_map)
  return _internal_uint32_map();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, int32_t >*
TestMap::_internal_mutable_uint32_map() {
  return uint32_map_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, int32_t >*
TestMap::mutable_uint32_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestMap.uint32_map)
  return _internal_mutable_uint32_map();
}

// map<uint64, int32> uint64_map = 5;
inline int TestMap::_internal_uint64_map_size() const {
  return uint64_map_.size();
}
inline int TestMap::uint64_map_size() const {
  return _internal_uint64_map_size();
}
inline void TestMap::clear_uint64_map() {
  uint64_map_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, int32_t >&
TestMap::_internal_uint64_map() const {
  return uint64_map_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, int32_t >&
TestMap::uint64_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestMap.uint64_map)
  return _internal_uint64_map();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, int32_t >*
TestMap::_internal_mutable_uint64_map() {
  return uint64_map_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, int32_t >*
TestMap::mutable_uint64_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestMap.uint64_map)
  return _internal_mutable_uint64_map();
}

// map<string, int32> string_map = 6;
inline int TestMap::_internal_string_map_size() const {
  return string_map_.size();
}
inline int TestMap::string_map_size() const {
  return _internal_string_map_size();
}
inline void TestMap::clear_string_map() {
  string_map_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >&
TestMap::_internal_string_map() const {
  return string_map_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >&
TestMap::string_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestMap.string_map)
  return _internal_string_map();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >*
TestMap::_internal_mutable_string_map() {
  return string_map_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >*
TestMap::mutable_string_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestMap.string_map)
  return _internal_mutable_string_map();
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// TestNestedMap

// map<bool, int32> bool_map = 1;
inline int TestNestedMap::_internal_bool_map_size() const {
  return bool_map_.size();
}
inline int TestNestedMap::bool_map_size() const {
  return _internal_bool_map_size();
}
inline void TestNestedMap::clear_bool_map() {
  bool_map_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >&
TestNestedMap::_internal_bool_map() const {
  return bool_map_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >&
TestNestedMap::bool_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestNestedMap.bool_map)
  return _internal_bool_map();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >*
TestNestedMap::_internal_mutable_bool_map() {
  return bool_map_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >*
TestNestedMap::mutable_bool_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestNestedMap.bool_map)
  return _internal_mutable_bool_map();
}

// map<int32, int32> int32_map = 2;
inline int TestNestedMap::_internal_int32_map_size() const {
  return int32_map_.size();
}
inline int TestNestedMap::int32_map_size() const {
  return _internal_int32_map_size();
}
inline void TestNestedMap::clear_int32_map() {
  int32_map_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int32_t >&
TestNestedMap::_internal_int32_map() const {
  return int32_map_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int32_t >&
TestNestedMap::int32_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestNestedMap.int32_map)
  return _internal_int32_map();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int32_t >*
TestNestedMap::_internal_mutable_int32_map() {
  return int32_map_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int32_t >*
TestNestedMap::mutable_int32_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestNestedMap.int32_map)
  return _internal_mutable_int32_map();
}

// map<int64, int32> int64_map = 3;
inline int TestNestedMap::_internal_int64_map_size() const {
  return int64_map_.size();
}
inline int TestNestedMap::int64_map_size() const {
  return _internal_int64_map_size();
}
inline void TestNestedMap::clear_int64_map() {
  int64_map_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >&
TestNestedMap::_internal_int64_map() const {
  return int64_map_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >&
TestNestedMap::int64_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestNestedMap.int64_map)
  return _internal_int64_map();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >*
TestNestedMap::_internal_mutable_int64_map() {
  return int64_map_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >*
TestNestedMap::mutable_int64_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestNestedMap.int64_map)
  return _internal_mutable_int64_map();
}

// map<uint32, int32> uint32_map = 4;
inline int TestNestedMap::_internal_uint32_map_size() const {
  return uint32_map_.size();
}
inline int TestNestedMap::uint32_map_size() const {
  return _internal_uint32_map_size();
}
inline void TestNestedMap::clear_uint32_map() {
  uint32_map_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, int32_t >&
TestNestedMap::_internal_uint32_map() const {
  return uint32_map_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, int32_t >&
TestNestedMap::uint32_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestNestedMap.uint32_map)
  return _internal_uint32_map();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, int32_t >*
TestNestedMap::_internal_mutable_uint32_map() {
  return uint32_map_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, int32_t >*
TestNestedMap::mutable_uint32_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestNestedMap.uint32_map)
  return _internal_mutable_uint32_map();
}

// map<uint64, int32> uint64_map = 5;
inline int TestNestedMap::_internal_uint64_map_size() const {
  return uint64_map_.size();
}
inline int TestNestedMap::uint64_map_size() const {
  return _internal_uint64_map_size();
}
inline void TestNestedMap::clear_uint64_map() {
  uint64_map_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, int32_t >&
TestNestedMap::_internal_uint64_map() const {
  return uint64_map_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, int32_t >&
TestNestedMap::uint64_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestNestedMap.uint64_map)
  return _internal_uint64_map();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, int32_t >*
TestNestedMap::_internal_mutable_uint64_map() {
  return uint64_map_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, int32_t >*
TestNestedMap::mutable_uint64_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestNestedMap.uint64_map)
  return _internal_mutable_uint64_map();
}

// map<string, int32> string_map = 6;
inline int TestNestedMap::_internal_string_map_size() const {
  return string_map_.size();
}
inline int TestNestedMap::string_map_size() const {
  return _internal_string_map_size();
}
inline void TestNestedMap::clear_string_map() {
  string_map_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >&
TestNestedMap::_internal_string_map() const {
  return string_map_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >&
TestNestedMap::string_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestNestedMap.string_map)
  return _internal_string_map();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >*
TestNestedMap::_internal_mutable_string_map() {
  return string_map_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >*
TestNestedMap::mutable_string_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestNestedMap.string_map)
  return _internal_mutable_string_map();
}

// map<string, .proto3.TestNestedMap> map_map = 7;
inline int TestNestedMap::_internal_map_map_size() const {
  return map_map_.size();
}
inline int TestNestedMap::map_map_size() const {
  return _internal_map_map_size();
}
inline void TestNestedMap::clear_map_map() {
  map_map_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::proto3::TestNestedMap >&
TestNestedMap::_internal_map_map() const {
  return map_map_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::proto3::TestNestedMap >&
TestNestedMap::map_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestNestedMap.map_map)
  return _internal_map_map();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::proto3::TestNestedMap >*
TestNestedMap::_internal_mutable_map_map() {
  return map_map_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::proto3::TestNestedMap >*
TestNestedMap::mutable_map_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestNestedMap.map_map)
  return _internal_mutable_map_map();
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// TestStringMap

// map<string, string> string_map = 1;
inline int TestStringMap::_internal_string_map_size() const {
  return string_map_.size();
}
inline int TestStringMap::string_map_size() const {
  return _internal_string_map_size();
}
inline void TestStringMap::clear_string_map() {
  string_map_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
TestStringMap::_internal_string_map() const {
  return string_map_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
TestStringMap::string_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestStringMap.string_map)
  return _internal_string_map();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
TestStringMap::_internal_mutable_string_map() {
  return string_map_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
TestStringMap::mutable_string_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestStringMap.string_map)
  return _internal_mutable_string_map();
}

// -------------------------------------------------------------------

// TestWrapper

// .google.protobuf.BoolValue bool_value = 1;
inline bool TestWrapper::_internal_has_bool_value() const {
  return this != internal_default_instance() && bool_value_ != nullptr;
}
inline bool TestWrapper::has_bool_value() const {
  return _internal_has_bool_value();
}
inline const ::PROTOBUF_NAMESPACE_ID::BoolValue& TestWrapper::_internal_bool_value() const {
  const ::PROTOBUF_NAMESPACE_ID::BoolValue* p = bool_value_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::BoolValue&>(
      ::PROTOBUF_NAMESPACE_ID::_BoolValue_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::BoolValue& TestWrapper::bool_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.bool_value)
  return _internal_bool_value();
}
inline void TestWrapper::unsafe_arena_set_allocated_bool_value(
    ::PROTOBUF_NAMESPACE_ID::BoolValue* bool_value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bool_value_);
  }
  bool_value_ = bool_value;
  if (bool_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3.TestWrapper.bool_value)
}
inline ::PROTOBUF_NAMESPACE_ID::BoolValue* TestWrapper::release_bool_value() {
  
  ::PROTOBUF_NAMESPACE_ID::BoolValue* temp = bool_value_;
  bool_value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::BoolValue* TestWrapper::unsafe_arena_release_bool_value() {
  // @@protoc_insertion_point(field_release:proto3.TestWrapper.bool_value)
  
  ::PROTOBUF_NAMESPACE_ID::BoolValue* temp = bool_value_;
  bool_value_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::BoolValue* TestWrapper::_internal_mutable_bool_value() {
  
  if (bool_value_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::BoolValue>(GetArenaForAllocation());
    bool_value_ = p;
  }
  return bool_value_;
}
inline ::PROTOBUF_NAMESPACE_ID::BoolValue* TestWrapper::mutable_bool_value() {
  ::PROTOBUF_NAMESPACE_ID::BoolValue* _msg = _internal_mutable_bool_value();
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.bool_value)
  return _msg;
}
inline void TestWrapper::set_allocated_bool_value(::PROTOBUF_NAMESPACE_ID::BoolValue* bool_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(bool_value_);
  }
  if (bool_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bool_value));
    if (message_arena != submessage_arena) {
      bool_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bool_value, submessage_arena);
    }
    
  } else {
    
  }
  bool_value_ = bool_value;
  // @@protoc_insertion_point(field_set_allocated:proto3.TestWrapper.bool_value)
}

// .google.protobuf.Int32Value int32_value = 2;
inline bool TestWrapper::_internal_has_int32_value() const {
  return this != internal_default_instance() && int32_value_ != nullptr;
}
inline bool TestWrapper::has_int32_value() const {
  return _internal_has_int32_value();
}
inline const ::PROTOBUF_NAMESPACE_ID::Int32Value& TestWrapper::_internal_int32_value() const {
  const ::PROTOBUF_NAMESPACE_ID::Int32Value* p = int32_value_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Int32Value&>(
      ::PROTOBUF_NAMESPACE_ID::_Int32Value_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::Int32Value& TestWrapper::int32_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.int32_value)
  return _internal_int32_value();
}
inline void TestWrapper::unsafe_arena_set_allocated_int32_value(
    ::PROTOBUF_NAMESPACE_ID::Int32Value* int32_value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(int32_value_);
  }
  int32_value_ = int32_value;
  if (int32_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3.TestWrapper.int32_value)
}
inline ::PROTOBUF_NAMESPACE_ID::Int32Value* TestWrapper::release_int32_value() {
  
  ::PROTOBUF_NAMESPACE_ID::Int32Value* temp = int32_value_;
  int32_value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Int32Value* TestWrapper::unsafe_arena_release_int32_value() {
  // @@protoc_insertion_point(field_release:proto3.TestWrapper.int32_value)
  
  ::PROTOBUF_NAMESPACE_ID::Int32Value* temp = int32_value_;
  int32_value_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Int32Value* TestWrapper::_internal_mutable_int32_value() {
  
  if (int32_value_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::Int32Value>(GetArenaForAllocation());
    int32_value_ = p;
  }
  return int32_value_;
}
inline ::PROTOBUF_NAMESPACE_ID::Int32Value* TestWrapper::mutable_int32_value() {
  ::PROTOBUF_NAMESPACE_ID::Int32Value* _msg = _internal_mutable_int32_value();
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.int32_value)
  return _msg;
}
inline void TestWrapper::set_allocated_int32_value(::PROTOBUF_NAMESPACE_ID::Int32Value* int32_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(int32_value_);
  }
  if (int32_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(int32_value));
    if (message_arena != submessage_arena) {
      int32_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, int32_value, submessage_arena);
    }
    
  } else {
    
  }
  int32_value_ = int32_value;
  // @@protoc_insertion_point(field_set_allocated:proto3.TestWrapper.int32_value)
}

// .google.protobuf.Int64Value int64_value = 3;
inline bool TestWrapper::_internal_has_int64_value() const {
  return this != internal_default_instance() && int64_value_ != nullptr;
}
inline bool TestWrapper::has_int64_value() const {
  return _internal_has_int64_value();
}
inline const ::PROTOBUF_NAMESPACE_ID::Int64Value& TestWrapper::_internal_int64_value() const {
  const ::PROTOBUF_NAMESPACE_ID::Int64Value* p = int64_value_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Int64Value&>(
      ::PROTOBUF_NAMESPACE_ID::_Int64Value_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::Int64Value& TestWrapper::int64_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.int64_value)
  return _internal_int64_value();
}
inline void TestWrapper::unsafe_arena_set_allocated_int64_value(
    ::PROTOBUF_NAMESPACE_ID::Int64Value* int64_value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(int64_value_);
  }
  int64_value_ = int64_value;
  if (int64_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3.TestWrapper.int64_value)
}
inline ::PROTOBUF_NAMESPACE_ID::Int64Value* TestWrapper::release_int64_value() {
  
  ::PROTOBUF_NAMESPACE_ID::Int64Value* temp = int64_value_;
  int64_value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Int64Value* TestWrapper::unsafe_arena_release_int64_value() {
  // @@protoc_insertion_point(field_release:proto3.TestWrapper.int64_value)
  
  ::PROTOBUF_NAMESPACE_ID::Int64Value* temp = int64_value_;
  int64_value_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Int64Value* TestWrapper::_internal_mutable_int64_value() {
  
  if (int64_value_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::Int64Value>(GetArenaForAllocation());
    int64_value_ = p;
  }
  return int64_value_;
}
inline ::PROTOBUF_NAMESPACE_ID::Int64Value* TestWrapper::mutable_int64_value() {
  ::PROTOBUF_NAMESPACE_ID::Int64Value* _msg = _internal_mutable_int64_value();
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.int64_value)
  return _msg;
}
inline void TestWrapper::set_allocated_int64_value(::PROTOBUF_NAMESPACE_ID::Int64Value* int64_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(int64_value_);
  }
  if (int64_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(int64_value));
    if (message_arena != submessage_arena) {
      int64_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, int64_value, submessage_arena);
    }
    
  } else {
    
  }
  int64_value_ = int64_value;
  // @@protoc_insertion_point(field_set_allocated:proto3.TestWrapper.int64_value)
}

// .google.protobuf.UInt32Value uint32_value = 4;
inline bool TestWrapper::_internal_has_uint32_value() const {
  return this != internal_default_instance() && uint32_value_ != nullptr;
}
inline bool TestWrapper::has_uint32_value() const {
  return _internal_has_uint32_value();
}
inline const ::PROTOBUF_NAMESPACE_ID::UInt32Value& TestWrapper::_internal_uint32_value() const {
  const ::PROTOBUF_NAMESPACE_ID::UInt32Value* p = uint32_value_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::UInt32Value&>(
      ::PROTOBUF_NAMESPACE_ID::_UInt32Value_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::UInt32Value& TestWrapper::uint32_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.uint32_value)
  return _internal_uint32_value();
}
inline void TestWrapper::unsafe_arena_set_allocated_uint32_value(
    ::PROTOBUF_NAMESPACE_ID::UInt32Value* uint32_value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(uint32_value_);
  }
  uint32_value_ = uint32_value;
  if (uint32_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3.TestWrapper.uint32_value)
}
inline ::PROTOBUF_NAMESPACE_ID::UInt32Value* TestWrapper::release_uint32_value() {
  
  ::PROTOBUF_NAMESPACE_ID::UInt32Value* temp = uint32_value_;
  uint32_value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::UInt32Value* TestWrapper::unsafe_arena_release_uint32_value() {
  // @@protoc_insertion_point(field_release:proto3.TestWrapper.uint32_value)
  
  ::PROTOBUF_NAMESPACE_ID::UInt32Value* temp = uint32_value_;
  uint32_value_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::UInt32Value* TestWrapper::_internal_mutable_uint32_value() {
  
  if (uint32_value_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::UInt32Value>(GetArenaForAllocation());
    uint32_value_ = p;
  }
  return uint32_value_;
}
inline ::PROTOBUF_NAMESPACE_ID::UInt32Value* TestWrapper::mutable_uint32_value() {
  ::PROTOBUF_NAMESPACE_ID::UInt32Value* _msg = _internal_mutable_uint32_value();
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.uint32_value)
  return _msg;
}
inline void TestWrapper::set_allocated_uint32_value(::PROTOBUF_NAMESPACE_ID::UInt32Value* uint32_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(uint32_value_);
  }
  if (uint32_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(uint32_value));
    if (message_arena != submessage_arena) {
      uint32_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, uint32_value, submessage_arena);
    }
    
  } else {
    
  }
  uint32_value_ = uint32_value;
  // @@protoc_insertion_point(field_set_allocated:proto3.TestWrapper.uint32_value)
}

// .google.protobuf.UInt64Value uint64_value = 5;
inline bool TestWrapper::_internal_has_uint64_value() const {
  return this != internal_default_instance() && uint64_value_ != nullptr;
}
inline bool TestWrapper::has_uint64_value() const {
  return _internal_has_uint64_value();
}
inline const ::PROTOBUF_NAMESPACE_ID::UInt64Value& TestWrapper::_internal_uint64_value() const {
  const ::PROTOBUF_NAMESPACE_ID::UInt64Value* p = uint64_value_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::UInt64Value&>(
      ::PROTOBUF_NAMESPACE_ID::_UInt64Value_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::UInt64Value& TestWrapper::uint64_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.uint64_value)
  return _internal_uint64_value();
}
inline void TestWrapper::unsafe_arena_set_allocated_uint64_value(
    ::PROTOBUF_NAMESPACE_ID::UInt64Value* uint64_value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(uint64_value_);
  }
  uint64_value_ = uint64_value;
  if (uint64_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3.TestWrapper.uint64_value)
}
inline ::PROTOBUF_NAMESPACE_ID::UInt64Value* TestWrapper::release_uint64_value() {
  
  ::PROTOBUF_NAMESPACE_ID::UInt64Value* temp = uint64_value_;
  uint64_value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::UInt64Value* TestWrapper::unsafe_arena_release_uint64_value() {
  // @@protoc_insertion_point(field_release:proto3.TestWrapper.uint64_value)
  
  ::PROTOBUF_NAMESPACE_ID::UInt64Value* temp = uint64_value_;
  uint64_value_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::UInt64Value* TestWrapper::_internal_mutable_uint64_value() {
  
  if (uint64_value_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::UInt64Value>(GetArenaForAllocation());
    uint64_value_ = p;
  }
  return uint64_value_;
}
inline ::PROTOBUF_NAMESPACE_ID::UInt64Value* TestWrapper::mutable_uint64_value() {
  ::PROTOBUF_NAMESPACE_ID::UInt64Value* _msg = _internal_mutable_uint64_value();
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.uint64_value)
  return _msg;
}
inline void TestWrapper::set_allocated_uint64_value(::PROTOBUF_NAMESPACE_ID::UInt64Value* uint64_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(uint64_value_);
  }
  if (uint64_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(uint64_value));
    if (message_arena != submessage_arena) {
      uint64_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, uint64_value, submessage_arena);
    }
    
  } else {
    
  }
  uint64_value_ = uint64_value;
  // @@protoc_insertion_point(field_set_allocated:proto3.TestWrapper.uint64_value)
}

// .google.protobuf.FloatValue float_value = 6;
inline bool TestWrapper::_internal_has_float_value() const {
  return this != internal_default_instance() && float_value_ != nullptr;
}
inline bool TestWrapper::has_float_value() const {
  return _internal_has_float_value();
}
inline const ::PROTOBUF_NAMESPACE_ID::FloatValue& TestWrapper::_internal_float_value() const {
  const ::PROTOBUF_NAMESPACE_ID::FloatValue* p = float_value_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::FloatValue&>(
      ::PROTOBUF_NAMESPACE_ID::_FloatValue_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::FloatValue& TestWrapper::float_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.float_value)
  return _internal_float_value();
}
inline void TestWrapper::unsafe_arena_set_allocated_float_value(
    ::PROTOBUF_NAMESPACE_ID::FloatValue* float_value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(float_value_);
  }
  float_value_ = float_value;
  if (float_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3.TestWrapper.float_value)
}
inline ::PROTOBUF_NAMESPACE_ID::FloatValue* TestWrapper::release_float_value() {
  
  ::PROTOBUF_NAMESPACE_ID::FloatValue* temp = float_value_;
  float_value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::FloatValue* TestWrapper::unsafe_arena_release_float_value() {
  // @@protoc_insertion_point(field_release:proto3.TestWrapper.float_value)
  
  ::PROTOBUF_NAMESPACE_ID::FloatValue* temp = float_value_;
  float_value_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::FloatValue* TestWrapper::_internal_mutable_float_value() {
  
  if (float_value_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::FloatValue>(GetArenaForAllocation());
    float_value_ = p;
  }
  return float_value_;
}
inline ::PROTOBUF_NAMESPACE_ID::FloatValue* TestWrapper::mutable_float_value() {
  ::PROTOBUF_NAMESPACE_ID::FloatValue* _msg = _internal_mutable_float_value();
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.float_value)
  return _msg;
}
inline void TestWrapper::set_allocated_float_value(::PROTOBUF_NAMESPACE_ID::FloatValue* float_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(float_value_);
  }
  if (float_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(float_value));
    if (message_arena != submessage_arena) {
      float_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, float_value, submessage_arena);
    }
    
  } else {
    
  }
  float_value_ = float_value;
  // @@protoc_insertion_point(field_set_allocated:proto3.TestWrapper.float_value)
}

// .google.protobuf.DoubleValue double_value = 7;
inline bool TestWrapper::_internal_has_double_value() const {
  return this != internal_default_instance() && double_value_ != nullptr;
}
inline bool TestWrapper::has_double_value() const {
  return _internal_has_double_value();
}
inline const ::PROTOBUF_NAMESPACE_ID::DoubleValue& TestWrapper::_internal_double_value() const {
  const ::PROTOBUF_NAMESPACE_ID::DoubleValue* p = double_value_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::DoubleValue&>(
      ::PROTOBUF_NAMESPACE_ID::_DoubleValue_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::DoubleValue& TestWrapper::double_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.double_value)
  return _internal_double_value();
}
inline void TestWrapper::unsafe_arena_set_allocated_double_value(
    ::PROTOBUF_NAMESPACE_ID::DoubleValue* double_value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(double_value_);
  }
  double_value_ = double_value;
  if (double_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3.TestWrapper.double_value)
}
inline ::PROTOBUF_NAMESPACE_ID::DoubleValue* TestWrapper::release_double_value() {
  
  ::PROTOBUF_NAMESPACE_ID::DoubleValue* temp = double_value_;
  double_value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::DoubleValue* TestWrapper::unsafe_arena_release_double_value() {
  // @@protoc_insertion_point(field_release:proto3.TestWrapper.double_value)
  
  ::PROTOBUF_NAMESPACE_ID::DoubleValue* temp = double_value_;
  double_value_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::DoubleValue* TestWrapper::_internal_mutable_double_value() {
  
  if (double_value_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::DoubleValue>(GetArenaForAllocation());
    double_value_ = p;
  }
  return double_value_;
}
inline ::PROTOBUF_NAMESPACE_ID::DoubleValue* TestWrapper::mutable_double_value() {
  ::PROTOBUF_NAMESPACE_ID::DoubleValue* _msg = _internal_mutable_double_value();
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.double_value)
  return _msg;
}
inline void TestWrapper::set_allocated_double_value(::PROTOBUF_NAMESPACE_ID::DoubleValue* double_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(double_value_);
  }
  if (double_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(double_value));
    if (message_arena != submessage_arena) {
      double_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, double_value, submessage_arena);
    }
    
  } else {
    
  }
  double_value_ = double_value;
  // @@protoc_insertion_point(field_set_allocated:proto3.TestWrapper.double_value)
}

// .google.protobuf.StringValue string_value = 8;
inline bool TestWrapper::_internal_has_string_value() const {
  return this != internal_default_instance() && string_value_ != nullptr;
}
inline bool TestWrapper::has_string_value() const {
  return _internal_has_string_value();
}
inline const ::PROTOBUF_NAMESPACE_ID::StringValue& TestWrapper::_internal_string_value() const {
  const ::PROTOBUF_NAMESPACE_ID::StringValue* p = string_value_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::StringValue&>(
      ::PROTOBUF_NAMESPACE_ID::_StringValue_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::StringValue& TestWrapper::string_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.string_value)
  return _internal_string_value();
}
inline void TestWrapper::unsafe_arena_set_allocated_string_value(
    ::PROTOBUF_NAMESPACE_ID::StringValue* string_value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(string_value_);
  }
  string_value_ = string_value;
  if (string_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3.TestWrapper.string_value)
}
inline ::PROTOBUF_NAMESPACE_ID::StringValue* TestWrapper::release_string_value() {
  
  ::PROTOBUF_NAMESPACE_ID::StringValue* temp = string_value_;
  string_value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::StringValue* TestWrapper::unsafe_arena_release_string_value() {
  // @@protoc_insertion_point(field_release:proto3.TestWrapper.string_value)
  
  ::PROTOBUF_NAMESPACE_ID::StringValue* temp = string_value_;
  string_value_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::StringValue* TestWrapper::_internal_mutable_string_value() {
  
  if (string_value_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::StringValue>(GetArenaForAllocation());
    string_value_ = p;
  }
  return string_value_;
}
inline ::PROTOBUF_NAMESPACE_ID::StringValue* TestWrapper::mutable_string_value() {
  ::PROTOBUF_NAMESPACE_ID::StringValue* _msg = _internal_mutable_string_value();
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.string_value)
  return _msg;
}
inline void TestWrapper::set_allocated_string_value(::PROTOBUF_NAMESPACE_ID::StringValue* string_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(string_value_);
  }
  if (string_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(string_value));
    if (message_arena != submessage_arena) {
      string_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, string_value, submessage_arena);
    }
    
  } else {
    
  }
  string_value_ = string_value;
  // @@protoc_insertion_point(field_set_allocated:proto3.TestWrapper.string_value)
}

// .google.protobuf.BytesValue bytes_value = 9;
inline bool TestWrapper::_internal_has_bytes_value() const {
  return this != internal_default_instance() && bytes_value_ != nullptr;
}
inline bool TestWrapper::has_bytes_value() const {
  return _internal_has_bytes_value();
}
inline const ::PROTOBUF_NAMESPACE_ID::BytesValue& TestWrapper::_internal_bytes_value() const {
  const ::PROTOBUF_NAMESPACE_ID::BytesValue* p = bytes_value_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::BytesValue&>(
      ::PROTOBUF_NAMESPACE_ID::_BytesValue_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::BytesValue& TestWrapper::bytes_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.bytes_value)
  return _internal_bytes_value();
}
inline void TestWrapper::unsafe_arena_set_allocated_bytes_value(
    ::PROTOBUF_NAMESPACE_ID::BytesValue* bytes_value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bytes_value_);
  }
  bytes_value_ = bytes_value;
  if (bytes_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3.TestWrapper.bytes_value)
}
inline ::PROTOBUF_NAMESPACE_ID::BytesValue* TestWrapper::release_bytes_value() {
  
  ::PROTOBUF_NAMESPACE_ID::BytesValue* temp = bytes_value_;
  bytes_value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::BytesValue* TestWrapper::unsafe_arena_release_bytes_value() {
  // @@protoc_insertion_point(field_release:proto3.TestWrapper.bytes_value)
  
  ::PROTOBUF_NAMESPACE_ID::BytesValue* temp = bytes_value_;
  bytes_value_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::BytesValue* TestWrapper::_internal_mutable_bytes_value() {
  
  if (bytes_value_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::BytesValue>(GetArenaForAllocation());
    bytes_value_ = p;
  }
  return bytes_value_;
}
inline ::PROTOBUF_NAMESPACE_ID::BytesValue* TestWrapper::mutable_bytes_value() {
  ::PROTOBUF_NAMESPACE_ID::BytesValue* _msg = _internal_mutable_bytes_value();
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.bytes_value)
  return _msg;
}
inline void TestWrapper::set_allocated_bytes_value(::PROTOBUF_NAMESPACE_ID::BytesValue* bytes_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(bytes_value_);
  }
  if (bytes_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bytes_value));
    if (message_arena != submessage_arena) {
      bytes_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bytes_value, submessage_arena);
    }
    
  } else {
    
  }
  bytes_value_ = bytes_value;
  // @@protoc_insertion_point(field_set_allocated:proto3.TestWrapper.bytes_value)
}

// repeated .google.protobuf.BoolValue repeated_bool_value = 11;
inline int TestWrapper::_internal_repeated_bool_value_size() const {
  return repeated_bool_value_.size();
}
inline int TestWrapper::repeated_bool_value_size() const {
  return _internal_repeated_bool_value_size();
}
inline ::PROTOBUF_NAMESPACE_ID::BoolValue* TestWrapper::mutable_repeated_bool_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.repeated_bool_value)
  return repeated_bool_value_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::BoolValue >*
TestWrapper::mutable_repeated_bool_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestWrapper.repeated_bool_value)
  return &repeated_bool_value_;
}
inline const ::PROTOBUF_NAMESPACE_ID::BoolValue& TestWrapper::_internal_repeated_bool_value(int index) const {
  return repeated_bool_value_.Get(index);
}
inline const ::PROTOBUF_NAMESPACE_ID::BoolValue& TestWrapper::repeated_bool_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.repeated_bool_value)
  return _internal_repeated_bool_value(index);
}
inline ::PROTOBUF_NAMESPACE_ID::BoolValue* TestWrapper::_internal_add_repeated_bool_value() {
  return repeated_bool_value_.Add();
}
inline ::PROTOBUF_NAMESPACE_ID::BoolValue* TestWrapper::add_repeated_bool_value() {
  ::PROTOBUF_NAMESPACE_ID::BoolValue* _add = _internal_add_repeated_bool_value();
  // @@protoc_insertion_point(field_add:proto3.TestWrapper.repeated_bool_value)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::BoolValue >&
TestWrapper::repeated_bool_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestWrapper.repeated_bool_value)
  return repeated_bool_value_;
}

// repeated .google.protobuf.Int32Value repeated_int32_value = 12;
inline int TestWrapper::_internal_repeated_int32_value_size() const {
  return repeated_int32_value_.size();
}
inline int TestWrapper::repeated_int32_value_size() const {
  return _internal_repeated_int32_value_size();
}
inline ::PROTOBUF_NAMESPACE_ID::Int32Value* TestWrapper::mutable_repeated_int32_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.repeated_int32_value)
  return repeated_int32_value_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Int32Value >*
TestWrapper::mutable_repeated_int32_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestWrapper.repeated_int32_value)
  return &repeated_int32_value_;
}
inline const ::PROTOBUF_NAMESPACE_ID::Int32Value& TestWrapper::_internal_repeated_int32_value(int index) const {
  return repeated_int32_value_.Get(index);
}
inline const ::PROTOBUF_NAMESPACE_ID::Int32Value& TestWrapper::repeated_int32_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.repeated_int32_value)
  return _internal_repeated_int32_value(index);
}
inline ::PROTOBUF_NAMESPACE_ID::Int32Value* TestWrapper::_internal_add_repeated_int32_value() {
  return repeated_int32_value_.Add();
}
inline ::PROTOBUF_NAMESPACE_ID::Int32Value* TestWrapper::add_repeated_int32_value() {
  ::PROTOBUF_NAMESPACE_ID::Int32Value* _add = _internal_add_repeated_int32_value();
  // @@protoc_insertion_point(field_add:proto3.TestWrapper.repeated_int32_value)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Int32Value >&
TestWrapper::repeated_int32_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestWrapper.repeated_int32_value)
  return repeated_int32_value_;
}

// repeated .google.protobuf.Int64Value repeated_int64_value = 13;
inline int TestWrapper::_internal_repeated_int64_value_size() const {
  return repeated_int64_value_.size();
}
inline int TestWrapper::repeated_int64_value_size() const {
  return _internal_repeated_int64_value_size();
}
inline ::PROTOBUF_NAMESPACE_ID::Int64Value* TestWrapper::mutable_repeated_int64_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.repeated_int64_value)
  return repeated_int64_value_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Int64Value >*
TestWrapper::mutable_repeated_int64_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestWrapper.repeated_int64_value)
  return &repeated_int64_value_;
}
inline const ::PROTOBUF_NAMESPACE_ID::Int64Value& TestWrapper::_internal_repeated_int64_value(int index) const {
  return repeated_int64_value_.Get(index);
}
inline const ::PROTOBUF_NAMESPACE_ID::Int64Value& TestWrapper::repeated_int64_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.repeated_int64_value)
  return _internal_repeated_int64_value(index);
}
inline ::PROTOBUF_NAMESPACE_ID::Int64Value* TestWrapper::_internal_add_repeated_int64_value() {
  return repeated_int64_value_.Add();
}
inline ::PROTOBUF_NAMESPACE_ID::Int64Value* TestWrapper::add_repeated_int64_value() {
  ::PROTOBUF_NAMESPACE_ID::Int64Value* _add = _internal_add_repeated_int64_value();
  // @@protoc_insertion_point(field_add:proto3.TestWrapper.repeated_int64_value)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Int64Value >&
TestWrapper::repeated_int64_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestWrapper.repeated_int64_value)
  return repeated_int64_value_;
}

// repeated .google.protobuf.UInt32Value repeated_uint32_value = 14;
inline int TestWrapper::_internal_repeated_uint32_value_size() const {
  return repeated_uint32_value_.size();
}
inline int TestWrapper::repeated_uint32_value_size() const {
  return _internal_repeated_uint32_value_size();
}
inline ::PROTOBUF_NAMESPACE_ID::UInt32Value* TestWrapper::mutable_repeated_uint32_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.repeated_uint32_value)
  return repeated_uint32_value_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::UInt32Value >*
TestWrapper::mutable_repeated_uint32_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestWrapper.repeated_uint32_value)
  return &repeated_uint32_value_;
}
inline const ::PROTOBUF_NAMESPACE_ID::UInt32Value& TestWrapper::_internal_repeated_uint32_value(int index) const {
  return repeated_uint32_value_.Get(index);
}
inline const ::PROTOBUF_NAMESPACE_ID::UInt32Value& TestWrapper::repeated_uint32_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.repeated_uint32_value)
  return _internal_repeated_uint32_value(index);
}
inline ::PROTOBUF_NAMESPACE_ID::UInt32Value* TestWrapper::_internal_add_repeated_uint32_value() {
  return repeated_uint32_value_.Add();
}
inline ::PROTOBUF_NAMESPACE_ID::UInt32Value* TestWrapper::add_repeated_uint32_value() {
  ::PROTOBUF_NAMESPACE_ID::UInt32Value* _add = _internal_add_repeated_uint32_value();
  // @@protoc_insertion_point(field_add:proto3.TestWrapper.repeated_uint32_value)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::UInt32Value >&
TestWrapper::repeated_uint32_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestWrapper.repeated_uint32_value)
  return repeated_uint32_value_;
}

// repeated .google.protobuf.UInt64Value repeated_uint64_value = 15;
inline int TestWrapper::_internal_repeated_uint64_value_size() const {
  return repeated_uint64_value_.size();
}
inline int TestWrapper::repeated_uint64_value_size() const {
  return _internal_repeated_uint64_value_size();
}
inline ::PROTOBUF_NAMESPACE_ID::UInt64Value* TestWrapper::mutable_repeated_uint64_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.repeated_uint64_value)
  return repeated_uint64_value_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::UInt64Value >*
TestWrapper::mutable_repeated_uint64_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestWrapper.repeated_uint64_value)
  return &repeated_uint64_value_;
}
inline const ::PROTOBUF_NAMESPACE_ID::UInt64Value& TestWrapper::_internal_repeated_uint64_value(int index) const {
  return repeated_uint64_value_.Get(index);
}
inline const ::PROTOBUF_NAMESPACE_ID::UInt64Value& TestWrapper::repeated_uint64_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.repeated_uint64_value)
  return _internal_repeated_uint64_value(index);
}
inline ::PROTOBUF_NAMESPACE_ID::UInt64Value* TestWrapper::_internal_add_repeated_uint64_value() {
  return repeated_uint64_value_.Add();
}
inline ::PROTOBUF_NAMESPACE_ID::UInt64Value* TestWrapper::add_repeated_uint64_value() {
  ::PROTOBUF_NAMESPACE_ID::UInt64Value* _add = _internal_add_repeated_uint64_value();
  // @@protoc_insertion_point(field_add:proto3.TestWrapper.repeated_uint64_value)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::UInt64Value >&
TestWrapper::repeated_uint64_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestWrapper.repeated_uint64_value)
  return repeated_uint64_value_;
}

// repeated .google.protobuf.FloatValue repeated_float_value = 16;
inline int TestWrapper::_internal_repeated_float_value_size() const {
  return repeated_float_value_.size();
}
inline int TestWrapper::repeated_float_value_size() const {
  return _internal_repeated_float_value_size();
}
inline ::PROTOBUF_NAMESPACE_ID::FloatValue* TestWrapper::mutable_repeated_float_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.repeated_float_value)
  return repeated_float_value_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::FloatValue >*
TestWrapper::mutable_repeated_float_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestWrapper.repeated_float_value)
  return &repeated_float_value_;
}
inline const ::PROTOBUF_NAMESPACE_ID::FloatValue& TestWrapper::_internal_repeated_float_value(int index) const {
  return repeated_float_value_.Get(index);
}
inline const ::PROTOBUF_NAMESPACE_ID::FloatValue& TestWrapper::repeated_float_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.repeated_float_value)
  return _internal_repeated_float_value(index);
}
inline ::PROTOBUF_NAMESPACE_ID::FloatValue* TestWrapper::_internal_add_repeated_float_value() {
  return repeated_float_value_.Add();
}
inline ::PROTOBUF_NAMESPACE_ID::FloatValue* TestWrapper::add_repeated_float_value() {
  ::PROTOBUF_NAMESPACE_ID::FloatValue* _add = _internal_add_repeated_float_value();
  // @@protoc_insertion_point(field_add:proto3.TestWrapper.repeated_float_value)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::FloatValue >&
TestWrapper::repeated_float_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestWrapper.repeated_float_value)
  return repeated_float_value_;
}

// repeated .google.protobuf.DoubleValue repeated_double_value = 17;
inline int TestWrapper::_internal_repeated_double_value_size() const {
  return repeated_double_value_.size();
}
inline int TestWrapper::repeated_double_value_size() const {
  return _internal_repeated_double_value_size();
}
inline ::PROTOBUF_NAMESPACE_ID::DoubleValue* TestWrapper::mutable_repeated_double_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.repeated_double_value)
  return repeated_double_value_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::DoubleValue >*
TestWrapper::mutable_repeated_double_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestWrapper.repeated_double_value)
  return &repeated_double_value_;
}
inline const ::PROTOBUF_NAMESPACE_ID::DoubleValue& TestWrapper::_internal_repeated_double_value(int index) const {
  return repeated_double_value_.Get(index);
}
inline const ::PROTOBUF_NAMESPACE_ID::DoubleValue& TestWrapper::repeated_double_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.repeated_double_value)
  return _internal_repeated_double_value(index);
}
inline ::PROTOBUF_NAMESPACE_ID::DoubleValue* TestWrapper::_internal_add_repeated_double_value() {
  return repeated_double_value_.Add();
}
inline ::PROTOBUF_NAMESPACE_ID::DoubleValue* TestWrapper::add_repeated_double_value() {
  ::PROTOBUF_NAMESPACE_ID::DoubleValue* _add = _internal_add_repeated_double_value();
  // @@protoc_insertion_point(field_add:proto3.TestWrapper.repeated_double_value)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::DoubleValue >&
TestWrapper::repeated_double_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestWrapper.repeated_double_value)
  return repeated_double_value_;
}

// repeated .google.protobuf.StringValue repeated_string_value = 18;
inline int TestWrapper::_internal_repeated_string_value_size() const {
  return repeated_string_value_.size();
}
inline int TestWrapper::repeated_string_value_size() const {
  return _internal_repeated_string_value_size();
}
inline ::PROTOBUF_NAMESPACE_ID::StringValue* TestWrapper::mutable_repeated_string_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.repeated_string_value)
  return repeated_string_value_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::StringValue >*
TestWrapper::mutable_repeated_string_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestWrapper.repeated_string_value)
  return &repeated_string_value_;
}
inline const ::PROTOBUF_NAMESPACE_ID::StringValue& TestWrapper::_internal_repeated_string_value(int index) const {
  return repeated_string_value_.Get(index);
}
inline const ::PROTOBUF_NAMESPACE_ID::StringValue& TestWrapper::repeated_string_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.repeated_string_value)
  return _internal_repeated_string_value(index);
}
inline ::PROTOBUF_NAMESPACE_ID::StringValue* TestWrapper::_internal_add_repeated_string_value() {
  return repeated_string_value_.Add();
}
inline ::PROTOBUF_NAMESPACE_ID::StringValue* TestWrapper::add_repeated_string_value() {
  ::PROTOBUF_NAMESPACE_ID::StringValue* _add = _internal_add_repeated_string_value();
  // @@protoc_insertion_point(field_add:proto3.TestWrapper.repeated_string_value)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::StringValue >&
TestWrapper::repeated_string_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestWrapper.repeated_string_value)
  return repeated_string_value_;
}

// repeated .google.protobuf.BytesValue repeated_bytes_value = 19;
inline int TestWrapper::_internal_repeated_bytes_value_size() const {
  return repeated_bytes_value_.size();
}
inline int TestWrapper::repeated_bytes_value_size() const {
  return _internal_repeated_bytes_value_size();
}
inline ::PROTOBUF_NAMESPACE_ID::BytesValue* TestWrapper::mutable_repeated_bytes_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.repeated_bytes_value)
  return repeated_bytes_value_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::BytesValue >*
TestWrapper::mutable_repeated_bytes_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestWrapper.repeated_bytes_value)
  return &repeated_bytes_value_;
}
inline const ::PROTOBUF_NAMESPACE_ID::BytesValue& TestWrapper::_internal_repeated_bytes_value(int index) const {
  return repeated_bytes_value_.Get(index);
}
inline const ::PROTOBUF_NAMESPACE_ID::BytesValue& TestWrapper::repeated_bytes_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.repeated_bytes_value)
  return _internal_repeated_bytes_value(index);
}
inline ::PROTOBUF_NAMESPACE_ID::BytesValue* TestWrapper::_internal_add_repeated_bytes_value() {
  return repeated_bytes_value_.Add();
}
inline ::PROTOBUF_NAMESPACE_ID::BytesValue* TestWrapper::add_repeated_bytes_value() {
  ::PROTOBUF_NAMESPACE_ID::BytesValue* _add = _internal_add_repeated_bytes_value();
  // @@protoc_insertion_point(field_add:proto3.TestWrapper.repeated_bytes_value)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::BytesValue >&
TestWrapper::repeated_bytes_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestWrapper.repeated_bytes_value)
  return repeated_bytes_value_;
}

// -------------------------------------------------------------------

// TestTimestamp

// .google.protobuf.Timestamp value = 1;
inline bool TestTimestamp::_internal_has_value() const {
  return this != internal_default_instance() && value_ != nullptr;
}
inline bool TestTimestamp::has_value() const {
  return _internal_has_value();
}
inline const ::PROTOBUF_NAMESPACE_ID::Timestamp& TestTimestamp::_internal_value() const {
  const ::PROTOBUF_NAMESPACE_ID::Timestamp* p = value_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Timestamp&>(
      ::PROTOBUF_NAMESPACE_ID::_Timestamp_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::Timestamp& TestTimestamp::value() const {
  // @@protoc_insertion_point(field_get:proto3.TestTimestamp.value)
  return _internal_value();
}
inline void TestTimestamp::unsafe_arena_set_allocated_value(
    ::PROTOBUF_NAMESPACE_ID::Timestamp* value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(value_);
  }
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3.TestTimestamp.value)
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* TestTimestamp::release_value() {
  
  ::PROTOBUF_NAMESPACE_ID::Timestamp* temp = value_;
  value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* TestTimestamp::unsafe_arena_release_value() {
  // @@protoc_insertion_point(field_release:proto3.TestTimestamp.value)
  
  ::PROTOBUF_NAMESPACE_ID::Timestamp* temp = value_;
  value_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* TestTimestamp::_internal_mutable_value() {
  
  if (value_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::Timestamp>(GetArenaForAllocation());
    value_ = p;
  }
  return value_;
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* TestTimestamp::mutable_value() {
  ::PROTOBUF_NAMESPACE_ID::Timestamp* _msg = _internal_mutable_value();
  // @@protoc_insertion_point(field_mutable:proto3.TestTimestamp.value)
  return _msg;
}
inline void TestTimestamp::set_allocated_value(::PROTOBUF_NAMESPACE_ID::Timestamp* value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(value_);
  }
  if (value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(value));
    if (message_arena != submessage_arena) {
      value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, value, submessage_arena);
    }
    
  } else {
    
  }
  value_ = value;
  // @@protoc_insertion_point(field_set_allocated:proto3.TestTimestamp.value)
}

// repeated .google.protobuf.Timestamp repeated_value = 2;
inline int TestTimestamp::_internal_repeated_value_size() const {
  return repeated_value_.size();
}
inline int TestTimestamp::repeated_value_size() const {
  return _internal_repeated_value_size();
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* TestTimestamp::mutable_repeated_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestTimestamp.repeated_value)
  return repeated_value_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Timestamp >*
TestTimestamp::mutable_repeated_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestTimestamp.repeated_value)
  return &repeated_value_;
}
inline const ::PROTOBUF_NAMESPACE_ID::Timestamp& TestTimestamp::_internal_repeated_value(int index) const {
  return repeated_value_.Get(index);
}
inline const ::PROTOBUF_NAMESPACE_ID::Timestamp& TestTimestamp::repeated_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestTimestamp.repeated_value)
  return _internal_repeated_value(index);
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* TestTimestamp::_internal_add_repeated_value() {
  return repeated_value_.Add();
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* TestTimestamp::add_repeated_value() {
  ::PROTOBUF_NAMESPACE_ID::Timestamp* _add = _internal_add_repeated_value();
  // @@protoc_insertion_point(field_add:proto3.TestTimestamp.repeated_value)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Timestamp >&
TestTimestamp::repeated_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestTimestamp.repeated_value)
  return repeated_value_;
}

// -------------------------------------------------------------------

// TestDuration

// .google.protobuf.Duration value = 1;
inline bool TestDuration::_internal_has_value() const {
  return this != internal_default_instance() && value_ != nullptr;
}
inline bool TestDuration::has_value() const {
  return _internal_has_value();
}
inline const ::PROTOBUF_NAMESPACE_ID::Duration& TestDuration::_internal_value() const {
  const ::PROTOBUF_NAMESPACE_ID::Duration* p = value_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Duration&>(
      ::PROTOBUF_NAMESPACE_ID::_Duration_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::Duration& TestDuration::value() const {
  // @@protoc_insertion_point(field_get:proto3.TestDuration.value)
  return _internal_value();
}
inline void TestDuration::unsafe_arena_set_allocated_value(
    ::PROTOBUF_NAMESPACE_ID::Duration* value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(value_);
  }
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3.TestDuration.value)
}
inline ::PROTOBUF_NAMESPACE_ID::Duration* TestDuration::release_value() {
  
  ::PROTOBUF_NAMESPACE_ID::Duration* temp = value_;
  value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Duration* TestDuration::unsafe_arena_release_value() {
  // @@protoc_insertion_point(field_release:proto3.TestDuration.value)
  
  ::PROTOBUF_NAMESPACE_ID::Duration* temp = value_;
  value_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Duration* TestDuration::_internal_mutable_value() {
  
  if (value_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::Duration>(GetArenaForAllocation());
    value_ = p;
  }
  return value_;
}
inline ::PROTOBUF_NAMESPACE_ID::Duration* TestDuration::mutable_value() {
  ::PROTOBUF_NAMESPACE_ID::Duration* _msg = _internal_mutable_value();
  // @@protoc_insertion_point(field_mutable:proto3.TestDuration.value)
  return _msg;
}
inline void TestDuration::set_allocated_value(::PROTOBUF_NAMESPACE_ID::Duration* value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(value_);
  }
  if (value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(value));
    if (message_arena != submessage_arena) {
      value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, value, submessage_arena);
    }
    
  } else {
    
  }
  value_ = value;
  // @@protoc_insertion_point(field_set_allocated:proto3.TestDuration.value)
}

// repeated .google.protobuf.Duration repeated_value = 2;
inline int TestDuration::_internal_repeated_value_size() const {
  return repeated_value_.size();
}
inline int TestDuration::repeated_value_size() const {
  return _internal_repeated_value_size();
}
inline ::PROTOBUF_NAMESPACE_ID::Duration* TestDuration::mutable_repeated_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestDuration.repeated_value)
  return repeated_value_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Duration >*
TestDuration::mutable_repeated_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestDuration.repeated_value)
  return &repeated_value_;
}
inline const ::PROTOBUF_NAMESPACE_ID::Duration& TestDuration::_internal_repeated_value(int index) const {
  return repeated_value_.Get(index);
}
inline const ::PROTOBUF_NAMESPACE_ID::Duration& TestDuration::repeated_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestDuration.repeated_value)
  return _internal_repeated_value(index);
}
inline ::PROTOBUF_NAMESPACE_ID::Duration* TestDuration::_internal_add_repeated_value() {
  return repeated_value_.Add();
}
inline ::PROTOBUF_NAMESPACE_ID::Duration* TestDuration::add_repeated_value() {
  ::PROTOBUF_NAMESPACE_ID::Duration* _add = _internal_add_repeated_value();
  // @@protoc_insertion_point(field_add:proto3.TestDuration.repeated_value)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Duration >&
TestDuration::repeated_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestDuration.repeated_value)
  return repeated_value_;
}

// -------------------------------------------------------------------

// TestFieldMask

// .google.protobuf.FieldMask value = 1;
inline bool TestFieldMask::_internal_has_value() const {
  return this != internal_default_instance() && value_ != nullptr;
}
inline bool TestFieldMask::has_value() const {
  return _internal_has_value();
}
inline const ::PROTOBUF_NAMESPACE_ID::FieldMask& TestFieldMask::_internal_value() const {
  const ::PROTOBUF_NAMESPACE_ID::FieldMask* p = value_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::FieldMask&>(
      ::PROTOBUF_NAMESPACE_ID::_FieldMask_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::FieldMask& TestFieldMask::value() const {
  // @@protoc_insertion_point(field_get:proto3.TestFieldMask.value)
  return _internal_value();
}
inline void TestFieldMask::unsafe_arena_set_allocated_value(
    ::PROTOBUF_NAMESPACE_ID::FieldMask* value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(value_);
  }
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3.TestFieldMask.value)
}
inline ::PROTOBUF_NAMESPACE_ID::FieldMask* TestFieldMask::release_value() {
  
  ::PROTOBUF_NAMESPACE_ID::FieldMask* temp = value_;
  value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::FieldMask* TestFieldMask::unsafe_arena_release_value() {
  // @@protoc_insertion_point(field_release:proto3.TestFieldMask.value)
  
  ::PROTOBUF_NAMESPACE_ID::FieldMask* temp = value_;
  value_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::FieldMask* TestFieldMask::_internal_mutable_value() {
  
  if (value_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::FieldMask>(GetArenaForAllocation());
    value_ = p;
  }
  return value_;
}
inline ::PROTOBUF_NAMESPACE_ID::FieldMask* TestFieldMask::mutable_value() {
  ::PROTOBUF_NAMESPACE_ID::FieldMask* _msg = _internal_mutable_value();
  // @@protoc_insertion_point(field_mutable:proto3.TestFieldMask.value)
  return _msg;
}
inline void TestFieldMask::set_allocated_value(::PROTOBUF_NAMESPACE_ID::FieldMask* value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(value_);
  }
  if (value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(value));
    if (message_arena != submessage_arena) {
      value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, value, submessage_arena);
    }
    
  } else {
    
  }
  value_ = value;
  // @@protoc_insertion_point(field_set_allocated:proto3.TestFieldMask.value)
}

// -------------------------------------------------------------------

// TestStruct

// .google.protobuf.Struct value = 1;
inline bool TestStruct::_internal_has_value() const {
  return this != internal_default_instance() && value_ != nullptr;
}
inline bool TestStruct::has_value() const {
  return _internal_has_value();
}
inline const ::PROTOBUF_NAMESPACE_ID::Struct& TestStruct::_internal_value() const {
  const ::PROTOBUF_NAMESPACE_ID::Struct* p = value_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Struct&>(
      ::PROTOBUF_NAMESPACE_ID::_Struct_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::Struct& TestStruct::value() const {
  // @@protoc_insertion_point(field_get:proto3.TestStruct.value)
  return _internal_value();
}
inline void TestStruct::unsafe_arena_set_allocated_value(
    ::PROTOBUF_NAMESPACE_ID::Struct* value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(value_);
  }
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3.TestStruct.value)
}
inline ::PROTOBUF_NAMESPACE_ID::Struct* TestStruct::release_value() {
  
  ::PROTOBUF_NAMESPACE_ID::Struct* temp = value_;
  value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Struct* TestStruct::unsafe_arena_release_value() {
  // @@protoc_insertion_point(field_release:proto3.TestStruct.value)
  
  ::PROTOBUF_NAMESPACE_ID::Struct* temp = value_;
  value_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Struct* TestStruct::_internal_mutable_value() {
  
  if (value_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::Struct>(GetArenaForAllocation());
    value_ = p;
  }
  return value_;
}
inline ::PROTOBUF_NAMESPACE_ID::Struct* TestStruct::mutable_value() {
  ::PROTOBUF_NAMESPACE_ID::Struct* _msg = _internal_mutable_value();
  // @@protoc_insertion_point(field_mutable:proto3.TestStruct.value)
  return _msg;
}
inline void TestStruct::set_allocated_value(::PROTOBUF_NAMESPACE_ID::Struct* value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(value_);
  }
  if (value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(value));
    if (message_arena != submessage_arena) {
      value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, value, submessage_arena);
    }
    
  } else {
    
  }
  value_ = value;
  // @@protoc_insertion_point(field_set_allocated:proto3.TestStruct.value)
}

// repeated .google.protobuf.Struct repeated_value = 2;
inline int TestStruct::_internal_repeated_value_size() const {
  return repeated_value_.size();
}
inline int TestStruct::repeated_value_size() const {
  return _internal_repeated_value_size();
}
inline ::PROTOBUF_NAMESPACE_ID::Struct* TestStruct::mutable_repeated_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestStruct.repeated_value)
  return repeated_value_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Struct >*
TestStruct::mutable_repeated_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestStruct.repeated_value)
  return &repeated_value_;
}
inline const ::PROTOBUF_NAMESPACE_ID::Struct& TestStruct::_internal_repeated_value(int index) const {
  return repeated_value_.Get(index);
}
inline const ::PROTOBUF_NAMESPACE_ID::Struct& TestStruct::repeated_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestStruct.repeated_value)
  return _internal_repeated_value(index);
}
inline ::PROTOBUF_NAMESPACE_ID::Struct* TestStruct::_internal_add_repeated_value() {
  return repeated_value_.Add();
}
inline ::PROTOBUF_NAMESPACE_ID::Struct* TestStruct::add_repeated_value() {
  ::PROTOBUF_NAMESPACE_ID::Struct* _add = _internal_add_repeated_value();
  // @@protoc_insertion_point(field_add:proto3.TestStruct.repeated_value)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Struct >&
TestStruct::repeated_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestStruct.repeated_value)
  return repeated_value_;
}

// -------------------------------------------------------------------

// TestAny

// .google.protobuf.Any value = 1;
inline bool TestAny::_internal_has_value() const {
  return this != internal_default_instance() && value_ != nullptr;
}
inline bool TestAny::has_value() const {
  return _internal_has_value();
}
inline const ::PROTOBUF_NAMESPACE_ID::Any& TestAny::_internal_value() const {
  const ::PROTOBUF_NAMESPACE_ID::Any* p = value_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Any&>(
      ::PROTOBUF_NAMESPACE_ID::_Any_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::Any& TestAny::value() const {
  // @@protoc_insertion_point(field_get:proto3.TestAny.value)
  return _internal_value();
}
inline void TestAny::unsafe_arena_set_allocated_value(
    ::PROTOBUF_NAMESPACE_ID::Any* value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(value_);
  }
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3.TestAny.value)
}
inline ::PROTOBUF_NAMESPACE_ID::Any* TestAny::release_value() {
  
  ::PROTOBUF_NAMESPACE_ID::Any* temp = value_;
  value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Any* TestAny::unsafe_arena_release_value() {
  // @@protoc_insertion_point(field_release:proto3.TestAny.value)
  
  ::PROTOBUF_NAMESPACE_ID::Any* temp = value_;
  value_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Any* TestAny::_internal_mutable_value() {
  
  if (value_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::Any>(GetArenaForAllocation());
    value_ = p;
  }
  return value_;
}
inline ::PROTOBUF_NAMESPACE_ID::Any* TestAny::mutable_value() {
  ::PROTOBUF_NAMESPACE_ID::Any* _msg = _internal_mutable_value();
  // @@protoc_insertion_point(field_mutable:proto3.TestAny.value)
  return _msg;
}
inline void TestAny::set_allocated_value(::PROTOBUF_NAMESPACE_ID::Any* value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(value_);
  }
  if (value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(value));
    if (message_arena != submessage_arena) {
      value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, value, submessage_arena);
    }
    
  } else {
    
  }
  value_ = value;
  // @@protoc_insertion_point(field_set_allocated:proto3.TestAny.value)
}

// repeated .google.protobuf.Any repeated_value = 2;
inline int TestAny::_internal_repeated_value_size() const {
  return repeated_value_.size();
}
inline int TestAny::repeated_value_size() const {
  return _internal_repeated_value_size();
}
inline ::PROTOBUF_NAMESPACE_ID::Any* TestAny::mutable_repeated_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestAny.repeated_value)
  return repeated_value_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Any >*
TestAny::mutable_repeated_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestAny.repeated_value)
  return &repeated_value_;
}
inline const ::PROTOBUF_NAMESPACE_ID::Any& TestAny::_internal_repeated_value(int index) const {
  return repeated_value_.Get(index);
}
inline const ::PROTOBUF_NAMESPACE_ID::Any& TestAny::repeated_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestAny.repeated_value)
  return _internal_repeated_value(index);
}
inline ::PROTOBUF_NAMESPACE_ID::Any* TestAny::_internal_add_repeated_value() {
  return repeated_value_.Add();
}
inline ::PROTOBUF_NAMESPACE_ID::Any* TestAny::add_repeated_value() {
  ::PROTOBUF_NAMESPACE_ID::Any* _add = _internal_add_repeated_value();
  // @@protoc_insertion_point(field_add:proto3.TestAny.repeated_value)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Any >&
TestAny::repeated_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestAny.repeated_value)
  return repeated_value_;
}

// -------------------------------------------------------------------

// TestValue

// .google.protobuf.Value value = 1;
inline bool TestValue::_internal_has_value() const {
  return this != internal_default_instance() && value_ != nullptr;
}
inline bool TestValue::has_value() const {
  return _internal_has_value();
}
inline const ::PROTOBUF_NAMESPACE_ID::Value& TestValue::_internal_value() const {
  const ::PROTOBUF_NAMESPACE_ID::Value* p = value_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Value&>(
      ::PROTOBUF_NAMESPACE_ID::_Value_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::Value& TestValue::value() const {
  // @@protoc_insertion_point(field_get:proto3.TestValue.value)
  return _internal_value();
}
inline void TestValue::unsafe_arena_set_allocated_value(
    ::PROTOBUF_NAMESPACE_ID::Value* value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(value_);
  }
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3.TestValue.value)
}
inline ::PROTOBUF_NAMESPACE_ID::Value* TestValue::release_value() {
  
  ::PROTOBUF_NAMESPACE_ID::Value* temp = value_;
  value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Value* TestValue::unsafe_arena_release_value() {
  // @@protoc_insertion_point(field_release:proto3.TestValue.value)
  
  ::PROTOBUF_NAMESPACE_ID::Value* temp = value_;
  value_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Value* TestValue::_internal_mutable_value() {
  
  if (value_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::Value>(GetArenaForAllocation());
    value_ = p;
  }
  return value_;
}
inline ::PROTOBUF_NAMESPACE_ID::Value* TestValue::mutable_value() {
  ::PROTOBUF_NAMESPACE_ID::Value* _msg = _internal_mutable_value();
  // @@protoc_insertion_point(field_mutable:proto3.TestValue.value)
  return _msg;
}
inline void TestValue::set_allocated_value(::PROTOBUF_NAMESPACE_ID::Value* value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(value_);
  }
  if (value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(value));
    if (message_arena != submessage_arena) {
      value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, value, submessage_arena);
    }
    
  } else {
    
  }
  value_ = value;
  // @@protoc_insertion_point(field_set_allocated:proto3.TestValue.value)
}

// repeated .google.protobuf.Value repeated_value = 2;
inline int TestValue::_internal_repeated_value_size() const {
  return repeated_value_.size();
}
inline int TestValue::repeated_value_size() const {
  return _internal_repeated_value_size();
}
inline ::PROTOBUF_NAMESPACE_ID::Value* TestValue::mutable_repeated_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestValue.repeated_value)
  return repeated_value_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Value >*
TestValue::mutable_repeated_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestValue.repeated_value)
  return &repeated_value_;
}
inline const ::PROTOBUF_NAMESPACE_ID::Value& TestValue::_internal_repeated_value(int index) const {
  return repeated_value_.Get(index);
}
inline const ::PROTOBUF_NAMESPACE_ID::Value& TestValue::repeated_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestValue.repeated_value)
  return _internal_repeated_value(index);
}
inline ::PROTOBUF_NAMESPACE_ID::Value* TestValue::_internal_add_repeated_value() {
  return repeated_value_.Add();
}
inline ::PROTOBUF_NAMESPACE_ID::Value* TestValue::add_repeated_value() {
  ::PROTOBUF_NAMESPACE_ID::Value* _add = _internal_add_repeated_value();
  // @@protoc_insertion_point(field_add:proto3.TestValue.repeated_value)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Value >&
TestValue::repeated_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestValue.repeated_value)
  return repeated_value_;
}

// -------------------------------------------------------------------

// TestListValue

// .google.protobuf.ListValue value = 1;
inline bool TestListValue::_internal_has_value() const {
  return this != internal_default_instance() && value_ != nullptr;
}
inline bool TestListValue::has_value() const {
  return _internal_has_value();
}
inline const ::PROTOBUF_NAMESPACE_ID::ListValue& TestListValue::_internal_value() const {
  const ::PROTOBUF_NAMESPACE_ID::ListValue* p = value_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::ListValue&>(
      ::PROTOBUF_NAMESPACE_ID::_ListValue_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::ListValue& TestListValue::value() const {
  // @@protoc_insertion_point(field_get:proto3.TestListValue.value)
  return _internal_value();
}
inline void TestListValue::unsafe_arena_set_allocated_value(
    ::PROTOBUF_NAMESPACE_ID::ListValue* value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(value_);
  }
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3.TestListValue.value)
}
inline ::PROTOBUF_NAMESPACE_ID::ListValue* TestListValue::release_value() {
  
  ::PROTOBUF_NAMESPACE_ID::ListValue* temp = value_;
  value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::ListValue* TestListValue::unsafe_arena_release_value() {
  // @@protoc_insertion_point(field_release:proto3.TestListValue.value)
  
  ::PROTOBUF_NAMESPACE_ID::ListValue* temp = value_;
  value_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::ListValue* TestListValue::_internal_mutable_value() {
  
  if (value_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::ListValue>(GetArenaForAllocation());
    value_ = p;
  }
  return value_;
}
inline ::PROTOBUF_NAMESPACE_ID::ListValue* TestListValue::mutable_value() {
  ::PROTOBUF_NAMESPACE_ID::ListValue* _msg = _internal_mutable_value();
  // @@protoc_insertion_point(field_mutable:proto3.TestListValue.value)
  return _msg;
}
inline void TestListValue::set_allocated_value(::PROTOBUF_NAMESPACE_ID::ListValue* value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(value_);
  }
  if (value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(value));
    if (message_arena != submessage_arena) {
      value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, value, submessage_arena);
    }
    
  } else {
    
  }
  value_ = value;
  // @@protoc_insertion_point(field_set_allocated:proto3.TestListValue.value)
}

// repeated .google.protobuf.ListValue repeated_value = 2;
inline int TestListValue::_internal_repeated_value_size() const {
  return repeated_value_.size();
}
inline int TestListValue::repeated_value_size() const {
  return _internal_repeated_value_size();
}
inline ::PROTOBUF_NAMESPACE_ID::ListValue* TestListValue::mutable_repeated_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestListValue.repeated_value)
  return repeated_value_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::ListValue >*
TestListValue::mutable_repeated_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestListValue.repeated_value)
  return &repeated_value_;
}
inline const ::PROTOBUF_NAMESPACE_ID::ListValue& TestListValue::_internal_repeated_value(int index) const {
  return repeated_value_.Get(index);
}
inline const ::PROTOBUF_NAMESPACE_ID::ListValue& TestListValue::repeated_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestListValue.repeated_value)
  return _internal_repeated_value(index);
}
inline ::PROTOBUF_NAMESPACE_ID::ListValue* TestListValue::_internal_add_repeated_value() {
  return repeated_value_.Add();
}
inline ::PROTOBUF_NAMESPACE_ID::ListValue* TestListValue::add_repeated_value() {
  ::PROTOBUF_NAMESPACE_ID::ListValue* _add = _internal_add_repeated_value();
  // @@protoc_insertion_point(field_add:proto3.TestListValue.repeated_value)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::ListValue >&
TestListValue::repeated_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestListValue.repeated_value)
  return repeated_value_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// TestBoolValue

// bool bool_value = 1;
inline void TestBoolValue::clear_bool_value() {
  bool_value_ = false;
}
inline bool TestBoolValue::_internal_bool_value() const {
  return bool_value_;
}
inline bool TestBoolValue::bool_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestBoolValue.bool_value)
  return _internal_bool_value();
}
inline void TestBoolValue::_internal_set_bool_value(bool value) {
  
  bool_value_ = value;
}
inline void TestBoolValue::set_bool_value(bool value) {
  _internal_set_bool_value(value);
  // @@protoc_insertion_point(field_set:proto3.TestBoolValue.bool_value)
}

// map<bool, int32> bool_map = 2;
inline int TestBoolValue::_internal_bool_map_size() const {
  return bool_map_.size();
}
inline int TestBoolValue::bool_map_size() const {
  return _internal_bool_map_size();
}
inline void TestBoolValue::clear_bool_map() {
  bool_map_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >&
TestBoolValue::_internal_bool_map() const {
  return bool_map_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >&
TestBoolValue::bool_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestBoolValue.bool_map)
  return _internal_bool_map();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >*
TestBoolValue::_internal_mutable_bool_map() {
  return bool_map_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >*
TestBoolValue::mutable_bool_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestBoolValue.bool_map)
  return _internal_mutable_bool_map();
}

// -------------------------------------------------------------------

// TestCustomJsonName

// int32 value = 1 [json_name = "@value"];
inline void TestCustomJsonName::clear_value() {
  value_ = 0;
}
inline int32_t TestCustomJsonName::_internal_value() const {
  return value_;
}
inline int32_t TestCustomJsonName::value() const {
  // @@protoc_insertion_point(field_get:proto3.TestCustomJsonName.value)
  return _internal_value();
}
inline void TestCustomJsonName::_internal_set_value(int32_t value) {
  
  value_ = value;
}
inline void TestCustomJsonName::set_value(int32_t value) {
  _internal_set_value(value);
  // @@protoc_insertion_point(field_set:proto3.TestCustomJsonName.value)
}

// -------------------------------------------------------------------

// TestExtensions

// .protobuf_unittest.TestAllExtensions extensions = 1;
inline bool TestExtensions::_internal_has_extensions() const {
  return this != internal_default_instance() && extensions_ != nullptr;
}
inline bool TestExtensions::has_extensions() const {
  return _internal_has_extensions();
}
inline const ::protobuf_unittest::TestAllExtensions& TestExtensions::_internal_extensions() const {
  const ::protobuf_unittest::TestAllExtensions* p = extensions_;
  return p != nullptr ? *p : reinterpret_cast<const ::protobuf_unittest::TestAllExtensions&>(
      ::protobuf_unittest::_TestAllExtensions_default_instance_);
}
inline const ::protobuf_unittest::TestAllExtensions& TestExtensions::extensions() const {
  // @@protoc_insertion_point(field_get:proto3.TestExtensions.extensions)
  return _internal_extensions();
}
inline void TestExtensions::unsafe_arena_set_allocated_extensions(
    ::protobuf_unittest::TestAllExtensions* extensions) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(extensions_);
  }
  extensions_ = extensions;
  if (extensions) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3.TestExtensions.extensions)
}
inline ::protobuf_unittest::TestAllExtensions* TestExtensions::release_extensions() {
  
  ::protobuf_unittest::TestAllExtensions* temp = extensions_;
  extensions_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::protobuf_unittest::TestAllExtensions* TestExtensions::unsafe_arena_release_extensions() {
  // @@protoc_insertion_point(field_release:proto3.TestExtensions.extensions)
  
  ::protobuf_unittest::TestAllExtensions* temp = extensions_;
  extensions_ = nullptr;
  return temp;
}
inline ::protobuf_unittest::TestAllExtensions* TestExtensions::_internal_mutable_extensions() {
  
  if (extensions_ == nullptr) {
    auto* p = CreateMaybeMessage<::protobuf_unittest::TestAllExtensions>(GetArenaForAllocation());
    extensions_ = p;
  }
  return extensions_;
}
inline ::protobuf_unittest::TestAllExtensions* TestExtensions::mutable_extensions() {
  ::protobuf_unittest::TestAllExtensions* _msg = _internal_mutable_extensions();
  // @@protoc_insertion_point(field_mutable:proto3.TestExtensions.extensions)
  return _msg;
}
inline void TestExtensions::set_allocated_extensions(::protobuf_unittest::TestAllExtensions* extensions) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(extensions_);
  }
  if (extensions) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(extensions));
    if (message_arena != submessage_arena) {
      extensions = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, extensions, submessage_arena);
    }
    
  } else {
    
  }
  extensions_ = extensions;
  // @@protoc_insertion_point(field_set_allocated:proto3.TestExtensions.extensions)
}

// -------------------------------------------------------------------

// TestEnumValue

// .proto3.EnumType enum_value1 = 1;
inline void TestEnumValue::clear_enum_value1() {
  enum_value1_ = 0;
}
inline ::proto3::EnumType TestEnumValue::_internal_enum_value1() const {
  return static_cast< ::proto3::EnumType >(enum_value1_);
}
inline ::proto3::EnumType TestEnumValue::enum_value1() const {
  // @@protoc_insertion_point(field_get:proto3.TestEnumValue.enum_value1)
  return _internal_enum_value1();
}
inline void TestEnumValue::_internal_set_enum_value1(::proto3::EnumType value) {
  
  enum_value1_ = value;
}
inline void TestEnumValue::set_enum_value1(::proto3::EnumType value) {
  _internal_set_enum_value1(value);
  // @@protoc_insertion_point(field_set:proto3.TestEnumValue.enum_value1)
}

// .proto3.EnumType enum_value2 = 2;
inline void TestEnumValue::clear_enum_value2() {
  enum_value2_ = 0;
}
inline ::proto3::EnumType TestEnumValue::_internal_enum_value2() const {
  return static_cast< ::proto3::EnumType >(enum_value2_);
}
inline ::proto3::EnumType TestEnumValue::enum_value2() const {
  // @@protoc_insertion_point(field_get:proto3.TestEnumValue.enum_value2)
  return _internal_enum_value2();
}
inline void TestEnumValue::_internal_set_enum_value2(::proto3::EnumType value) {
  
  enum_value2_ = value;
}
inline void TestEnumValue::set_enum_value2(::proto3::EnumType value) {
  _internal_set_enum_value2(value);
  // @@protoc_insertion_point(field_set:proto3.TestEnumValue.enum_value2)
}

// .proto3.EnumType enum_value3 = 3;
inline void TestEnumValue::clear_enum_value3() {
  enum_value3_ = 0;
}
inline ::proto3::EnumType TestEnumValue::_internal_enum_value3() const {
  return static_cast< ::proto3::EnumType >(enum_value3_);
}
inline ::proto3::EnumType TestEnumValue::enum_value3() const {
  // @@protoc_insertion_point(field_get:proto3.TestEnumValue.enum_value3)
  return _internal_enum_value3();
}
inline void TestEnumValue::_internal_set_enum_value3(::proto3::EnumType value) {
  
  enum_value3_ = value;
}
inline void TestEnumValue::set_enum_value3(::proto3::EnumType value) {
  _internal_set_enum_value3(value);
  // @@protoc_insertion_point(field_set:proto3.TestEnumValue.enum_value3)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace proto3

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::proto3::EnumType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::proto3::EnumType>() {
  return ::proto3::EnumType_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto
