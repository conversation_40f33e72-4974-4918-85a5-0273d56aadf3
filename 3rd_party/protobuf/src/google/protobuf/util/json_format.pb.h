// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/util/json_format.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_google_2fprotobuf_2futil_2fjson_5fformat_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_google_2fprotobuf_2futil_2fjson_5fformat_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019000 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_google_2fprotobuf_2futil_2fjson_5fformat_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[19]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto;
namespace protobuf_unittest {
class TestBase64ByteArrays;
struct TestBase64ByteArraysDefaultTypeInternal;
extern TestBase64ByteArraysDefaultTypeInternal _TestBase64ByteArrays_default_instance_;
class TestBoolMap;
struct TestBoolMapDefaultTypeInternal;
extern TestBoolMapDefaultTypeInternal _TestBoolMap_default_instance_;
class TestBoolMap_BoolMapEntry_DoNotUse;
struct TestBoolMap_BoolMapEntry_DoNotUseDefaultTypeInternal;
extern TestBoolMap_BoolMapEntry_DoNotUseDefaultTypeInternal _TestBoolMap_BoolMapEntry_DoNotUse_default_instance_;
class TestCamelCase;
struct TestCamelCaseDefaultTypeInternal;
extern TestCamelCaseDefaultTypeInternal _TestCamelCase_default_instance_;
class TestDefaultEnumValue;
struct TestDefaultEnumValueDefaultTypeInternal;
extern TestDefaultEnumValueDefaultTypeInternal _TestDefaultEnumValue_default_instance_;
class TestExtension;
struct TestExtensionDefaultTypeInternal;
extern TestExtensionDefaultTypeInternal _TestExtension_default_instance_;
class TestFlagsAndStrings;
struct TestFlagsAndStringsDefaultTypeInternal;
extern TestFlagsAndStringsDefaultTypeInternal _TestFlagsAndStrings_default_instance_;
class TestFlagsAndStrings_RepeatedGroup;
struct TestFlagsAndStrings_RepeatedGroupDefaultTypeInternal;
extern TestFlagsAndStrings_RepeatedGroupDefaultTypeInternal _TestFlagsAndStrings_RepeatedGroup_default_instance_;
class TestJavaScriptJSON;
struct TestJavaScriptJSONDefaultTypeInternal;
extern TestJavaScriptJSONDefaultTypeInternal _TestJavaScriptJSON_default_instance_;
class TestJavaScriptOrderJSON1;
struct TestJavaScriptOrderJSON1DefaultTypeInternal;
extern TestJavaScriptOrderJSON1DefaultTypeInternal _TestJavaScriptOrderJSON1_default_instance_;
class TestJavaScriptOrderJSON2;
struct TestJavaScriptOrderJSON2DefaultTypeInternal;
extern TestJavaScriptOrderJSON2DefaultTypeInternal _TestJavaScriptOrderJSON2_default_instance_;
class TestLargeInt;
struct TestLargeIntDefaultTypeInternal;
extern TestLargeIntDefaultTypeInternal _TestLargeInt_default_instance_;
class TestMessageWithExtension;
struct TestMessageWithExtensionDefaultTypeInternal;
extern TestMessageWithExtensionDefaultTypeInternal _TestMessageWithExtension_default_instance_;
class TestNumbers;
struct TestNumbersDefaultTypeInternal;
extern TestNumbersDefaultTypeInternal _TestNumbers_default_instance_;
class TestRecursion;
struct TestRecursionDefaultTypeInternal;
extern TestRecursionDefaultTypeInternal _TestRecursion_default_instance_;
class TestStringMap;
struct TestStringMapDefaultTypeInternal;
extern TestStringMapDefaultTypeInternal _TestStringMap_default_instance_;
class TestStringMap_StringMapEntry_DoNotUse;
struct TestStringMap_StringMapEntry_DoNotUseDefaultTypeInternal;
extern TestStringMap_StringMapEntry_DoNotUseDefaultTypeInternal _TestStringMap_StringMapEntry_DoNotUse_default_instance_;
class TestStringSerializer;
struct TestStringSerializerDefaultTypeInternal;
extern TestStringSerializerDefaultTypeInternal _TestStringSerializer_default_instance_;
class TestStringSerializer_StringMapEntry_DoNotUse;
struct TestStringSerializer_StringMapEntry_DoNotUseDefaultTypeInternal;
extern TestStringSerializer_StringMapEntry_DoNotUseDefaultTypeInternal _TestStringSerializer_StringMapEntry_DoNotUse_default_instance_;
}  // namespace protobuf_unittest
PROTOBUF_NAMESPACE_OPEN
template<> ::protobuf_unittest::TestBase64ByteArrays* Arena::CreateMaybeMessage<::protobuf_unittest::TestBase64ByteArrays>(Arena*);
template<> ::protobuf_unittest::TestBoolMap* Arena::CreateMaybeMessage<::protobuf_unittest::TestBoolMap>(Arena*);
template<> ::protobuf_unittest::TestBoolMap_BoolMapEntry_DoNotUse* Arena::CreateMaybeMessage<::protobuf_unittest::TestBoolMap_BoolMapEntry_DoNotUse>(Arena*);
template<> ::protobuf_unittest::TestCamelCase* Arena::CreateMaybeMessage<::protobuf_unittest::TestCamelCase>(Arena*);
template<> ::protobuf_unittest::TestDefaultEnumValue* Arena::CreateMaybeMessage<::protobuf_unittest::TestDefaultEnumValue>(Arena*);
template<> ::protobuf_unittest::TestExtension* Arena::CreateMaybeMessage<::protobuf_unittest::TestExtension>(Arena*);
template<> ::protobuf_unittest::TestFlagsAndStrings* Arena::CreateMaybeMessage<::protobuf_unittest::TestFlagsAndStrings>(Arena*);
template<> ::protobuf_unittest::TestFlagsAndStrings_RepeatedGroup* Arena::CreateMaybeMessage<::protobuf_unittest::TestFlagsAndStrings_RepeatedGroup>(Arena*);
template<> ::protobuf_unittest::TestJavaScriptJSON* Arena::CreateMaybeMessage<::protobuf_unittest::TestJavaScriptJSON>(Arena*);
template<> ::protobuf_unittest::TestJavaScriptOrderJSON1* Arena::CreateMaybeMessage<::protobuf_unittest::TestJavaScriptOrderJSON1>(Arena*);
template<> ::protobuf_unittest::TestJavaScriptOrderJSON2* Arena::CreateMaybeMessage<::protobuf_unittest::TestJavaScriptOrderJSON2>(Arena*);
template<> ::protobuf_unittest::TestLargeInt* Arena::CreateMaybeMessage<::protobuf_unittest::TestLargeInt>(Arena*);
template<> ::protobuf_unittest::TestMessageWithExtension* Arena::CreateMaybeMessage<::protobuf_unittest::TestMessageWithExtension>(Arena*);
template<> ::protobuf_unittest::TestNumbers* Arena::CreateMaybeMessage<::protobuf_unittest::TestNumbers>(Arena*);
template<> ::protobuf_unittest::TestRecursion* Arena::CreateMaybeMessage<::protobuf_unittest::TestRecursion>(Arena*);
template<> ::protobuf_unittest::TestStringMap* Arena::CreateMaybeMessage<::protobuf_unittest::TestStringMap>(Arena*);
template<> ::protobuf_unittest::TestStringMap_StringMapEntry_DoNotUse* Arena::CreateMaybeMessage<::protobuf_unittest::TestStringMap_StringMapEntry_DoNotUse>(Arena*);
template<> ::protobuf_unittest::TestStringSerializer* Arena::CreateMaybeMessage<::protobuf_unittest::TestStringSerializer>(Arena*);
template<> ::protobuf_unittest::TestStringSerializer_StringMapEntry_DoNotUse* Arena::CreateMaybeMessage<::protobuf_unittest::TestStringSerializer_StringMapEntry_DoNotUse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace protobuf_unittest {

enum TestNumbers_MyType : int {
  TestNumbers_MyType_OK = 0,
  TestNumbers_MyType_WARNING = 1,
  TestNumbers_MyType_ERROR = 2
};
bool TestNumbers_MyType_IsValid(int value);
constexpr TestNumbers_MyType TestNumbers_MyType_MyType_MIN = TestNumbers_MyType_OK;
constexpr TestNumbers_MyType TestNumbers_MyType_MyType_MAX = TestNumbers_MyType_ERROR;
constexpr int TestNumbers_MyType_MyType_ARRAYSIZE = TestNumbers_MyType_MyType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TestNumbers_MyType_descriptor();
template<typename T>
inline const std::string& TestNumbers_MyType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, TestNumbers_MyType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function TestNumbers_MyType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    TestNumbers_MyType_descriptor(), enum_t_value);
}
inline bool TestNumbers_MyType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, TestNumbers_MyType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<TestNumbers_MyType>(
    TestNumbers_MyType_descriptor(), name, value);
}
enum EnumValue : int {
  PROTOCOL = 0,
  BUFFER = 1,
  DEFAULT = 2
};
bool EnumValue_IsValid(int value);
constexpr EnumValue EnumValue_MIN = PROTOCOL;
constexpr EnumValue EnumValue_MAX = DEFAULT;
constexpr int EnumValue_ARRAYSIZE = EnumValue_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* EnumValue_descriptor();
template<typename T>
inline const std::string& EnumValue_Name(T enum_t_value) {
  static_assert(::std::is_same<T, EnumValue>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function EnumValue_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    EnumValue_descriptor(), enum_t_value);
}
inline bool EnumValue_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, EnumValue* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<EnumValue>(
    EnumValue_descriptor(), name, value);
}
// ===================================================================

class TestFlagsAndStrings_RepeatedGroup final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestFlagsAndStrings.RepeatedGroup) */ {
 public:
  inline TestFlagsAndStrings_RepeatedGroup() : TestFlagsAndStrings_RepeatedGroup(nullptr) {}
  ~TestFlagsAndStrings_RepeatedGroup() override;
  explicit constexpr TestFlagsAndStrings_RepeatedGroup(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestFlagsAndStrings_RepeatedGroup(const TestFlagsAndStrings_RepeatedGroup& from);
  TestFlagsAndStrings_RepeatedGroup(TestFlagsAndStrings_RepeatedGroup&& from) noexcept
    : TestFlagsAndStrings_RepeatedGroup() {
    *this = ::std::move(from);
  }

  inline TestFlagsAndStrings_RepeatedGroup& operator=(const TestFlagsAndStrings_RepeatedGroup& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestFlagsAndStrings_RepeatedGroup& operator=(TestFlagsAndStrings_RepeatedGroup&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestFlagsAndStrings_RepeatedGroup& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestFlagsAndStrings_RepeatedGroup* internal_default_instance() {
    return reinterpret_cast<const TestFlagsAndStrings_RepeatedGroup*>(
               &_TestFlagsAndStrings_RepeatedGroup_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(TestFlagsAndStrings_RepeatedGroup& a, TestFlagsAndStrings_RepeatedGroup& b) {
    a.Swap(&b);
  }
  inline void Swap(TestFlagsAndStrings_RepeatedGroup* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestFlagsAndStrings_RepeatedGroup* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestFlagsAndStrings_RepeatedGroup* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestFlagsAndStrings_RepeatedGroup>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestFlagsAndStrings_RepeatedGroup& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestFlagsAndStrings_RepeatedGroup& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestFlagsAndStrings_RepeatedGroup* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "protobuf_unittest.TestFlagsAndStrings.RepeatedGroup";
  }
  protected:
  explicit TestFlagsAndStrings_RepeatedGroup(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFFieldNumber = 3,
  };
  // required string f = 3;
  bool has_f() const;
  private:
  bool _internal_has_f() const;
  public:
  void clear_f();
  const std::string& f() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_f(ArgT0&& arg0, ArgT... args);
  std::string* mutable_f();
  PROTOBUF_NODISCARD std::string* release_f();
  void set_allocated_f(std::string* f);
  private:
  const std::string& _internal_f() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_f(const std::string& value);
  std::string* _internal_mutable_f();
  public:

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestFlagsAndStrings.RepeatedGroup)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr f_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_2eproto;
};
// -------------------------------------------------------------------

class TestFlagsAndStrings final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestFlagsAndStrings) */ {
 public:
  inline TestFlagsAndStrings() : TestFlagsAndStrings(nullptr) {}
  ~TestFlagsAndStrings() override;
  explicit constexpr TestFlagsAndStrings(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestFlagsAndStrings(const TestFlagsAndStrings& from);
  TestFlagsAndStrings(TestFlagsAndStrings&& from) noexcept
    : TestFlagsAndStrings() {
    *this = ::std::move(from);
  }

  inline TestFlagsAndStrings& operator=(const TestFlagsAndStrings& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestFlagsAndStrings& operator=(TestFlagsAndStrings&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestFlagsAndStrings& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestFlagsAndStrings* internal_default_instance() {
    return reinterpret_cast<const TestFlagsAndStrings*>(
               &_TestFlagsAndStrings_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(TestFlagsAndStrings& a, TestFlagsAndStrings& b) {
    a.Swap(&b);
  }
  inline void Swap(TestFlagsAndStrings* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestFlagsAndStrings* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestFlagsAndStrings* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestFlagsAndStrings>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestFlagsAndStrings& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestFlagsAndStrings& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestFlagsAndStrings* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "protobuf_unittest.TestFlagsAndStrings";
  }
  protected:
  explicit TestFlagsAndStrings(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef TestFlagsAndStrings_RepeatedGroup RepeatedGroup;

  // accessors -------------------------------------------------------

  enum : int {
    kRepeatedgroupFieldNumber = 2,
    kAFieldNumber = 1,
  };
  // repeated group RepeatedGroup = 2 { ... };
  int repeatedgroup_size() const;
  private:
  int _internal_repeatedgroup_size() const;
  public:
  void clear_repeatedgroup();
  ::protobuf_unittest::TestFlagsAndStrings_RepeatedGroup* mutable_repeatedgroup(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::protobuf_unittest::TestFlagsAndStrings_RepeatedGroup >*
      mutable_repeatedgroup();
  private:
  const ::protobuf_unittest::TestFlagsAndStrings_RepeatedGroup& _internal_repeatedgroup(int index) const;
  ::protobuf_unittest::TestFlagsAndStrings_RepeatedGroup* _internal_add_repeatedgroup();
  public:
  const ::protobuf_unittest::TestFlagsAndStrings_RepeatedGroup& repeatedgroup(int index) const;
  ::protobuf_unittest::TestFlagsAndStrings_RepeatedGroup* add_repeatedgroup();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::protobuf_unittest::TestFlagsAndStrings_RepeatedGroup >&
      repeatedgroup() const;

  // required int32 A = 1;
  bool has_a() const;
  private:
  bool _internal_has_a() const;
  public:
  void clear_a();
  int32_t a() const;
  void set_a(int32_t value);
  private:
  int32_t _internal_a() const;
  void _internal_set_a(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestFlagsAndStrings)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::protobuf_unittest::TestFlagsAndStrings_RepeatedGroup > repeatedgroup_;
  int32_t a_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_2eproto;
};
// -------------------------------------------------------------------

class TestBase64ByteArrays final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestBase64ByteArrays) */ {
 public:
  inline TestBase64ByteArrays() : TestBase64ByteArrays(nullptr) {}
  ~TestBase64ByteArrays() override;
  explicit constexpr TestBase64ByteArrays(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestBase64ByteArrays(const TestBase64ByteArrays& from);
  TestBase64ByteArrays(TestBase64ByteArrays&& from) noexcept
    : TestBase64ByteArrays() {
    *this = ::std::move(from);
  }

  inline TestBase64ByteArrays& operator=(const TestBase64ByteArrays& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestBase64ByteArrays& operator=(TestBase64ByteArrays&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestBase64ByteArrays& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestBase64ByteArrays* internal_default_instance() {
    return reinterpret_cast<const TestBase64ByteArrays*>(
               &_TestBase64ByteArrays_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(TestBase64ByteArrays& a, TestBase64ByteArrays& b) {
    a.Swap(&b);
  }
  inline void Swap(TestBase64ByteArrays* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestBase64ByteArrays* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestBase64ByteArrays* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestBase64ByteArrays>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestBase64ByteArrays& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestBase64ByteArrays& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestBase64ByteArrays* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "protobuf_unittest.TestBase64ByteArrays";
  }
  protected:
  explicit TestBase64ByteArrays(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAFieldNumber = 1,
  };
  // required bytes a = 1;
  bool has_a() const;
  private:
  bool _internal_has_a() const;
  public:
  void clear_a();
  const std::string& a() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_a(ArgT0&& arg0, ArgT... args);
  std::string* mutable_a();
  PROTOBUF_NODISCARD std::string* release_a();
  void set_allocated_a(std::string* a);
  private:
  const std::string& _internal_a() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_a(const std::string& value);
  std::string* _internal_mutable_a();
  public:

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestBase64ByteArrays)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr a_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_2eproto;
};
// -------------------------------------------------------------------

class TestJavaScriptJSON final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestJavaScriptJSON) */ {
 public:
  inline TestJavaScriptJSON() : TestJavaScriptJSON(nullptr) {}
  ~TestJavaScriptJSON() override;
  explicit constexpr TestJavaScriptJSON(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestJavaScriptJSON(const TestJavaScriptJSON& from);
  TestJavaScriptJSON(TestJavaScriptJSON&& from) noexcept
    : TestJavaScriptJSON() {
    *this = ::std::move(from);
  }

  inline TestJavaScriptJSON& operator=(const TestJavaScriptJSON& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestJavaScriptJSON& operator=(TestJavaScriptJSON&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestJavaScriptJSON& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestJavaScriptJSON* internal_default_instance() {
    return reinterpret_cast<const TestJavaScriptJSON*>(
               &_TestJavaScriptJSON_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(TestJavaScriptJSON& a, TestJavaScriptJSON& b) {
    a.Swap(&b);
  }
  inline void Swap(TestJavaScriptJSON* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestJavaScriptJSON* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestJavaScriptJSON* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestJavaScriptJSON>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestJavaScriptJSON& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestJavaScriptJSON& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestJavaScriptJSON* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "protobuf_unittest.TestJavaScriptJSON";
  }
  protected:
  explicit TestJavaScriptJSON(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInFieldNumber = 3,
    kVarFieldNumber = 4,
    kAFieldNumber = 1,
    kFinalFieldNumber = 2,
  };
  // optional string in = 3;
  bool has_in() const;
  private:
  bool _internal_has_in() const;
  public:
  void clear_in();
  const std::string& in() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_in(ArgT0&& arg0, ArgT... args);
  std::string* mutable_in();
  PROTOBUF_NODISCARD std::string* release_in();
  void set_allocated_in(std::string* in);
  private:
  const std::string& _internal_in() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_in(const std::string& value);
  std::string* _internal_mutable_in();
  public:

  // optional string Var = 4;
  bool has_var() const;
  private:
  bool _internal_has_var() const;
  public:
  void clear_var();
  const std::string& var() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_var(ArgT0&& arg0, ArgT... args);
  std::string* mutable_var();
  PROTOBUF_NODISCARD std::string* release_var();
  void set_allocated_var(std::string* var);
  private:
  const std::string& _internal_var() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_var(const std::string& value);
  std::string* _internal_mutable_var();
  public:

  // optional int32 a = 1;
  bool has_a() const;
  private:
  bool _internal_has_a() const;
  public:
  void clear_a();
  int32_t a() const;
  void set_a(int32_t value);
  private:
  int32_t _internal_a() const;
  void _internal_set_a(int32_t value);
  public:

  // optional float final = 2;
  bool has_final() const;
  private:
  bool _internal_has_final() const;
  public:
  void clear_final();
  float final() const;
  void set_final(float value);
  private:
  float _internal_final() const;
  void _internal_set_final(float value);
  public:

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestJavaScriptJSON)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr in_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr var_;
  int32_t a_;
  float final_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_2eproto;
};
// -------------------------------------------------------------------

class TestJavaScriptOrderJSON1 final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestJavaScriptOrderJSON1) */ {
 public:
  inline TestJavaScriptOrderJSON1() : TestJavaScriptOrderJSON1(nullptr) {}
  ~TestJavaScriptOrderJSON1() override;
  explicit constexpr TestJavaScriptOrderJSON1(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestJavaScriptOrderJSON1(const TestJavaScriptOrderJSON1& from);
  TestJavaScriptOrderJSON1(TestJavaScriptOrderJSON1&& from) noexcept
    : TestJavaScriptOrderJSON1() {
    *this = ::std::move(from);
  }

  inline TestJavaScriptOrderJSON1& operator=(const TestJavaScriptOrderJSON1& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestJavaScriptOrderJSON1& operator=(TestJavaScriptOrderJSON1&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestJavaScriptOrderJSON1& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestJavaScriptOrderJSON1* internal_default_instance() {
    return reinterpret_cast<const TestJavaScriptOrderJSON1*>(
               &_TestJavaScriptOrderJSON1_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(TestJavaScriptOrderJSON1& a, TestJavaScriptOrderJSON1& b) {
    a.Swap(&b);
  }
  inline void Swap(TestJavaScriptOrderJSON1* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestJavaScriptOrderJSON1* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestJavaScriptOrderJSON1* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestJavaScriptOrderJSON1>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestJavaScriptOrderJSON1& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestJavaScriptOrderJSON1& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestJavaScriptOrderJSON1* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "protobuf_unittest.TestJavaScriptOrderJSON1";
  }
  protected:
  explicit TestJavaScriptOrderJSON1(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDFieldNumber = 1,
    kCFieldNumber = 2,
    kXFieldNumber = 3,
    kBFieldNumber = 4,
    kAFieldNumber = 5,
  };
  // optional int32 d = 1;
  bool has_d() const;
  private:
  bool _internal_has_d() const;
  public:
  void clear_d();
  int32_t d() const;
  void set_d(int32_t value);
  private:
  int32_t _internal_d() const;
  void _internal_set_d(int32_t value);
  public:

  // optional int32 c = 2;
  bool has_c() const;
  private:
  bool _internal_has_c() const;
  public:
  void clear_c();
  int32_t c() const;
  void set_c(int32_t value);
  private:
  int32_t _internal_c() const;
  void _internal_set_c(int32_t value);
  public:

  // optional bool x = 3;
  bool has_x() const;
  private:
  bool _internal_has_x() const;
  public:
  void clear_x();
  bool x() const;
  void set_x(bool value);
  private:
  bool _internal_x() const;
  void _internal_set_x(bool value);
  public:

  // optional int32 b = 4;
  bool has_b() const;
  private:
  bool _internal_has_b() const;
  public:
  void clear_b();
  int32_t b() const;
  void set_b(int32_t value);
  private:
  int32_t _internal_b() const;
  void _internal_set_b(int32_t value);
  public:

  // optional int32 a = 5;
  bool has_a() const;
  private:
  bool _internal_has_a() const;
  public:
  void clear_a();
  int32_t a() const;
  void set_a(int32_t value);
  private:
  int32_t _internal_a() const;
  void _internal_set_a(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestJavaScriptOrderJSON1)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  int32_t d_;
  int32_t c_;
  bool x_;
  int32_t b_;
  int32_t a_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_2eproto;
};
// -------------------------------------------------------------------

class TestJavaScriptOrderJSON2 final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestJavaScriptOrderJSON2) */ {
 public:
  inline TestJavaScriptOrderJSON2() : TestJavaScriptOrderJSON2(nullptr) {}
  ~TestJavaScriptOrderJSON2() override;
  explicit constexpr TestJavaScriptOrderJSON2(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestJavaScriptOrderJSON2(const TestJavaScriptOrderJSON2& from);
  TestJavaScriptOrderJSON2(TestJavaScriptOrderJSON2&& from) noexcept
    : TestJavaScriptOrderJSON2() {
    *this = ::std::move(from);
  }

  inline TestJavaScriptOrderJSON2& operator=(const TestJavaScriptOrderJSON2& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestJavaScriptOrderJSON2& operator=(TestJavaScriptOrderJSON2&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestJavaScriptOrderJSON2& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestJavaScriptOrderJSON2* internal_default_instance() {
    return reinterpret_cast<const TestJavaScriptOrderJSON2*>(
               &_TestJavaScriptOrderJSON2_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(TestJavaScriptOrderJSON2& a, TestJavaScriptOrderJSON2& b) {
    a.Swap(&b);
  }
  inline void Swap(TestJavaScriptOrderJSON2* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestJavaScriptOrderJSON2* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestJavaScriptOrderJSON2* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestJavaScriptOrderJSON2>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestJavaScriptOrderJSON2& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestJavaScriptOrderJSON2& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestJavaScriptOrderJSON2* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "protobuf_unittest.TestJavaScriptOrderJSON2";
  }
  protected:
  explicit TestJavaScriptOrderJSON2(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kZFieldNumber = 6,
    kDFieldNumber = 1,
    kCFieldNumber = 2,
    kXFieldNumber = 3,
    kBFieldNumber = 4,
    kAFieldNumber = 5,
  };
  // repeated .protobuf_unittest.TestJavaScriptOrderJSON1 z = 6;
  int z_size() const;
  private:
  int _internal_z_size() const;
  public:
  void clear_z();
  ::protobuf_unittest::TestJavaScriptOrderJSON1* mutable_z(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::protobuf_unittest::TestJavaScriptOrderJSON1 >*
      mutable_z();
  private:
  const ::protobuf_unittest::TestJavaScriptOrderJSON1& _internal_z(int index) const;
  ::protobuf_unittest::TestJavaScriptOrderJSON1* _internal_add_z();
  public:
  const ::protobuf_unittest::TestJavaScriptOrderJSON1& z(int index) const;
  ::protobuf_unittest::TestJavaScriptOrderJSON1* add_z();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::protobuf_unittest::TestJavaScriptOrderJSON1 >&
      z() const;

  // optional int32 d = 1;
  bool has_d() const;
  private:
  bool _internal_has_d() const;
  public:
  void clear_d();
  int32_t d() const;
  void set_d(int32_t value);
  private:
  int32_t _internal_d() const;
  void _internal_set_d(int32_t value);
  public:

  // optional int32 c = 2;
  bool has_c() const;
  private:
  bool _internal_has_c() const;
  public:
  void clear_c();
  int32_t c() const;
  void set_c(int32_t value);
  private:
  int32_t _internal_c() const;
  void _internal_set_c(int32_t value);
  public:

  // optional bool x = 3;
  bool has_x() const;
  private:
  bool _internal_has_x() const;
  public:
  void clear_x();
  bool x() const;
  void set_x(bool value);
  private:
  bool _internal_x() const;
  void _internal_set_x(bool value);
  public:

  // optional int32 b = 4;
  bool has_b() const;
  private:
  bool _internal_has_b() const;
  public:
  void clear_b();
  int32_t b() const;
  void set_b(int32_t value);
  private:
  int32_t _internal_b() const;
  void _internal_set_b(int32_t value);
  public:

  // optional int32 a = 5;
  bool has_a() const;
  private:
  bool _internal_has_a() const;
  public:
  void clear_a();
  int32_t a() const;
  void set_a(int32_t value);
  private:
  int32_t _internal_a() const;
  void _internal_set_a(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestJavaScriptOrderJSON2)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::protobuf_unittest::TestJavaScriptOrderJSON1 > z_;
  int32_t d_;
  int32_t c_;
  bool x_;
  int32_t b_;
  int32_t a_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_2eproto;
};
// -------------------------------------------------------------------

class TestLargeInt final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestLargeInt) */ {
 public:
  inline TestLargeInt() : TestLargeInt(nullptr) {}
  ~TestLargeInt() override;
  explicit constexpr TestLargeInt(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestLargeInt(const TestLargeInt& from);
  TestLargeInt(TestLargeInt&& from) noexcept
    : TestLargeInt() {
    *this = ::std::move(from);
  }

  inline TestLargeInt& operator=(const TestLargeInt& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestLargeInt& operator=(TestLargeInt&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestLargeInt& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestLargeInt* internal_default_instance() {
    return reinterpret_cast<const TestLargeInt*>(
               &_TestLargeInt_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(TestLargeInt& a, TestLargeInt& b) {
    a.Swap(&b);
  }
  inline void Swap(TestLargeInt* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestLargeInt* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestLargeInt* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestLargeInt>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestLargeInt& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestLargeInt& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestLargeInt* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "protobuf_unittest.TestLargeInt";
  }
  protected:
  explicit TestLargeInt(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAFieldNumber = 1,
    kBFieldNumber = 2,
  };
  // required int64 a = 1;
  bool has_a() const;
  private:
  bool _internal_has_a() const;
  public:
  void clear_a();
  int64_t a() const;
  void set_a(int64_t value);
  private:
  int64_t _internal_a() const;
  void _internal_set_a(int64_t value);
  public:

  // required uint64 b = 2;
  bool has_b() const;
  private:
  bool _internal_has_b() const;
  public:
  void clear_b();
  uint64_t b() const;
  void set_b(uint64_t value);
  private:
  uint64_t _internal_b() const;
  void _internal_set_b(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestLargeInt)
 private:
  class _Internal;

  // helper for ByteSizeLong()
  size_t RequiredFieldsByteSizeFallback() const;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  int64_t a_;
  uint64_t b_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_2eproto;
};
// -------------------------------------------------------------------

class TestNumbers final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestNumbers) */ {
 public:
  inline TestNumbers() : TestNumbers(nullptr) {}
  ~TestNumbers() override;
  explicit constexpr TestNumbers(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestNumbers(const TestNumbers& from);
  TestNumbers(TestNumbers&& from) noexcept
    : TestNumbers() {
    *this = ::std::move(from);
  }

  inline TestNumbers& operator=(const TestNumbers& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestNumbers& operator=(TestNumbers&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestNumbers& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestNumbers* internal_default_instance() {
    return reinterpret_cast<const TestNumbers*>(
               &_TestNumbers_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(TestNumbers& a, TestNumbers& b) {
    a.Swap(&b);
  }
  inline void Swap(TestNumbers* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestNumbers* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestNumbers* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestNumbers>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestNumbers& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestNumbers& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestNumbers* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "protobuf_unittest.TestNumbers";
  }
  protected:
  explicit TestNumbers(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef TestNumbers_MyType MyType;
  static constexpr MyType OK =
    TestNumbers_MyType_OK;
  static constexpr MyType WARNING =
    TestNumbers_MyType_WARNING;
  static constexpr MyType ERROR =
    TestNumbers_MyType_ERROR;
  static inline bool MyType_IsValid(int value) {
    return TestNumbers_MyType_IsValid(value);
  }
  static constexpr MyType MyType_MIN =
    TestNumbers_MyType_MyType_MIN;
  static constexpr MyType MyType_MAX =
    TestNumbers_MyType_MyType_MAX;
  static constexpr int MyType_ARRAYSIZE =
    TestNumbers_MyType_MyType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  MyType_descriptor() {
    return TestNumbers_MyType_descriptor();
  }
  template<typename T>
  static inline const std::string& MyType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, MyType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function MyType_Name.");
    return TestNumbers_MyType_Name(enum_t_value);
  }
  static inline bool MyType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      MyType* value) {
    return TestNumbers_MyType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kAFieldNumber = 1,
    kBFieldNumber = 2,
    kCFieldNumber = 3,
    kDFieldNumber = 4,
    kEFieldNumber = 5,
    kFFieldNumber = 6,
  };
  // optional .protobuf_unittest.TestNumbers.MyType a = 1;
  bool has_a() const;
  private:
  bool _internal_has_a() const;
  public:
  void clear_a();
  ::protobuf_unittest::TestNumbers_MyType a() const;
  void set_a(::protobuf_unittest::TestNumbers_MyType value);
  private:
  ::protobuf_unittest::TestNumbers_MyType _internal_a() const;
  void _internal_set_a(::protobuf_unittest::TestNumbers_MyType value);
  public:

  // optional int32 b = 2;
  bool has_b() const;
  private:
  bool _internal_has_b() const;
  public:
  void clear_b();
  int32_t b() const;
  void set_b(int32_t value);
  private:
  int32_t _internal_b() const;
  void _internal_set_b(int32_t value);
  public:

  // optional float c = 3;
  bool has_c() const;
  private:
  bool _internal_has_c() const;
  public:
  void clear_c();
  float c() const;
  void set_c(float value);
  private:
  float _internal_c() const;
  void _internal_set_c(float value);
  public:

  // optional bool d = 4;
  bool has_d() const;
  private:
  bool _internal_has_d() const;
  public:
  void clear_d();
  bool d() const;
  void set_d(bool value);
  private:
  bool _internal_d() const;
  void _internal_set_d(bool value);
  public:

  // optional double e = 5;
  bool has_e() const;
  private:
  bool _internal_has_e() const;
  public:
  void clear_e();
  double e() const;
  void set_e(double value);
  private:
  double _internal_e() const;
  void _internal_set_e(double value);
  public:

  // optional uint32 f = 6;
  bool has_f() const;
  private:
  bool _internal_has_f() const;
  public:
  void clear_f();
  uint32_t f() const;
  void set_f(uint32_t value);
  private:
  uint32_t _internal_f() const;
  void _internal_set_f(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestNumbers)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  int a_;
  int32_t b_;
  float c_;
  bool d_;
  double e_;
  uint32_t f_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_2eproto;
};
// -------------------------------------------------------------------

class TestCamelCase final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestCamelCase) */ {
 public:
  inline TestCamelCase() : TestCamelCase(nullptr) {}
  ~TestCamelCase() override;
  explicit constexpr TestCamelCase(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestCamelCase(const TestCamelCase& from);
  TestCamelCase(TestCamelCase&& from) noexcept
    : TestCamelCase() {
    *this = ::std::move(from);
  }

  inline TestCamelCase& operator=(const TestCamelCase& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestCamelCase& operator=(TestCamelCase&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestCamelCase& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestCamelCase* internal_default_instance() {
    return reinterpret_cast<const TestCamelCase*>(
               &_TestCamelCase_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(TestCamelCase& a, TestCamelCase& b) {
    a.Swap(&b);
  }
  inline void Swap(TestCamelCase* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestCamelCase* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestCamelCase* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestCamelCase>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestCamelCase& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestCamelCase& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestCamelCase* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "protobuf_unittest.TestCamelCase";
  }
  protected:
  explicit TestCamelCase(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNormalFieldFieldNumber = 1,
    kCAPITALFIELDFieldNumber = 2,
    kCamelCaseFieldFieldNumber = 3,
  };
  // optional string normal_field = 1;
  bool has_normal_field() const;
  private:
  bool _internal_has_normal_field() const;
  public:
  void clear_normal_field();
  const std::string& normal_field() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_normal_field(ArgT0&& arg0, ArgT... args);
  std::string* mutable_normal_field();
  PROTOBUF_NODISCARD std::string* release_normal_field();
  void set_allocated_normal_field(std::string* normal_field);
  private:
  const std::string& _internal_normal_field() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_normal_field(const std::string& value);
  std::string* _internal_mutable_normal_field();
  public:

  // optional int32 CAPITAL_FIELD = 2;
  bool has_capital_field() const;
  private:
  bool _internal_has_capital_field() const;
  public:
  void clear_capital_field();
  int32_t capital_field() const;
  void set_capital_field(int32_t value);
  private:
  int32_t _internal_capital_field() const;
  void _internal_set_capital_field(int32_t value);
  public:

  // optional int32 CamelCaseField = 3;
  bool has_camelcasefield() const;
  private:
  bool _internal_has_camelcasefield() const;
  public:
  void clear_camelcasefield();
  int32_t camelcasefield() const;
  void set_camelcasefield(int32_t value);
  private:
  int32_t _internal_camelcasefield() const;
  void _internal_set_camelcasefield(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestCamelCase)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr normal_field_;
  int32_t capital_field_;
  int32_t camelcasefield_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_2eproto;
};
// -------------------------------------------------------------------

class TestBoolMap_BoolMapEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestBoolMap_BoolMapEntry_DoNotUse, 
    bool, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_BOOL,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestBoolMap_BoolMapEntry_DoNotUse, 
    bool, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_BOOL,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> SuperType;
  TestBoolMap_BoolMapEntry_DoNotUse();
  explicit constexpr TestBoolMap_BoolMapEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit TestBoolMap_BoolMapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const TestBoolMap_BoolMapEntry_DoNotUse& other);
  static const TestBoolMap_BoolMapEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const TestBoolMap_BoolMapEntry_DoNotUse*>(&_TestBoolMap_BoolMapEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class TestBoolMap final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestBoolMap) */ {
 public:
  inline TestBoolMap() : TestBoolMap(nullptr) {}
  ~TestBoolMap() override;
  explicit constexpr TestBoolMap(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestBoolMap(const TestBoolMap& from);
  TestBoolMap(TestBoolMap&& from) noexcept
    : TestBoolMap() {
    *this = ::std::move(from);
  }

  inline TestBoolMap& operator=(const TestBoolMap& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestBoolMap& operator=(TestBoolMap&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestBoolMap& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestBoolMap* internal_default_instance() {
    return reinterpret_cast<const TestBoolMap*>(
               &_TestBoolMap_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(TestBoolMap& a, TestBoolMap& b) {
    a.Swap(&b);
  }
  inline void Swap(TestBoolMap* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestBoolMap* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestBoolMap* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestBoolMap>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestBoolMap& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestBoolMap& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestBoolMap* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "protobuf_unittest.TestBoolMap";
  }
  protected:
  explicit TestBoolMap(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kBoolMapFieldNumber = 1,
  };
  // map<bool, int32> bool_map = 1;
  int bool_map_size() const;
  private:
  int _internal_bool_map_size() const;
  public:
  void clear_bool_map();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >&
      _internal_bool_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >*
      _internal_mutable_bool_map();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >&
      bool_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >*
      mutable_bool_map();

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestBoolMap)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      TestBoolMap_BoolMapEntry_DoNotUse,
      bool, int32_t,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_BOOL,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> bool_map_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_2eproto;
};
// -------------------------------------------------------------------

class TestRecursion final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestRecursion) */ {
 public:
  inline TestRecursion() : TestRecursion(nullptr) {}
  ~TestRecursion() override;
  explicit constexpr TestRecursion(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestRecursion(const TestRecursion& from);
  TestRecursion(TestRecursion&& from) noexcept
    : TestRecursion() {
    *this = ::std::move(from);
  }

  inline TestRecursion& operator=(const TestRecursion& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestRecursion& operator=(TestRecursion&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestRecursion& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestRecursion* internal_default_instance() {
    return reinterpret_cast<const TestRecursion*>(
               &_TestRecursion_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(TestRecursion& a, TestRecursion& b) {
    a.Swap(&b);
  }
  inline void Swap(TestRecursion* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestRecursion* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestRecursion* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestRecursion>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestRecursion& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestRecursion& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestRecursion* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "protobuf_unittest.TestRecursion";
  }
  protected:
  explicit TestRecursion(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kChildFieldNumber = 2,
    kValueFieldNumber = 1,
  };
  // optional .protobuf_unittest.TestRecursion child = 2;
  bool has_child() const;
  private:
  bool _internal_has_child() const;
  public:
  void clear_child();
  const ::protobuf_unittest::TestRecursion& child() const;
  PROTOBUF_NODISCARD ::protobuf_unittest::TestRecursion* release_child();
  ::protobuf_unittest::TestRecursion* mutable_child();
  void set_allocated_child(::protobuf_unittest::TestRecursion* child);
  private:
  const ::protobuf_unittest::TestRecursion& _internal_child() const;
  ::protobuf_unittest::TestRecursion* _internal_mutable_child();
  public:
  void unsafe_arena_set_allocated_child(
      ::protobuf_unittest::TestRecursion* child);
  ::protobuf_unittest::TestRecursion* unsafe_arena_release_child();

  // optional int32 value = 1;
  bool has_value() const;
  private:
  bool _internal_has_value() const;
  public:
  void clear_value();
  int32_t value() const;
  void set_value(int32_t value);
  private:
  int32_t _internal_value() const;
  void _internal_set_value(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestRecursion)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::protobuf_unittest::TestRecursion* child_;
  int32_t value_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_2eproto;
};
// -------------------------------------------------------------------

class TestStringMap_StringMapEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestStringMap_StringMapEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestStringMap_StringMapEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  TestStringMap_StringMapEntry_DoNotUse();
  explicit constexpr TestStringMap_StringMapEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit TestStringMap_StringMapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const TestStringMap_StringMapEntry_DoNotUse& other);
  static const TestStringMap_StringMapEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const TestStringMap_StringMapEntry_DoNotUse*>(&_TestStringMap_StringMapEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
#ifndef NDEBUG
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
       s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "protobuf_unittest.TestStringMap.StringMapEntry.key");
#else
    (void) s;
#endif
    return true;
 }
  static bool ValidateValue(std::string* s) {
#ifndef NDEBUG
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
       s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "protobuf_unittest.TestStringMap.StringMapEntry.value");
#else
    (void) s;
#endif
    return true;
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class TestStringMap final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestStringMap) */ {
 public:
  inline TestStringMap() : TestStringMap(nullptr) {}
  ~TestStringMap() override;
  explicit constexpr TestStringMap(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestStringMap(const TestStringMap& from);
  TestStringMap(TestStringMap&& from) noexcept
    : TestStringMap() {
    *this = ::std::move(from);
  }

  inline TestStringMap& operator=(const TestStringMap& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestStringMap& operator=(TestStringMap&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestStringMap& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestStringMap* internal_default_instance() {
    return reinterpret_cast<const TestStringMap*>(
               &_TestStringMap_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(TestStringMap& a, TestStringMap& b) {
    a.Swap(&b);
  }
  inline void Swap(TestStringMap* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestStringMap* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestStringMap* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestStringMap>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestStringMap& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestStringMap& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestStringMap* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "protobuf_unittest.TestStringMap";
  }
  protected:
  explicit TestStringMap(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kStringMapFieldNumber = 1,
  };
  // map<string, string> string_map = 1;
  int string_map_size() const;
  private:
  int _internal_string_map_size() const;
  public:
  void clear_string_map();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      _internal_string_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      _internal_mutable_string_map();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      string_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_string_map();

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestStringMap)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      TestStringMap_StringMapEntry_DoNotUse,
      std::string, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> string_map_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_2eproto;
};
// -------------------------------------------------------------------

class TestStringSerializer_StringMapEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestStringSerializer_StringMapEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TestStringSerializer_StringMapEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  TestStringSerializer_StringMapEntry_DoNotUse();
  explicit constexpr TestStringSerializer_StringMapEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit TestStringSerializer_StringMapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const TestStringSerializer_StringMapEntry_DoNotUse& other);
  static const TestStringSerializer_StringMapEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const TestStringSerializer_StringMapEntry_DoNotUse*>(&_TestStringSerializer_StringMapEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
#ifndef NDEBUG
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
       s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "protobuf_unittest.TestStringSerializer.StringMapEntry.key");
#else
    (void) s;
#endif
    return true;
 }
  static bool ValidateValue(std::string* s) {
#ifndef NDEBUG
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
       s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "protobuf_unittest.TestStringSerializer.StringMapEntry.value");
#else
    (void) s;
#endif
    return true;
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class TestStringSerializer final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestStringSerializer) */ {
 public:
  inline TestStringSerializer() : TestStringSerializer(nullptr) {}
  ~TestStringSerializer() override;
  explicit constexpr TestStringSerializer(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestStringSerializer(const TestStringSerializer& from);
  TestStringSerializer(TestStringSerializer&& from) noexcept
    : TestStringSerializer() {
    *this = ::std::move(from);
  }

  inline TestStringSerializer& operator=(const TestStringSerializer& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestStringSerializer& operator=(TestStringSerializer&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestStringSerializer& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestStringSerializer* internal_default_instance() {
    return reinterpret_cast<const TestStringSerializer*>(
               &_TestStringSerializer_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(TestStringSerializer& a, TestStringSerializer& b) {
    a.Swap(&b);
  }
  inline void Swap(TestStringSerializer* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestStringSerializer* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestStringSerializer* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestStringSerializer>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestStringSerializer& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestStringSerializer& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestStringSerializer* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "protobuf_unittest.TestStringSerializer";
  }
  protected:
  explicit TestStringSerializer(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kRepeatedStringFieldNumber = 2,
    kStringMapFieldNumber = 3,
    kScalarStringFieldNumber = 1,
  };
  // repeated string repeated_string = 2;
  int repeated_string_size() const;
  private:
  int _internal_repeated_string_size() const;
  public:
  void clear_repeated_string();
  const std::string& repeated_string(int index) const;
  std::string* mutable_repeated_string(int index);
  void set_repeated_string(int index, const std::string& value);
  void set_repeated_string(int index, std::string&& value);
  void set_repeated_string(int index, const char* value);
  void set_repeated_string(int index, const char* value, size_t size);
  std::string* add_repeated_string();
  void add_repeated_string(const std::string& value);
  void add_repeated_string(std::string&& value);
  void add_repeated_string(const char* value);
  void add_repeated_string(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& repeated_string() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_repeated_string();
  private:
  const std::string& _internal_repeated_string(int index) const;
  std::string* _internal_add_repeated_string();
  public:

  // map<string, string> string_map = 3;
  int string_map_size() const;
  private:
  int _internal_string_map_size() const;
  public:
  void clear_string_map();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      _internal_string_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      _internal_mutable_string_map();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      string_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_string_map();

  // optional string scalar_string = 1;
  bool has_scalar_string() const;
  private:
  bool _internal_has_scalar_string() const;
  public:
  void clear_scalar_string();
  const std::string& scalar_string() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_scalar_string(ArgT0&& arg0, ArgT... args);
  std::string* mutable_scalar_string();
  PROTOBUF_NODISCARD std::string* release_scalar_string();
  void set_allocated_scalar_string(std::string* scalar_string);
  private:
  const std::string& _internal_scalar_string() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_scalar_string(const std::string& value);
  std::string* _internal_mutable_scalar_string();
  public:

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestStringSerializer)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> repeated_string_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      TestStringSerializer_StringMapEntry_DoNotUse,
      std::string, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> string_map_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr scalar_string_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_2eproto;
};
// -------------------------------------------------------------------

class TestMessageWithExtension final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestMessageWithExtension) */ {
 public:
  inline TestMessageWithExtension() : TestMessageWithExtension(nullptr) {}
  ~TestMessageWithExtension() override;
  explicit constexpr TestMessageWithExtension(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestMessageWithExtension(const TestMessageWithExtension& from);
  TestMessageWithExtension(TestMessageWithExtension&& from) noexcept
    : TestMessageWithExtension() {
    *this = ::std::move(from);
  }

  inline TestMessageWithExtension& operator=(const TestMessageWithExtension& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestMessageWithExtension& operator=(TestMessageWithExtension&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestMessageWithExtension& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestMessageWithExtension* internal_default_instance() {
    return reinterpret_cast<const TestMessageWithExtension*>(
               &_TestMessageWithExtension_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(TestMessageWithExtension& a, TestMessageWithExtension& b) {
    a.Swap(&b);
  }
  inline void Swap(TestMessageWithExtension* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestMessageWithExtension* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestMessageWithExtension* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestMessageWithExtension>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestMessageWithExtension& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestMessageWithExtension& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestMessageWithExtension* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "protobuf_unittest.TestMessageWithExtension";
  }
  protected:
  explicit TestMessageWithExtension(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------


  template <typename _proto_TypeTraits,
            ::PROTOBUF_NAMESPACE_ID::internal::FieldType _field_type,
            bool _is_packed>
  inline bool HasExtension(
      const ::PROTOBUF_NAMESPACE_ID::internal::ExtensionIdentifier<
          TestMessageWithExtension, _proto_TypeTraits, _field_type, _is_packed>& id) const {

    return _extensions_.Has(id.number());
  }

  template <typename _proto_TypeTraits,
            ::PROTOBUF_NAMESPACE_ID::internal::FieldType _field_type,
            bool _is_packed>
  inline void ClearExtension(
      const ::PROTOBUF_NAMESPACE_ID::internal::ExtensionIdentifier<
          TestMessageWithExtension, _proto_TypeTraits, _field_type, _is_packed>& id) {
    _extensions_.ClearExtension(id.number());

  }

  template <typename _proto_TypeTraits,
            ::PROTOBUF_NAMESPACE_ID::internal::FieldType _field_type,
            bool _is_packed>
  inline int ExtensionSize(
      const ::PROTOBUF_NAMESPACE_ID::internal::ExtensionIdentifier<
          TestMessageWithExtension, _proto_TypeTraits, _field_type, _is_packed>& id) const {

    return _extensions_.ExtensionSize(id.number());
  }

  template <typename _proto_TypeTraits,
            ::PROTOBUF_NAMESPACE_ID::internal::FieldType _field_type,
            bool _is_packed>
  inline typename _proto_TypeTraits::Singular::ConstType GetExtension(
      const ::PROTOBUF_NAMESPACE_ID::internal::ExtensionIdentifier<
          TestMessageWithExtension, _proto_TypeTraits, _field_type, _is_packed>& id) const {

    return _proto_TypeTraits::Get(id.number(), _extensions_,
                                  id.default_value());
  }

  template <typename _proto_TypeTraits,
            ::PROTOBUF_NAMESPACE_ID::internal::FieldType _field_type,
            bool _is_packed>
  inline typename _proto_TypeTraits::Singular::MutableType MutableExtension(
      const ::PROTOBUF_NAMESPACE_ID::internal::ExtensionIdentifier<
          TestMessageWithExtension, _proto_TypeTraits, _field_type, _is_packed>& id) {

    return _proto_TypeTraits::Mutable(id.number(), _field_type,
                                      &_extensions_);
  }

  template <typename _proto_TypeTraits,
            ::PROTOBUF_NAMESPACE_ID::internal::FieldType _field_type,
            bool _is_packed>
  inline void SetExtension(
      const ::PROTOBUF_NAMESPACE_ID::internal::ExtensionIdentifier<
          TestMessageWithExtension, _proto_TypeTraits, _field_type, _is_packed>& id,
      typename _proto_TypeTraits::Singular::ConstType value) {
    _proto_TypeTraits::Set(id.number(), _field_type, value, &_extensions_);

  }

  template <typename _proto_TypeTraits,
            ::PROTOBUF_NAMESPACE_ID::internal::FieldType _field_type,
            bool _is_packed>
  inline void SetAllocatedExtension(
      const ::PROTOBUF_NAMESPACE_ID::internal::ExtensionIdentifier<
          TestMessageWithExtension, _proto_TypeTraits, _field_type, _is_packed>& id,
      typename _proto_TypeTraits::Singular::MutableType value) {
    _proto_TypeTraits::SetAllocated(id.number(), _field_type, value,
                                    &_extensions_);

  }
  template <typename _proto_TypeTraits,
            ::PROTOBUF_NAMESPACE_ID::internal::FieldType _field_type,
            bool _is_packed>
  inline void UnsafeArenaSetAllocatedExtension(
      const ::PROTOBUF_NAMESPACE_ID::internal::ExtensionIdentifier<
          TestMessageWithExtension, _proto_TypeTraits, _field_type, _is_packed>& id,
      typename _proto_TypeTraits::Singular::MutableType value) {
    _proto_TypeTraits::UnsafeArenaSetAllocated(id.number(), _field_type,
                                               value, &_extensions_);

  }
  template <typename _proto_TypeTraits,
            ::PROTOBUF_NAMESPACE_ID::internal::FieldType _field_type,
            bool _is_packed>
  PROTOBUF_NODISCARD inline
      typename _proto_TypeTraits::Singular::MutableType
      ReleaseExtension(
          const ::PROTOBUF_NAMESPACE_ID::internal::ExtensionIdentifier<
              TestMessageWithExtension, _proto_TypeTraits, _field_type, _is_packed>& id) {

    return _proto_TypeTraits::Release(id.number(), _field_type,
                                      &_extensions_);
  }
  template <typename _proto_TypeTraits,
            ::PROTOBUF_NAMESPACE_ID::internal::FieldType _field_type,
            bool _is_packed>
  inline typename _proto_TypeTraits::Singular::MutableType
  UnsafeArenaReleaseExtension(
      const ::PROTOBUF_NAMESPACE_ID::internal::ExtensionIdentifier<
          TestMessageWithExtension, _proto_TypeTraits, _field_type, _is_packed>& id) {

    return _proto_TypeTraits::UnsafeArenaRelease(id.number(), _field_type,
                                                 &_extensions_);
  }

  template <typename _proto_TypeTraits,
            ::PROTOBUF_NAMESPACE_ID::internal::FieldType _field_type,
            bool _is_packed>
  inline typename _proto_TypeTraits::Repeated::ConstType GetExtension(
      const ::PROTOBUF_NAMESPACE_ID::internal::ExtensionIdentifier<
          TestMessageWithExtension, _proto_TypeTraits, _field_type, _is_packed>& id,
      int index) const {

    return _proto_TypeTraits::Get(id.number(), _extensions_, index);
  }

  template <typename _proto_TypeTraits,
            ::PROTOBUF_NAMESPACE_ID::internal::FieldType _field_type,
            bool _is_packed>
  inline typename _proto_TypeTraits::Repeated::MutableType MutableExtension(
      const ::PROTOBUF_NAMESPACE_ID::internal::ExtensionIdentifier<
          TestMessageWithExtension, _proto_TypeTraits, _field_type, _is_packed>& id,
      int index) {

    return _proto_TypeTraits::Mutable(id.number(), index, &_extensions_);
  }

  template <typename _proto_TypeTraits,
            ::PROTOBUF_NAMESPACE_ID::internal::FieldType _field_type,
            bool _is_packed>
  inline void SetExtension(
      const ::PROTOBUF_NAMESPACE_ID::internal::ExtensionIdentifier<
          TestMessageWithExtension, _proto_TypeTraits, _field_type, _is_packed>& id,
      int index, typename _proto_TypeTraits::Repeated::ConstType value) {
    _proto_TypeTraits::Set(id.number(), index, value, &_extensions_);

  }

  template <typename _proto_TypeTraits,
            ::PROTOBUF_NAMESPACE_ID::internal::FieldType _field_type,
            bool _is_packed>
  inline typename _proto_TypeTraits::Repeated::MutableType AddExtension(
      const ::PROTOBUF_NAMESPACE_ID::internal::ExtensionIdentifier<
          TestMessageWithExtension, _proto_TypeTraits, _field_type, _is_packed>& id) {
    typename _proto_TypeTraits::Repeated::MutableType to_add =
        _proto_TypeTraits::Add(id.number(), _field_type, &_extensions_);

    return to_add;
  }

  template <typename _proto_TypeTraits,
            ::PROTOBUF_NAMESPACE_ID::internal::FieldType _field_type,
            bool _is_packed>
  inline void AddExtension(
      const ::PROTOBUF_NAMESPACE_ID::internal::ExtensionIdentifier<
          TestMessageWithExtension, _proto_TypeTraits, _field_type, _is_packed>& id,
      typename _proto_TypeTraits::Repeated::ConstType value) {
    _proto_TypeTraits::Add(id.number(), _field_type, _is_packed, value,
                           &_extensions_);

  }

  template <typename _proto_TypeTraits,
            ::PROTOBUF_NAMESPACE_ID::internal::FieldType _field_type,
            bool _is_packed>
  inline const typename _proto_TypeTraits::Repeated::RepeatedFieldType&
  GetRepeatedExtension(
      const ::PROTOBUF_NAMESPACE_ID::internal::ExtensionIdentifier<
          TestMessageWithExtension, _proto_TypeTraits, _field_type, _is_packed>& id) const {

    return _proto_TypeTraits::GetRepeated(id.number(), _extensions_);
  }

  template <typename _proto_TypeTraits,
            ::PROTOBUF_NAMESPACE_ID::internal::FieldType _field_type,
            bool _is_packed>
  inline typename _proto_TypeTraits::Repeated::RepeatedFieldType*
  MutableRepeatedExtension(
      const ::PROTOBUF_NAMESPACE_ID::internal::ExtensionIdentifier<
          TestMessageWithExtension, _proto_TypeTraits, _field_type, _is_packed>& id) {

    return _proto_TypeTraits::MutableRepeated(id.number(), _field_type,
                                              _is_packed, &_extensions_);
  }

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestMessageWithExtension)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::ExtensionSet _extensions_;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_2eproto;
};
// -------------------------------------------------------------------

class TestExtension final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestExtension) */ {
 public:
  inline TestExtension() : TestExtension(nullptr) {}
  ~TestExtension() override;
  explicit constexpr TestExtension(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestExtension(const TestExtension& from);
  TestExtension(TestExtension&& from) noexcept
    : TestExtension() {
    *this = ::std::move(from);
  }

  inline TestExtension& operator=(const TestExtension& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestExtension& operator=(TestExtension&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestExtension& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestExtension* internal_default_instance() {
    return reinterpret_cast<const TestExtension*>(
               &_TestExtension_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(TestExtension& a, TestExtension& b) {
    a.Swap(&b);
  }
  inline void Swap(TestExtension* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestExtension* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestExtension* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestExtension>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestExtension& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestExtension& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestExtension* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "protobuf_unittest.TestExtension";
  }
  protected:
  explicit TestExtension(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValueFieldNumber = 1,
  };
  // optional string value = 1;
  bool has_value() const;
  private:
  bool _internal_has_value() const;
  public:
  void clear_value();
  const std::string& value() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_value(ArgT0&& arg0, ArgT... args);
  std::string* mutable_value();
  PROTOBUF_NODISCARD std::string* release_value();
  void set_allocated_value(std::string* value);
  private:
  const std::string& _internal_value() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_value(const std::string& value);
  std::string* _internal_mutable_value();
  public:

  static const int kExtFieldNumber = 100;
  static ::PROTOBUF_NAMESPACE_ID::internal::ExtensionIdentifier< ::protobuf_unittest::TestMessageWithExtension,
      ::PROTOBUF_NAMESPACE_ID::internal::MessageTypeTraits< ::protobuf_unittest::TestExtension >, 11, false >
    ext;
  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestExtension)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr value_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_2eproto;
};
// -------------------------------------------------------------------

class TestDefaultEnumValue final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestDefaultEnumValue) */ {
 public:
  inline TestDefaultEnumValue() : TestDefaultEnumValue(nullptr) {}
  ~TestDefaultEnumValue() override;
  explicit constexpr TestDefaultEnumValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestDefaultEnumValue(const TestDefaultEnumValue& from);
  TestDefaultEnumValue(TestDefaultEnumValue&& from) noexcept
    : TestDefaultEnumValue() {
    *this = ::std::move(from);
  }

  inline TestDefaultEnumValue& operator=(const TestDefaultEnumValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestDefaultEnumValue& operator=(TestDefaultEnumValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestDefaultEnumValue& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestDefaultEnumValue* internal_default_instance() {
    return reinterpret_cast<const TestDefaultEnumValue*>(
               &_TestDefaultEnumValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(TestDefaultEnumValue& a, TestDefaultEnumValue& b) {
    a.Swap(&b);
  }
  inline void Swap(TestDefaultEnumValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestDefaultEnumValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestDefaultEnumValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestDefaultEnumValue>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestDefaultEnumValue& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TestDefaultEnumValue& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestDefaultEnumValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "protobuf_unittest.TestDefaultEnumValue";
  }
  protected:
  explicit TestDefaultEnumValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEnumValueFieldNumber = 1,
  };
  // optional .protobuf_unittest.EnumValue enum_value = 1 [default = DEFAULT];
  bool has_enum_value() const;
  private:
  bool _internal_has_enum_value() const;
  public:
  void clear_enum_value();
  ::protobuf_unittest::EnumValue enum_value() const;
  void set_enum_value(::protobuf_unittest::EnumValue value);
  private:
  ::protobuf_unittest::EnumValue _internal_enum_value() const;
  void _internal_set_enum_value(::protobuf_unittest::EnumValue value);
  public:

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestDefaultEnumValue)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  int enum_value_;
  friend struct ::TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TestFlagsAndStrings_RepeatedGroup

// required string f = 3;
inline bool TestFlagsAndStrings_RepeatedGroup::_internal_has_f() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool TestFlagsAndStrings_RepeatedGroup::has_f() const {
  return _internal_has_f();
}
inline void TestFlagsAndStrings_RepeatedGroup::clear_f() {
  f_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& TestFlagsAndStrings_RepeatedGroup::f() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestFlagsAndStrings.RepeatedGroup.f)
  return _internal_f();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TestFlagsAndStrings_RepeatedGroup::set_f(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 f_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestFlagsAndStrings.RepeatedGroup.f)
}
inline std::string* TestFlagsAndStrings_RepeatedGroup::mutable_f() {
  std::string* _s = _internal_mutable_f();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestFlagsAndStrings.RepeatedGroup.f)
  return _s;
}
inline const std::string& TestFlagsAndStrings_RepeatedGroup::_internal_f() const {
  return f_.Get();
}
inline void TestFlagsAndStrings_RepeatedGroup::_internal_set_f(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  f_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* TestFlagsAndStrings_RepeatedGroup::_internal_mutable_f() {
  _has_bits_[0] |= 0x00000001u;
  return f_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* TestFlagsAndStrings_RepeatedGroup::release_f() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestFlagsAndStrings.RepeatedGroup.f)
  if (!_internal_has_f()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = f_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (f_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    f_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void TestFlagsAndStrings_RepeatedGroup::set_allocated_f(std::string* f) {
  if (f != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  f_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), f,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (f_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    f_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestFlagsAndStrings.RepeatedGroup.f)
}

// -------------------------------------------------------------------

// TestFlagsAndStrings

// required int32 A = 1;
inline bool TestFlagsAndStrings::_internal_has_a() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool TestFlagsAndStrings::has_a() const {
  return _internal_has_a();
}
inline void TestFlagsAndStrings::clear_a() {
  a_ = 0;
  _has_bits_[0] &= ~0x00000001u;
}
inline int32_t TestFlagsAndStrings::_internal_a() const {
  return a_;
}
inline int32_t TestFlagsAndStrings::a() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestFlagsAndStrings.A)
  return _internal_a();
}
inline void TestFlagsAndStrings::_internal_set_a(int32_t value) {
  _has_bits_[0] |= 0x00000001u;
  a_ = value;
}
inline void TestFlagsAndStrings::set_a(int32_t value) {
  _internal_set_a(value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestFlagsAndStrings.A)
}

// repeated group RepeatedGroup = 2 { ... };
inline int TestFlagsAndStrings::_internal_repeatedgroup_size() const {
  return repeatedgroup_.size();
}
inline int TestFlagsAndStrings::repeatedgroup_size() const {
  return _internal_repeatedgroup_size();
}
inline void TestFlagsAndStrings::clear_repeatedgroup() {
  repeatedgroup_.Clear();
}
inline ::protobuf_unittest::TestFlagsAndStrings_RepeatedGroup* TestFlagsAndStrings::mutable_repeatedgroup(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestFlagsAndStrings.repeatedgroup)
  return repeatedgroup_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::protobuf_unittest::TestFlagsAndStrings_RepeatedGroup >*
TestFlagsAndStrings::mutable_repeatedgroup() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestFlagsAndStrings.repeatedgroup)
  return &repeatedgroup_;
}
inline const ::protobuf_unittest::TestFlagsAndStrings_RepeatedGroup& TestFlagsAndStrings::_internal_repeatedgroup(int index) const {
  return repeatedgroup_.Get(index);
}
inline const ::protobuf_unittest::TestFlagsAndStrings_RepeatedGroup& TestFlagsAndStrings::repeatedgroup(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestFlagsAndStrings.repeatedgroup)
  return _internal_repeatedgroup(index);
}
inline ::protobuf_unittest::TestFlagsAndStrings_RepeatedGroup* TestFlagsAndStrings::_internal_add_repeatedgroup() {
  return repeatedgroup_.Add();
}
inline ::protobuf_unittest::TestFlagsAndStrings_RepeatedGroup* TestFlagsAndStrings::add_repeatedgroup() {
  ::protobuf_unittest::TestFlagsAndStrings_RepeatedGroup* _add = _internal_add_repeatedgroup();
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestFlagsAndStrings.repeatedgroup)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::protobuf_unittest::TestFlagsAndStrings_RepeatedGroup >&
TestFlagsAndStrings::repeatedgroup() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestFlagsAndStrings.repeatedgroup)
  return repeatedgroup_;
}

// -------------------------------------------------------------------

// TestBase64ByteArrays

// required bytes a = 1;
inline bool TestBase64ByteArrays::_internal_has_a() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool TestBase64ByteArrays::has_a() const {
  return _internal_has_a();
}
inline void TestBase64ByteArrays::clear_a() {
  a_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& TestBase64ByteArrays::a() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestBase64ByteArrays.a)
  return _internal_a();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TestBase64ByteArrays::set_a(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 a_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestBase64ByteArrays.a)
}
inline std::string* TestBase64ByteArrays::mutable_a() {
  std::string* _s = _internal_mutable_a();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestBase64ByteArrays.a)
  return _s;
}
inline const std::string& TestBase64ByteArrays::_internal_a() const {
  return a_.Get();
}
inline void TestBase64ByteArrays::_internal_set_a(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  a_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* TestBase64ByteArrays::_internal_mutable_a() {
  _has_bits_[0] |= 0x00000001u;
  return a_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* TestBase64ByteArrays::release_a() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestBase64ByteArrays.a)
  if (!_internal_has_a()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = a_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (a_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    a_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void TestBase64ByteArrays::set_allocated_a(std::string* a) {
  if (a != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  a_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), a,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (a_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    a_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestBase64ByteArrays.a)
}

// -------------------------------------------------------------------

// TestJavaScriptJSON

// optional int32 a = 1;
inline bool TestJavaScriptJSON::_internal_has_a() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool TestJavaScriptJSON::has_a() const {
  return _internal_has_a();
}
inline void TestJavaScriptJSON::clear_a() {
  a_ = 0;
  _has_bits_[0] &= ~0x00000004u;
}
inline int32_t TestJavaScriptJSON::_internal_a() const {
  return a_;
}
inline int32_t TestJavaScriptJSON::a() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestJavaScriptJSON.a)
  return _internal_a();
}
inline void TestJavaScriptJSON::_internal_set_a(int32_t value) {
  _has_bits_[0] |= 0x00000004u;
  a_ = value;
}
inline void TestJavaScriptJSON::set_a(int32_t value) {
  _internal_set_a(value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestJavaScriptJSON.a)
}

// optional float final = 2;
inline bool TestJavaScriptJSON::_internal_has_final() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool TestJavaScriptJSON::has_final() const {
  return _internal_has_final();
}
inline void TestJavaScriptJSON::clear_final() {
  final_ = 0;
  _has_bits_[0] &= ~0x00000008u;
}
inline float TestJavaScriptJSON::_internal_final() const {
  return final_;
}
inline float TestJavaScriptJSON::final() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestJavaScriptJSON.final)
  return _internal_final();
}
inline void TestJavaScriptJSON::_internal_set_final(float value) {
  _has_bits_[0] |= 0x00000008u;
  final_ = value;
}
inline void TestJavaScriptJSON::set_final(float value) {
  _internal_set_final(value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestJavaScriptJSON.final)
}

// optional string in = 3;
inline bool TestJavaScriptJSON::_internal_has_in() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool TestJavaScriptJSON::has_in() const {
  return _internal_has_in();
}
inline void TestJavaScriptJSON::clear_in() {
  in_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& TestJavaScriptJSON::in() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestJavaScriptJSON.in)
  return _internal_in();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TestJavaScriptJSON::set_in(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 in_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestJavaScriptJSON.in)
}
inline std::string* TestJavaScriptJSON::mutable_in() {
  std::string* _s = _internal_mutable_in();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestJavaScriptJSON.in)
  return _s;
}
inline const std::string& TestJavaScriptJSON::_internal_in() const {
  return in_.Get();
}
inline void TestJavaScriptJSON::_internal_set_in(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  in_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* TestJavaScriptJSON::_internal_mutable_in() {
  _has_bits_[0] |= 0x00000001u;
  return in_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* TestJavaScriptJSON::release_in() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestJavaScriptJSON.in)
  if (!_internal_has_in()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = in_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (in_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    in_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void TestJavaScriptJSON::set_allocated_in(std::string* in) {
  if (in != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  in_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), in,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (in_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    in_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestJavaScriptJSON.in)
}

// optional string Var = 4;
inline bool TestJavaScriptJSON::_internal_has_var() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool TestJavaScriptJSON::has_var() const {
  return _internal_has_var();
}
inline void TestJavaScriptJSON::clear_var() {
  var_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& TestJavaScriptJSON::var() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestJavaScriptJSON.Var)
  return _internal_var();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TestJavaScriptJSON::set_var(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000002u;
 var_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestJavaScriptJSON.Var)
}
inline std::string* TestJavaScriptJSON::mutable_var() {
  std::string* _s = _internal_mutable_var();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestJavaScriptJSON.Var)
  return _s;
}
inline const std::string& TestJavaScriptJSON::_internal_var() const {
  return var_.Get();
}
inline void TestJavaScriptJSON::_internal_set_var(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  var_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* TestJavaScriptJSON::_internal_mutable_var() {
  _has_bits_[0] |= 0x00000002u;
  return var_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* TestJavaScriptJSON::release_var() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestJavaScriptJSON.Var)
  if (!_internal_has_var()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  auto* p = var_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (var_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    var_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void TestJavaScriptJSON::set_allocated_var(std::string* var) {
  if (var != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  var_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), var,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (var_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    var_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestJavaScriptJSON.Var)
}

// -------------------------------------------------------------------

// TestJavaScriptOrderJSON1

// optional int32 d = 1;
inline bool TestJavaScriptOrderJSON1::_internal_has_d() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool TestJavaScriptOrderJSON1::has_d() const {
  return _internal_has_d();
}
inline void TestJavaScriptOrderJSON1::clear_d() {
  d_ = 0;
  _has_bits_[0] &= ~0x00000001u;
}
inline int32_t TestJavaScriptOrderJSON1::_internal_d() const {
  return d_;
}
inline int32_t TestJavaScriptOrderJSON1::d() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestJavaScriptOrderJSON1.d)
  return _internal_d();
}
inline void TestJavaScriptOrderJSON1::_internal_set_d(int32_t value) {
  _has_bits_[0] |= 0x00000001u;
  d_ = value;
}
inline void TestJavaScriptOrderJSON1::set_d(int32_t value) {
  _internal_set_d(value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestJavaScriptOrderJSON1.d)
}

// optional int32 c = 2;
inline bool TestJavaScriptOrderJSON1::_internal_has_c() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool TestJavaScriptOrderJSON1::has_c() const {
  return _internal_has_c();
}
inline void TestJavaScriptOrderJSON1::clear_c() {
  c_ = 0;
  _has_bits_[0] &= ~0x00000002u;
}
inline int32_t TestJavaScriptOrderJSON1::_internal_c() const {
  return c_;
}
inline int32_t TestJavaScriptOrderJSON1::c() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestJavaScriptOrderJSON1.c)
  return _internal_c();
}
inline void TestJavaScriptOrderJSON1::_internal_set_c(int32_t value) {
  _has_bits_[0] |= 0x00000002u;
  c_ = value;
}
inline void TestJavaScriptOrderJSON1::set_c(int32_t value) {
  _internal_set_c(value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestJavaScriptOrderJSON1.c)
}

// optional bool x = 3;
inline bool TestJavaScriptOrderJSON1::_internal_has_x() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool TestJavaScriptOrderJSON1::has_x() const {
  return _internal_has_x();
}
inline void TestJavaScriptOrderJSON1::clear_x() {
  x_ = false;
  _has_bits_[0] &= ~0x00000004u;
}
inline bool TestJavaScriptOrderJSON1::_internal_x() const {
  return x_;
}
inline bool TestJavaScriptOrderJSON1::x() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestJavaScriptOrderJSON1.x)
  return _internal_x();
}
inline void TestJavaScriptOrderJSON1::_internal_set_x(bool value) {
  _has_bits_[0] |= 0x00000004u;
  x_ = value;
}
inline void TestJavaScriptOrderJSON1::set_x(bool value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestJavaScriptOrderJSON1.x)
}

// optional int32 b = 4;
inline bool TestJavaScriptOrderJSON1::_internal_has_b() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool TestJavaScriptOrderJSON1::has_b() const {
  return _internal_has_b();
}
inline void TestJavaScriptOrderJSON1::clear_b() {
  b_ = 0;
  _has_bits_[0] &= ~0x00000008u;
}
inline int32_t TestJavaScriptOrderJSON1::_internal_b() const {
  return b_;
}
inline int32_t TestJavaScriptOrderJSON1::b() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestJavaScriptOrderJSON1.b)
  return _internal_b();
}
inline void TestJavaScriptOrderJSON1::_internal_set_b(int32_t value) {
  _has_bits_[0] |= 0x00000008u;
  b_ = value;
}
inline void TestJavaScriptOrderJSON1::set_b(int32_t value) {
  _internal_set_b(value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestJavaScriptOrderJSON1.b)
}

// optional int32 a = 5;
inline bool TestJavaScriptOrderJSON1::_internal_has_a() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool TestJavaScriptOrderJSON1::has_a() const {
  return _internal_has_a();
}
inline void TestJavaScriptOrderJSON1::clear_a() {
  a_ = 0;
  _has_bits_[0] &= ~0x00000010u;
}
inline int32_t TestJavaScriptOrderJSON1::_internal_a() const {
  return a_;
}
inline int32_t TestJavaScriptOrderJSON1::a() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestJavaScriptOrderJSON1.a)
  return _internal_a();
}
inline void TestJavaScriptOrderJSON1::_internal_set_a(int32_t value) {
  _has_bits_[0] |= 0x00000010u;
  a_ = value;
}
inline void TestJavaScriptOrderJSON1::set_a(int32_t value) {
  _internal_set_a(value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestJavaScriptOrderJSON1.a)
}

// -------------------------------------------------------------------

// TestJavaScriptOrderJSON2

// optional int32 d = 1;
inline bool TestJavaScriptOrderJSON2::_internal_has_d() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool TestJavaScriptOrderJSON2::has_d() const {
  return _internal_has_d();
}
inline void TestJavaScriptOrderJSON2::clear_d() {
  d_ = 0;
  _has_bits_[0] &= ~0x00000001u;
}
inline int32_t TestJavaScriptOrderJSON2::_internal_d() const {
  return d_;
}
inline int32_t TestJavaScriptOrderJSON2::d() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestJavaScriptOrderJSON2.d)
  return _internal_d();
}
inline void TestJavaScriptOrderJSON2::_internal_set_d(int32_t value) {
  _has_bits_[0] |= 0x00000001u;
  d_ = value;
}
inline void TestJavaScriptOrderJSON2::set_d(int32_t value) {
  _internal_set_d(value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestJavaScriptOrderJSON2.d)
}

// optional int32 c = 2;
inline bool TestJavaScriptOrderJSON2::_internal_has_c() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool TestJavaScriptOrderJSON2::has_c() const {
  return _internal_has_c();
}
inline void TestJavaScriptOrderJSON2::clear_c() {
  c_ = 0;
  _has_bits_[0] &= ~0x00000002u;
}
inline int32_t TestJavaScriptOrderJSON2::_internal_c() const {
  return c_;
}
inline int32_t TestJavaScriptOrderJSON2::c() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestJavaScriptOrderJSON2.c)
  return _internal_c();
}
inline void TestJavaScriptOrderJSON2::_internal_set_c(int32_t value) {
  _has_bits_[0] |= 0x00000002u;
  c_ = value;
}
inline void TestJavaScriptOrderJSON2::set_c(int32_t value) {
  _internal_set_c(value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestJavaScriptOrderJSON2.c)
}

// optional bool x = 3;
inline bool TestJavaScriptOrderJSON2::_internal_has_x() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool TestJavaScriptOrderJSON2::has_x() const {
  return _internal_has_x();
}
inline void TestJavaScriptOrderJSON2::clear_x() {
  x_ = false;
  _has_bits_[0] &= ~0x00000004u;
}
inline bool TestJavaScriptOrderJSON2::_internal_x() const {
  return x_;
}
inline bool TestJavaScriptOrderJSON2::x() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestJavaScriptOrderJSON2.x)
  return _internal_x();
}
inline void TestJavaScriptOrderJSON2::_internal_set_x(bool value) {
  _has_bits_[0] |= 0x00000004u;
  x_ = value;
}
inline void TestJavaScriptOrderJSON2::set_x(bool value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestJavaScriptOrderJSON2.x)
}

// optional int32 b = 4;
inline bool TestJavaScriptOrderJSON2::_internal_has_b() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool TestJavaScriptOrderJSON2::has_b() const {
  return _internal_has_b();
}
inline void TestJavaScriptOrderJSON2::clear_b() {
  b_ = 0;
  _has_bits_[0] &= ~0x00000008u;
}
inline int32_t TestJavaScriptOrderJSON2::_internal_b() const {
  return b_;
}
inline int32_t TestJavaScriptOrderJSON2::b() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestJavaScriptOrderJSON2.b)
  return _internal_b();
}
inline void TestJavaScriptOrderJSON2::_internal_set_b(int32_t value) {
  _has_bits_[0] |= 0x00000008u;
  b_ = value;
}
inline void TestJavaScriptOrderJSON2::set_b(int32_t value) {
  _internal_set_b(value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestJavaScriptOrderJSON2.b)
}

// optional int32 a = 5;
inline bool TestJavaScriptOrderJSON2::_internal_has_a() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool TestJavaScriptOrderJSON2::has_a() const {
  return _internal_has_a();
}
inline void TestJavaScriptOrderJSON2::clear_a() {
  a_ = 0;
  _has_bits_[0] &= ~0x00000010u;
}
inline int32_t TestJavaScriptOrderJSON2::_internal_a() const {
  return a_;
}
inline int32_t TestJavaScriptOrderJSON2::a() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestJavaScriptOrderJSON2.a)
  return _internal_a();
}
inline void TestJavaScriptOrderJSON2::_internal_set_a(int32_t value) {
  _has_bits_[0] |= 0x00000010u;
  a_ = value;
}
inline void TestJavaScriptOrderJSON2::set_a(int32_t value) {
  _internal_set_a(value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestJavaScriptOrderJSON2.a)
}

// repeated .protobuf_unittest.TestJavaScriptOrderJSON1 z = 6;
inline int TestJavaScriptOrderJSON2::_internal_z_size() const {
  return z_.size();
}
inline int TestJavaScriptOrderJSON2::z_size() const {
  return _internal_z_size();
}
inline void TestJavaScriptOrderJSON2::clear_z() {
  z_.Clear();
}
inline ::protobuf_unittest::TestJavaScriptOrderJSON1* TestJavaScriptOrderJSON2::mutable_z(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestJavaScriptOrderJSON2.z)
  return z_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::protobuf_unittest::TestJavaScriptOrderJSON1 >*
TestJavaScriptOrderJSON2::mutable_z() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestJavaScriptOrderJSON2.z)
  return &z_;
}
inline const ::protobuf_unittest::TestJavaScriptOrderJSON1& TestJavaScriptOrderJSON2::_internal_z(int index) const {
  return z_.Get(index);
}
inline const ::protobuf_unittest::TestJavaScriptOrderJSON1& TestJavaScriptOrderJSON2::z(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestJavaScriptOrderJSON2.z)
  return _internal_z(index);
}
inline ::protobuf_unittest::TestJavaScriptOrderJSON1* TestJavaScriptOrderJSON2::_internal_add_z() {
  return z_.Add();
}
inline ::protobuf_unittest::TestJavaScriptOrderJSON1* TestJavaScriptOrderJSON2::add_z() {
  ::protobuf_unittest::TestJavaScriptOrderJSON1* _add = _internal_add_z();
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestJavaScriptOrderJSON2.z)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::protobuf_unittest::TestJavaScriptOrderJSON1 >&
TestJavaScriptOrderJSON2::z() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestJavaScriptOrderJSON2.z)
  return z_;
}

// -------------------------------------------------------------------

// TestLargeInt

// required int64 a = 1;
inline bool TestLargeInt::_internal_has_a() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool TestLargeInt::has_a() const {
  return _internal_has_a();
}
inline void TestLargeInt::clear_a() {
  a_ = int64_t{0};
  _has_bits_[0] &= ~0x00000001u;
}
inline int64_t TestLargeInt::_internal_a() const {
  return a_;
}
inline int64_t TestLargeInt::a() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestLargeInt.a)
  return _internal_a();
}
inline void TestLargeInt::_internal_set_a(int64_t value) {
  _has_bits_[0] |= 0x00000001u;
  a_ = value;
}
inline void TestLargeInt::set_a(int64_t value) {
  _internal_set_a(value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestLargeInt.a)
}

// required uint64 b = 2;
inline bool TestLargeInt::_internal_has_b() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool TestLargeInt::has_b() const {
  return _internal_has_b();
}
inline void TestLargeInt::clear_b() {
  b_ = uint64_t{0u};
  _has_bits_[0] &= ~0x00000002u;
}
inline uint64_t TestLargeInt::_internal_b() const {
  return b_;
}
inline uint64_t TestLargeInt::b() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestLargeInt.b)
  return _internal_b();
}
inline void TestLargeInt::_internal_set_b(uint64_t value) {
  _has_bits_[0] |= 0x00000002u;
  b_ = value;
}
inline void TestLargeInt::set_b(uint64_t value) {
  _internal_set_b(value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestLargeInt.b)
}

// -------------------------------------------------------------------

// TestNumbers

// optional .protobuf_unittest.TestNumbers.MyType a = 1;
inline bool TestNumbers::_internal_has_a() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool TestNumbers::has_a() const {
  return _internal_has_a();
}
inline void TestNumbers::clear_a() {
  a_ = 0;
  _has_bits_[0] &= ~0x00000001u;
}
inline ::protobuf_unittest::TestNumbers_MyType TestNumbers::_internal_a() const {
  return static_cast< ::protobuf_unittest::TestNumbers_MyType >(a_);
}
inline ::protobuf_unittest::TestNumbers_MyType TestNumbers::a() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestNumbers.a)
  return _internal_a();
}
inline void TestNumbers::_internal_set_a(::protobuf_unittest::TestNumbers_MyType value) {
  assert(::protobuf_unittest::TestNumbers_MyType_IsValid(value));
  _has_bits_[0] |= 0x00000001u;
  a_ = value;
}
inline void TestNumbers::set_a(::protobuf_unittest::TestNumbers_MyType value) {
  _internal_set_a(value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestNumbers.a)
}

// optional int32 b = 2;
inline bool TestNumbers::_internal_has_b() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool TestNumbers::has_b() const {
  return _internal_has_b();
}
inline void TestNumbers::clear_b() {
  b_ = 0;
  _has_bits_[0] &= ~0x00000002u;
}
inline int32_t TestNumbers::_internal_b() const {
  return b_;
}
inline int32_t TestNumbers::b() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestNumbers.b)
  return _internal_b();
}
inline void TestNumbers::_internal_set_b(int32_t value) {
  _has_bits_[0] |= 0x00000002u;
  b_ = value;
}
inline void TestNumbers::set_b(int32_t value) {
  _internal_set_b(value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestNumbers.b)
}

// optional float c = 3;
inline bool TestNumbers::_internal_has_c() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool TestNumbers::has_c() const {
  return _internal_has_c();
}
inline void TestNumbers::clear_c() {
  c_ = 0;
  _has_bits_[0] &= ~0x00000004u;
}
inline float TestNumbers::_internal_c() const {
  return c_;
}
inline float TestNumbers::c() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestNumbers.c)
  return _internal_c();
}
inline void TestNumbers::_internal_set_c(float value) {
  _has_bits_[0] |= 0x00000004u;
  c_ = value;
}
inline void TestNumbers::set_c(float value) {
  _internal_set_c(value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestNumbers.c)
}

// optional bool d = 4;
inline bool TestNumbers::_internal_has_d() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool TestNumbers::has_d() const {
  return _internal_has_d();
}
inline void TestNumbers::clear_d() {
  d_ = false;
  _has_bits_[0] &= ~0x00000008u;
}
inline bool TestNumbers::_internal_d() const {
  return d_;
}
inline bool TestNumbers::d() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestNumbers.d)
  return _internal_d();
}
inline void TestNumbers::_internal_set_d(bool value) {
  _has_bits_[0] |= 0x00000008u;
  d_ = value;
}
inline void TestNumbers::set_d(bool value) {
  _internal_set_d(value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestNumbers.d)
}

// optional double e = 5;
inline bool TestNumbers::_internal_has_e() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool TestNumbers::has_e() const {
  return _internal_has_e();
}
inline void TestNumbers::clear_e() {
  e_ = 0;
  _has_bits_[0] &= ~0x00000010u;
}
inline double TestNumbers::_internal_e() const {
  return e_;
}
inline double TestNumbers::e() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestNumbers.e)
  return _internal_e();
}
inline void TestNumbers::_internal_set_e(double value) {
  _has_bits_[0] |= 0x00000010u;
  e_ = value;
}
inline void TestNumbers::set_e(double value) {
  _internal_set_e(value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestNumbers.e)
}

// optional uint32 f = 6;
inline bool TestNumbers::_internal_has_f() const {
  bool value = (_has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool TestNumbers::has_f() const {
  return _internal_has_f();
}
inline void TestNumbers::clear_f() {
  f_ = 0u;
  _has_bits_[0] &= ~0x00000020u;
}
inline uint32_t TestNumbers::_internal_f() const {
  return f_;
}
inline uint32_t TestNumbers::f() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestNumbers.f)
  return _internal_f();
}
inline void TestNumbers::_internal_set_f(uint32_t value) {
  _has_bits_[0] |= 0x00000020u;
  f_ = value;
}
inline void TestNumbers::set_f(uint32_t value) {
  _internal_set_f(value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestNumbers.f)
}

// -------------------------------------------------------------------

// TestCamelCase

// optional string normal_field = 1;
inline bool TestCamelCase::_internal_has_normal_field() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool TestCamelCase::has_normal_field() const {
  return _internal_has_normal_field();
}
inline void TestCamelCase::clear_normal_field() {
  normal_field_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& TestCamelCase::normal_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestCamelCase.normal_field)
  return _internal_normal_field();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TestCamelCase::set_normal_field(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 normal_field_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestCamelCase.normal_field)
}
inline std::string* TestCamelCase::mutable_normal_field() {
  std::string* _s = _internal_mutable_normal_field();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestCamelCase.normal_field)
  return _s;
}
inline const std::string& TestCamelCase::_internal_normal_field() const {
  return normal_field_.Get();
}
inline void TestCamelCase::_internal_set_normal_field(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  normal_field_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* TestCamelCase::_internal_mutable_normal_field() {
  _has_bits_[0] |= 0x00000001u;
  return normal_field_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* TestCamelCase::release_normal_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestCamelCase.normal_field)
  if (!_internal_has_normal_field()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = normal_field_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (normal_field_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    normal_field_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void TestCamelCase::set_allocated_normal_field(std::string* normal_field) {
  if (normal_field != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  normal_field_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), normal_field,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (normal_field_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    normal_field_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestCamelCase.normal_field)
}

// optional int32 CAPITAL_FIELD = 2;
inline bool TestCamelCase::_internal_has_capital_field() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool TestCamelCase::has_capital_field() const {
  return _internal_has_capital_field();
}
inline void TestCamelCase::clear_capital_field() {
  capital_field_ = 0;
  _has_bits_[0] &= ~0x00000002u;
}
inline int32_t TestCamelCase::_internal_capital_field() const {
  return capital_field_;
}
inline int32_t TestCamelCase::capital_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestCamelCase.CAPITAL_FIELD)
  return _internal_capital_field();
}
inline void TestCamelCase::_internal_set_capital_field(int32_t value) {
  _has_bits_[0] |= 0x00000002u;
  capital_field_ = value;
}
inline void TestCamelCase::set_capital_field(int32_t value) {
  _internal_set_capital_field(value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestCamelCase.CAPITAL_FIELD)
}

// optional int32 CamelCaseField = 3;
inline bool TestCamelCase::_internal_has_camelcasefield() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool TestCamelCase::has_camelcasefield() const {
  return _internal_has_camelcasefield();
}
inline void TestCamelCase::clear_camelcasefield() {
  camelcasefield_ = 0;
  _has_bits_[0] &= ~0x00000004u;
}
inline int32_t TestCamelCase::_internal_camelcasefield() const {
  return camelcasefield_;
}
inline int32_t TestCamelCase::camelcasefield() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestCamelCase.CamelCaseField)
  return _internal_camelcasefield();
}
inline void TestCamelCase::_internal_set_camelcasefield(int32_t value) {
  _has_bits_[0] |= 0x00000004u;
  camelcasefield_ = value;
}
inline void TestCamelCase::set_camelcasefield(int32_t value) {
  _internal_set_camelcasefield(value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestCamelCase.CamelCaseField)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// TestBoolMap

// map<bool, int32> bool_map = 1;
inline int TestBoolMap::_internal_bool_map_size() const {
  return bool_map_.size();
}
inline int TestBoolMap::bool_map_size() const {
  return _internal_bool_map_size();
}
inline void TestBoolMap::clear_bool_map() {
  bool_map_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >&
TestBoolMap::_internal_bool_map() const {
  return bool_map_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >&
TestBoolMap::bool_map() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestBoolMap.bool_map)
  return _internal_bool_map();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >*
TestBoolMap::_internal_mutable_bool_map() {
  return bool_map_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >*
TestBoolMap::mutable_bool_map() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestBoolMap.bool_map)
  return _internal_mutable_bool_map();
}

// -------------------------------------------------------------------

// TestRecursion

// optional int32 value = 1;
inline bool TestRecursion::_internal_has_value() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool TestRecursion::has_value() const {
  return _internal_has_value();
}
inline void TestRecursion::clear_value() {
  value_ = 0;
  _has_bits_[0] &= ~0x00000002u;
}
inline int32_t TestRecursion::_internal_value() const {
  return value_;
}
inline int32_t TestRecursion::value() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestRecursion.value)
  return _internal_value();
}
inline void TestRecursion::_internal_set_value(int32_t value) {
  _has_bits_[0] |= 0x00000002u;
  value_ = value;
}
inline void TestRecursion::set_value(int32_t value) {
  _internal_set_value(value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestRecursion.value)
}

// optional .protobuf_unittest.TestRecursion child = 2;
inline bool TestRecursion::_internal_has_child() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || child_ != nullptr);
  return value;
}
inline bool TestRecursion::has_child() const {
  return _internal_has_child();
}
inline void TestRecursion::clear_child() {
  if (child_ != nullptr) child_->Clear();
  _has_bits_[0] &= ~0x00000001u;
}
inline const ::protobuf_unittest::TestRecursion& TestRecursion::_internal_child() const {
  const ::protobuf_unittest::TestRecursion* p = child_;
  return p != nullptr ? *p : reinterpret_cast<const ::protobuf_unittest::TestRecursion&>(
      ::protobuf_unittest::_TestRecursion_default_instance_);
}
inline const ::protobuf_unittest::TestRecursion& TestRecursion::child() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestRecursion.child)
  return _internal_child();
}
inline void TestRecursion::unsafe_arena_set_allocated_child(
    ::protobuf_unittest::TestRecursion* child) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(child_);
  }
  child_ = child;
  if (child) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:protobuf_unittest.TestRecursion.child)
}
inline ::protobuf_unittest::TestRecursion* TestRecursion::release_child() {
  _has_bits_[0] &= ~0x00000001u;
  ::protobuf_unittest::TestRecursion* temp = child_;
  child_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::protobuf_unittest::TestRecursion* TestRecursion::unsafe_arena_release_child() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestRecursion.child)
  _has_bits_[0] &= ~0x00000001u;
  ::protobuf_unittest::TestRecursion* temp = child_;
  child_ = nullptr;
  return temp;
}
inline ::protobuf_unittest::TestRecursion* TestRecursion::_internal_mutable_child() {
  _has_bits_[0] |= 0x00000001u;
  if (child_ == nullptr) {
    auto* p = CreateMaybeMessage<::protobuf_unittest::TestRecursion>(GetArenaForAllocation());
    child_ = p;
  }
  return child_;
}
inline ::protobuf_unittest::TestRecursion* TestRecursion::mutable_child() {
  ::protobuf_unittest::TestRecursion* _msg = _internal_mutable_child();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestRecursion.child)
  return _msg;
}
inline void TestRecursion::set_allocated_child(::protobuf_unittest::TestRecursion* child) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete child_;
  }
  if (child) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::protobuf_unittest::TestRecursion>::GetOwningArena(child);
    if (message_arena != submessage_arena) {
      child = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, child, submessage_arena);
    }
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  child_ = child;
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestRecursion.child)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// TestStringMap

// map<string, string> string_map = 1;
inline int TestStringMap::_internal_string_map_size() const {
  return string_map_.size();
}
inline int TestStringMap::string_map_size() const {
  return _internal_string_map_size();
}
inline void TestStringMap::clear_string_map() {
  string_map_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
TestStringMap::_internal_string_map() const {
  return string_map_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
TestStringMap::string_map() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestStringMap.string_map)
  return _internal_string_map();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
TestStringMap::_internal_mutable_string_map() {
  return string_map_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
TestStringMap::mutable_string_map() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestStringMap.string_map)
  return _internal_mutable_string_map();
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// TestStringSerializer

// optional string scalar_string = 1;
inline bool TestStringSerializer::_internal_has_scalar_string() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool TestStringSerializer::has_scalar_string() const {
  return _internal_has_scalar_string();
}
inline void TestStringSerializer::clear_scalar_string() {
  scalar_string_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& TestStringSerializer::scalar_string() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestStringSerializer.scalar_string)
  return _internal_scalar_string();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TestStringSerializer::set_scalar_string(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 scalar_string_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestStringSerializer.scalar_string)
}
inline std::string* TestStringSerializer::mutable_scalar_string() {
  std::string* _s = _internal_mutable_scalar_string();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestStringSerializer.scalar_string)
  return _s;
}
inline const std::string& TestStringSerializer::_internal_scalar_string() const {
  return scalar_string_.Get();
}
inline void TestStringSerializer::_internal_set_scalar_string(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  scalar_string_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* TestStringSerializer::_internal_mutable_scalar_string() {
  _has_bits_[0] |= 0x00000001u;
  return scalar_string_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* TestStringSerializer::release_scalar_string() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestStringSerializer.scalar_string)
  if (!_internal_has_scalar_string()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = scalar_string_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (scalar_string_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    scalar_string_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void TestStringSerializer::set_allocated_scalar_string(std::string* scalar_string) {
  if (scalar_string != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  scalar_string_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), scalar_string,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (scalar_string_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    scalar_string_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestStringSerializer.scalar_string)
}

// repeated string repeated_string = 2;
inline int TestStringSerializer::_internal_repeated_string_size() const {
  return repeated_string_.size();
}
inline int TestStringSerializer::repeated_string_size() const {
  return _internal_repeated_string_size();
}
inline void TestStringSerializer::clear_repeated_string() {
  repeated_string_.Clear();
}
inline std::string* TestStringSerializer::add_repeated_string() {
  std::string* _s = _internal_add_repeated_string();
  // @@protoc_insertion_point(field_add_mutable:protobuf_unittest.TestStringSerializer.repeated_string)
  return _s;
}
inline const std::string& TestStringSerializer::_internal_repeated_string(int index) const {
  return repeated_string_.Get(index);
}
inline const std::string& TestStringSerializer::repeated_string(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestStringSerializer.repeated_string)
  return _internal_repeated_string(index);
}
inline std::string* TestStringSerializer::mutable_repeated_string(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestStringSerializer.repeated_string)
  return repeated_string_.Mutable(index);
}
inline void TestStringSerializer::set_repeated_string(int index, const std::string& value) {
  repeated_string_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestStringSerializer.repeated_string)
}
inline void TestStringSerializer::set_repeated_string(int index, std::string&& value) {
  repeated_string_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestStringSerializer.repeated_string)
}
inline void TestStringSerializer::set_repeated_string(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  repeated_string_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestStringSerializer.repeated_string)
}
inline void TestStringSerializer::set_repeated_string(int index, const char* value, size_t size) {
  repeated_string_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestStringSerializer.repeated_string)
}
inline std::string* TestStringSerializer::_internal_add_repeated_string() {
  return repeated_string_.Add();
}
inline void TestStringSerializer::add_repeated_string(const std::string& value) {
  repeated_string_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestStringSerializer.repeated_string)
}
inline void TestStringSerializer::add_repeated_string(std::string&& value) {
  repeated_string_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestStringSerializer.repeated_string)
}
inline void TestStringSerializer::add_repeated_string(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  repeated_string_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:protobuf_unittest.TestStringSerializer.repeated_string)
}
inline void TestStringSerializer::add_repeated_string(const char* value, size_t size) {
  repeated_string_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:protobuf_unittest.TestStringSerializer.repeated_string)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
TestStringSerializer::repeated_string() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestStringSerializer.repeated_string)
  return repeated_string_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
TestStringSerializer::mutable_repeated_string() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestStringSerializer.repeated_string)
  return &repeated_string_;
}

// map<string, string> string_map = 3;
inline int TestStringSerializer::_internal_string_map_size() const {
  return string_map_.size();
}
inline int TestStringSerializer::string_map_size() const {
  return _internal_string_map_size();
}
inline void TestStringSerializer::clear_string_map() {
  string_map_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
TestStringSerializer::_internal_string_map() const {
  return string_map_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
TestStringSerializer::string_map() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestStringSerializer.string_map)
  return _internal_string_map();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
TestStringSerializer::_internal_mutable_string_map() {
  return string_map_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
TestStringSerializer::mutable_string_map() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestStringSerializer.string_map)
  return _internal_mutable_string_map();
}

// -------------------------------------------------------------------

// TestMessageWithExtension

// -------------------------------------------------------------------

// TestExtension

// optional string value = 1;
inline bool TestExtension::_internal_has_value() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool TestExtension::has_value() const {
  return _internal_has_value();
}
inline void TestExtension::clear_value() {
  value_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& TestExtension::value() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestExtension.value)
  return _internal_value();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TestExtension::set_value(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestExtension.value)
}
inline std::string* TestExtension::mutable_value() {
  std::string* _s = _internal_mutable_value();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestExtension.value)
  return _s;
}
inline const std::string& TestExtension::_internal_value() const {
  return value_.Get();
}
inline void TestExtension::_internal_set_value(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* TestExtension::_internal_mutable_value() {
  _has_bits_[0] |= 0x00000001u;
  return value_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* TestExtension::release_value() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestExtension.value)
  if (!_internal_has_value()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = value_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (value_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void TestExtension::set_allocated_value(std::string* value) {
  if (value != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  value_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (value_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestExtension.value)
}

// -------------------------------------------------------------------

// TestDefaultEnumValue

// optional .protobuf_unittest.EnumValue enum_value = 1 [default = DEFAULT];
inline bool TestDefaultEnumValue::_internal_has_enum_value() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool TestDefaultEnumValue::has_enum_value() const {
  return _internal_has_enum_value();
}
inline void TestDefaultEnumValue::clear_enum_value() {
  enum_value_ = 2;
  _has_bits_[0] &= ~0x00000001u;
}
inline ::protobuf_unittest::EnumValue TestDefaultEnumValue::_internal_enum_value() const {
  return static_cast< ::protobuf_unittest::EnumValue >(enum_value_);
}
inline ::protobuf_unittest::EnumValue TestDefaultEnumValue::enum_value() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestDefaultEnumValue.enum_value)
  return _internal_enum_value();
}
inline void TestDefaultEnumValue::_internal_set_enum_value(::protobuf_unittest::EnumValue value) {
  assert(::protobuf_unittest::EnumValue_IsValid(value));
  _has_bits_[0] |= 0x00000001u;
  enum_value_ = value;
}
inline void TestDefaultEnumValue::set_enum_value(::protobuf_unittest::EnumValue value) {
  _internal_set_enum_value(value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestDefaultEnumValue.enum_value)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace protobuf_unittest

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::protobuf_unittest::TestNumbers_MyType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::protobuf_unittest::TestNumbers_MyType>() {
  return ::protobuf_unittest::TestNumbers_MyType_descriptor();
}
template <> struct is_proto_enum< ::protobuf_unittest::EnumValue> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::protobuf_unittest::EnumValue>() {
  return ::protobuf_unittest::EnumValue_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_google_2fprotobuf_2futil_2fjson_5fformat_2eproto
