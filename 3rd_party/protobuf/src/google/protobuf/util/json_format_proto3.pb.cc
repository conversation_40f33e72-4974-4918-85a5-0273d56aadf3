// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/util/json_format_proto3.proto

#include "google/protobuf/util/json_format_proto3.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace proto3 {
constexpr MessageType::MessageType(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : value_(0){}
struct MessageTypeDefaultTypeInternal {
  constexpr MessageTypeDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MessageTypeDefaultTypeInternal() {}
  union {
    MessageType _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MessageTypeDefaultTypeInternal _MessageType_default_instance_;
constexpr TestMessage::TestMessage(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : repeated_bool_value_()
  , repeated_int32_value_()
  , _repeated_int32_value_cached_byte_size_(0)
  , repeated_int64_value_()
  , _repeated_int64_value_cached_byte_size_(0)
  , repeated_uint32_value_()
  , _repeated_uint32_value_cached_byte_size_(0)
  , repeated_uint64_value_()
  , _repeated_uint64_value_cached_byte_size_(0)
  , repeated_float_value_()
  , repeated_double_value_()
  , repeated_string_value_()
  , repeated_bytes_value_()
  , repeated_enum_value_()
  , _repeated_enum_value_cached_byte_size_(0)
  , repeated_message_value_()
  , string_value_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , bytes_value_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , message_value_(nullptr)
  , bool_value_(false)
  , int32_value_(0)
  , int64_value_(int64_t{0})
  , uint64_value_(uint64_t{0u})
  , uint32_value_(0u)
  , float_value_(0)
  , double_value_(0)
  , enum_value_(0)
{}
struct TestMessageDefaultTypeInternal {
  constexpr TestMessageDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestMessageDefaultTypeInternal() {}
  union {
    TestMessage _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestMessageDefaultTypeInternal _TestMessage_default_instance_;
constexpr TestOneof::TestOneof(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : _oneof_case_{}{}
struct TestOneofDefaultTypeInternal {
  constexpr TestOneofDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestOneofDefaultTypeInternal() {}
  union {
    TestOneof _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestOneofDefaultTypeInternal _TestOneof_default_instance_;
constexpr TestMap_BoolMapEntry_DoNotUse::TestMap_BoolMapEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct TestMap_BoolMapEntry_DoNotUseDefaultTypeInternal {
  constexpr TestMap_BoolMapEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestMap_BoolMapEntry_DoNotUseDefaultTypeInternal() {}
  union {
    TestMap_BoolMapEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestMap_BoolMapEntry_DoNotUseDefaultTypeInternal _TestMap_BoolMapEntry_DoNotUse_default_instance_;
constexpr TestMap_Int32MapEntry_DoNotUse::TestMap_Int32MapEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct TestMap_Int32MapEntry_DoNotUseDefaultTypeInternal {
  constexpr TestMap_Int32MapEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestMap_Int32MapEntry_DoNotUseDefaultTypeInternal() {}
  union {
    TestMap_Int32MapEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestMap_Int32MapEntry_DoNotUseDefaultTypeInternal _TestMap_Int32MapEntry_DoNotUse_default_instance_;
constexpr TestMap_Int64MapEntry_DoNotUse::TestMap_Int64MapEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct TestMap_Int64MapEntry_DoNotUseDefaultTypeInternal {
  constexpr TestMap_Int64MapEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestMap_Int64MapEntry_DoNotUseDefaultTypeInternal() {}
  union {
    TestMap_Int64MapEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestMap_Int64MapEntry_DoNotUseDefaultTypeInternal _TestMap_Int64MapEntry_DoNotUse_default_instance_;
constexpr TestMap_Uint32MapEntry_DoNotUse::TestMap_Uint32MapEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct TestMap_Uint32MapEntry_DoNotUseDefaultTypeInternal {
  constexpr TestMap_Uint32MapEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestMap_Uint32MapEntry_DoNotUseDefaultTypeInternal() {}
  union {
    TestMap_Uint32MapEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestMap_Uint32MapEntry_DoNotUseDefaultTypeInternal _TestMap_Uint32MapEntry_DoNotUse_default_instance_;
constexpr TestMap_Uint64MapEntry_DoNotUse::TestMap_Uint64MapEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct TestMap_Uint64MapEntry_DoNotUseDefaultTypeInternal {
  constexpr TestMap_Uint64MapEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestMap_Uint64MapEntry_DoNotUseDefaultTypeInternal() {}
  union {
    TestMap_Uint64MapEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestMap_Uint64MapEntry_DoNotUseDefaultTypeInternal _TestMap_Uint64MapEntry_DoNotUse_default_instance_;
constexpr TestMap_StringMapEntry_DoNotUse::TestMap_StringMapEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct TestMap_StringMapEntry_DoNotUseDefaultTypeInternal {
  constexpr TestMap_StringMapEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestMap_StringMapEntry_DoNotUseDefaultTypeInternal() {}
  union {
    TestMap_StringMapEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestMap_StringMapEntry_DoNotUseDefaultTypeInternal _TestMap_StringMapEntry_DoNotUse_default_instance_;
constexpr TestMap::TestMap(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : bool_map_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , int32_map_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , int64_map_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , uint32_map_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , uint64_map_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , string_map_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}){}
struct TestMapDefaultTypeInternal {
  constexpr TestMapDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestMapDefaultTypeInternal() {}
  union {
    TestMap _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestMapDefaultTypeInternal _TestMap_default_instance_;
constexpr TestNestedMap_BoolMapEntry_DoNotUse::TestNestedMap_BoolMapEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct TestNestedMap_BoolMapEntry_DoNotUseDefaultTypeInternal {
  constexpr TestNestedMap_BoolMapEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestNestedMap_BoolMapEntry_DoNotUseDefaultTypeInternal() {}
  union {
    TestNestedMap_BoolMapEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestNestedMap_BoolMapEntry_DoNotUseDefaultTypeInternal _TestNestedMap_BoolMapEntry_DoNotUse_default_instance_;
constexpr TestNestedMap_Int32MapEntry_DoNotUse::TestNestedMap_Int32MapEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct TestNestedMap_Int32MapEntry_DoNotUseDefaultTypeInternal {
  constexpr TestNestedMap_Int32MapEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestNestedMap_Int32MapEntry_DoNotUseDefaultTypeInternal() {}
  union {
    TestNestedMap_Int32MapEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestNestedMap_Int32MapEntry_DoNotUseDefaultTypeInternal _TestNestedMap_Int32MapEntry_DoNotUse_default_instance_;
constexpr TestNestedMap_Int64MapEntry_DoNotUse::TestNestedMap_Int64MapEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct TestNestedMap_Int64MapEntry_DoNotUseDefaultTypeInternal {
  constexpr TestNestedMap_Int64MapEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestNestedMap_Int64MapEntry_DoNotUseDefaultTypeInternal() {}
  union {
    TestNestedMap_Int64MapEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestNestedMap_Int64MapEntry_DoNotUseDefaultTypeInternal _TestNestedMap_Int64MapEntry_DoNotUse_default_instance_;
constexpr TestNestedMap_Uint32MapEntry_DoNotUse::TestNestedMap_Uint32MapEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct TestNestedMap_Uint32MapEntry_DoNotUseDefaultTypeInternal {
  constexpr TestNestedMap_Uint32MapEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestNestedMap_Uint32MapEntry_DoNotUseDefaultTypeInternal() {}
  union {
    TestNestedMap_Uint32MapEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestNestedMap_Uint32MapEntry_DoNotUseDefaultTypeInternal _TestNestedMap_Uint32MapEntry_DoNotUse_default_instance_;
constexpr TestNestedMap_Uint64MapEntry_DoNotUse::TestNestedMap_Uint64MapEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct TestNestedMap_Uint64MapEntry_DoNotUseDefaultTypeInternal {
  constexpr TestNestedMap_Uint64MapEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestNestedMap_Uint64MapEntry_DoNotUseDefaultTypeInternal() {}
  union {
    TestNestedMap_Uint64MapEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestNestedMap_Uint64MapEntry_DoNotUseDefaultTypeInternal _TestNestedMap_Uint64MapEntry_DoNotUse_default_instance_;
constexpr TestNestedMap_StringMapEntry_DoNotUse::TestNestedMap_StringMapEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct TestNestedMap_StringMapEntry_DoNotUseDefaultTypeInternal {
  constexpr TestNestedMap_StringMapEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestNestedMap_StringMapEntry_DoNotUseDefaultTypeInternal() {}
  union {
    TestNestedMap_StringMapEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestNestedMap_StringMapEntry_DoNotUseDefaultTypeInternal _TestNestedMap_StringMapEntry_DoNotUse_default_instance_;
constexpr TestNestedMap_MapMapEntry_DoNotUse::TestNestedMap_MapMapEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct TestNestedMap_MapMapEntry_DoNotUseDefaultTypeInternal {
  constexpr TestNestedMap_MapMapEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestNestedMap_MapMapEntry_DoNotUseDefaultTypeInternal() {}
  union {
    TestNestedMap_MapMapEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestNestedMap_MapMapEntry_DoNotUseDefaultTypeInternal _TestNestedMap_MapMapEntry_DoNotUse_default_instance_;
constexpr TestNestedMap::TestNestedMap(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : bool_map_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , int32_map_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , int64_map_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , uint32_map_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , uint64_map_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , string_map_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , map_map_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}){}
struct TestNestedMapDefaultTypeInternal {
  constexpr TestNestedMapDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestNestedMapDefaultTypeInternal() {}
  union {
    TestNestedMap _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestNestedMapDefaultTypeInternal _TestNestedMap_default_instance_;
constexpr TestStringMap_StringMapEntry_DoNotUse::TestStringMap_StringMapEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct TestStringMap_StringMapEntry_DoNotUseDefaultTypeInternal {
  constexpr TestStringMap_StringMapEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestStringMap_StringMapEntry_DoNotUseDefaultTypeInternal() {}
  union {
    TestStringMap_StringMapEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestStringMap_StringMapEntry_DoNotUseDefaultTypeInternal _TestStringMap_StringMapEntry_DoNotUse_default_instance_;
constexpr TestStringMap::TestStringMap(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : string_map_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}){}
struct TestStringMapDefaultTypeInternal {
  constexpr TestStringMapDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestStringMapDefaultTypeInternal() {}
  union {
    TestStringMap _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestStringMapDefaultTypeInternal _TestStringMap_default_instance_;
constexpr TestWrapper::TestWrapper(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : repeated_bool_value_()
  , repeated_int32_value_()
  , repeated_int64_value_()
  , repeated_uint32_value_()
  , repeated_uint64_value_()
  , repeated_float_value_()
  , repeated_double_value_()
  , repeated_string_value_()
  , repeated_bytes_value_()
  , bool_value_(nullptr)
  , int32_value_(nullptr)
  , int64_value_(nullptr)
  , uint32_value_(nullptr)
  , uint64_value_(nullptr)
  , float_value_(nullptr)
  , double_value_(nullptr)
  , string_value_(nullptr)
  , bytes_value_(nullptr){}
struct TestWrapperDefaultTypeInternal {
  constexpr TestWrapperDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestWrapperDefaultTypeInternal() {}
  union {
    TestWrapper _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestWrapperDefaultTypeInternal _TestWrapper_default_instance_;
constexpr TestTimestamp::TestTimestamp(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : repeated_value_()
  , value_(nullptr){}
struct TestTimestampDefaultTypeInternal {
  constexpr TestTimestampDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestTimestampDefaultTypeInternal() {}
  union {
    TestTimestamp _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestTimestampDefaultTypeInternal _TestTimestamp_default_instance_;
constexpr TestDuration::TestDuration(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : repeated_value_()
  , value_(nullptr){}
struct TestDurationDefaultTypeInternal {
  constexpr TestDurationDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestDurationDefaultTypeInternal() {}
  union {
    TestDuration _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestDurationDefaultTypeInternal _TestDuration_default_instance_;
constexpr TestFieldMask::TestFieldMask(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : value_(nullptr){}
struct TestFieldMaskDefaultTypeInternal {
  constexpr TestFieldMaskDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestFieldMaskDefaultTypeInternal() {}
  union {
    TestFieldMask _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestFieldMaskDefaultTypeInternal _TestFieldMask_default_instance_;
constexpr TestStruct::TestStruct(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : repeated_value_()
  , value_(nullptr){}
struct TestStructDefaultTypeInternal {
  constexpr TestStructDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestStructDefaultTypeInternal() {}
  union {
    TestStruct _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestStructDefaultTypeInternal _TestStruct_default_instance_;
constexpr TestAny::TestAny(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : repeated_value_()
  , value_(nullptr){}
struct TestAnyDefaultTypeInternal {
  constexpr TestAnyDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestAnyDefaultTypeInternal() {}
  union {
    TestAny _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestAnyDefaultTypeInternal _TestAny_default_instance_;
constexpr TestValue::TestValue(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : repeated_value_()
  , value_(nullptr){}
struct TestValueDefaultTypeInternal {
  constexpr TestValueDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestValueDefaultTypeInternal() {}
  union {
    TestValue _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestValueDefaultTypeInternal _TestValue_default_instance_;
constexpr TestListValue::TestListValue(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : repeated_value_()
  , value_(nullptr){}
struct TestListValueDefaultTypeInternal {
  constexpr TestListValueDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestListValueDefaultTypeInternal() {}
  union {
    TestListValue _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestListValueDefaultTypeInternal _TestListValue_default_instance_;
constexpr TestBoolValue_BoolMapEntry_DoNotUse::TestBoolValue_BoolMapEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct TestBoolValue_BoolMapEntry_DoNotUseDefaultTypeInternal {
  constexpr TestBoolValue_BoolMapEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestBoolValue_BoolMapEntry_DoNotUseDefaultTypeInternal() {}
  union {
    TestBoolValue_BoolMapEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestBoolValue_BoolMapEntry_DoNotUseDefaultTypeInternal _TestBoolValue_BoolMapEntry_DoNotUse_default_instance_;
constexpr TestBoolValue::TestBoolValue(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : bool_map_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , bool_value_(false){}
struct TestBoolValueDefaultTypeInternal {
  constexpr TestBoolValueDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestBoolValueDefaultTypeInternal() {}
  union {
    TestBoolValue _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestBoolValueDefaultTypeInternal _TestBoolValue_default_instance_;
constexpr TestCustomJsonName::TestCustomJsonName(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : value_(0){}
struct TestCustomJsonNameDefaultTypeInternal {
  constexpr TestCustomJsonNameDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestCustomJsonNameDefaultTypeInternal() {}
  union {
    TestCustomJsonName _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestCustomJsonNameDefaultTypeInternal _TestCustomJsonName_default_instance_;
constexpr TestExtensions::TestExtensions(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : extensions_(nullptr){}
struct TestExtensionsDefaultTypeInternal {
  constexpr TestExtensionsDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestExtensionsDefaultTypeInternal() {}
  union {
    TestExtensions _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestExtensionsDefaultTypeInternal _TestExtensions_default_instance_;
constexpr TestEnumValue::TestEnumValue(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : enum_value1_(0)

  , enum_value2_(0)

  , enum_value3_(0)
{}
struct TestEnumValueDefaultTypeInternal {
  constexpr TestEnumValueDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestEnumValueDefaultTypeInternal() {}
  union {
    TestEnumValue _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestEnumValueDefaultTypeInternal _TestEnumValue_default_instance_;
}  // namespace proto3
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[33];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto = nullptr;

const uint32_t TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::proto3::MessageType, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::MessageType, value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::proto3::TestMessage, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestMessage, bool_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMessage, int32_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMessage, int64_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMessage, uint32_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMessage, uint64_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMessage, float_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMessage, double_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMessage, string_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMessage, bytes_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMessage, enum_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMessage, message_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMessage, repeated_bool_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMessage, repeated_int32_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMessage, repeated_int64_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMessage, repeated_uint32_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMessage, repeated_uint64_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMessage, repeated_float_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMessage, repeated_double_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMessage, repeated_string_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMessage, repeated_bytes_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMessage, repeated_enum_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMessage, repeated_message_value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::proto3::TestOneof, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::proto3::TestOneof, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::proto3::TestOneof, oneof_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap_BoolMapEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap_BoolMapEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap_BoolMapEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap_BoolMapEntry_DoNotUse, value_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap_Int32MapEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap_Int32MapEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap_Int32MapEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap_Int32MapEntry_DoNotUse, value_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap_Int64MapEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap_Int64MapEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap_Int64MapEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap_Int64MapEntry_DoNotUse, value_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap_Uint32MapEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap_Uint32MapEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap_Uint32MapEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap_Uint32MapEntry_DoNotUse, value_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap_Uint64MapEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap_Uint64MapEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap_Uint64MapEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap_Uint64MapEntry_DoNotUse, value_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap_StringMapEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap_StringMapEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap_StringMapEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap_StringMapEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap, bool_map_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap, int32_map_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap, int64_map_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap, uint32_map_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap, uint64_map_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestMap, string_map_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap_BoolMapEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap_BoolMapEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap_BoolMapEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap_BoolMapEntry_DoNotUse, value_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap_Int32MapEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap_Int32MapEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap_Int32MapEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap_Int32MapEntry_DoNotUse, value_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap_Int64MapEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap_Int64MapEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap_Int64MapEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap_Int64MapEntry_DoNotUse, value_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap_Uint32MapEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap_Uint32MapEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap_Uint32MapEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap_Uint32MapEntry_DoNotUse, value_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap_Uint64MapEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap_Uint64MapEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap_Uint64MapEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap_Uint64MapEntry_DoNotUse, value_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap_StringMapEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap_StringMapEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap_StringMapEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap_StringMapEntry_DoNotUse, value_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap_MapMapEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap_MapMapEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap_MapMapEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap_MapMapEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap, bool_map_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap, int32_map_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap, int64_map_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap, uint32_map_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap, uint64_map_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap, string_map_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestNestedMap, map_map_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestStringMap_StringMapEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestStringMap_StringMapEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestStringMap_StringMapEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestStringMap_StringMapEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::proto3::TestStringMap, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestStringMap, string_map_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::proto3::TestWrapper, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestWrapper, bool_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestWrapper, int32_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestWrapper, int64_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestWrapper, uint32_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestWrapper, uint64_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestWrapper, float_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestWrapper, double_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestWrapper, string_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestWrapper, bytes_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestWrapper, repeated_bool_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestWrapper, repeated_int32_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestWrapper, repeated_int64_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestWrapper, repeated_uint32_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestWrapper, repeated_uint64_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestWrapper, repeated_float_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestWrapper, repeated_double_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestWrapper, repeated_string_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestWrapper, repeated_bytes_value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::proto3::TestTimestamp, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestTimestamp, value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestTimestamp, repeated_value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::proto3::TestDuration, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestDuration, value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestDuration, repeated_value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::proto3::TestFieldMask, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestFieldMask, value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::proto3::TestStruct, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestStruct, value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestStruct, repeated_value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::proto3::TestAny, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestAny, value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestAny, repeated_value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::proto3::TestValue, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestValue, value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestValue, repeated_value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::proto3::TestListValue, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestListValue, value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestListValue, repeated_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestBoolValue_BoolMapEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestBoolValue_BoolMapEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestBoolValue_BoolMapEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestBoolValue_BoolMapEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::proto3::TestBoolValue, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestBoolValue, bool_value_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestBoolValue, bool_map_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::proto3::TestCustomJsonName, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestCustomJsonName, value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::proto3::TestExtensions, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestExtensions, extensions_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::proto3::TestEnumValue, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::proto3::TestEnumValue, enum_value1_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestEnumValue, enum_value2_),
  PROTOBUF_FIELD_OFFSET(::proto3::TestEnumValue, enum_value3_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::proto3::MessageType)},
  { 7, -1, -1, sizeof(::proto3::TestMessage)},
  { 35, -1, -1, sizeof(::proto3::TestOneof)},
  { 48, 56, -1, sizeof(::proto3::TestMap_BoolMapEntry_DoNotUse)},
  { 58, 66, -1, sizeof(::proto3::TestMap_Int32MapEntry_DoNotUse)},
  { 68, 76, -1, sizeof(::proto3::TestMap_Int64MapEntry_DoNotUse)},
  { 78, 86, -1, sizeof(::proto3::TestMap_Uint32MapEntry_DoNotUse)},
  { 88, 96, -1, sizeof(::proto3::TestMap_Uint64MapEntry_DoNotUse)},
  { 98, 106, -1, sizeof(::proto3::TestMap_StringMapEntry_DoNotUse)},
  { 108, -1, -1, sizeof(::proto3::TestMap)},
  { 120, 128, -1, sizeof(::proto3::TestNestedMap_BoolMapEntry_DoNotUse)},
  { 130, 138, -1, sizeof(::proto3::TestNestedMap_Int32MapEntry_DoNotUse)},
  { 140, 148, -1, sizeof(::proto3::TestNestedMap_Int64MapEntry_DoNotUse)},
  { 150, 158, -1, sizeof(::proto3::TestNestedMap_Uint32MapEntry_DoNotUse)},
  { 160, 168, -1, sizeof(::proto3::TestNestedMap_Uint64MapEntry_DoNotUse)},
  { 170, 178, -1, sizeof(::proto3::TestNestedMap_StringMapEntry_DoNotUse)},
  { 180, 188, -1, sizeof(::proto3::TestNestedMap_MapMapEntry_DoNotUse)},
  { 190, -1, -1, sizeof(::proto3::TestNestedMap)},
  { 203, 211, -1, sizeof(::proto3::TestStringMap_StringMapEntry_DoNotUse)},
  { 213, -1, -1, sizeof(::proto3::TestStringMap)},
  { 220, -1, -1, sizeof(::proto3::TestWrapper)},
  { 244, -1, -1, sizeof(::proto3::TestTimestamp)},
  { 252, -1, -1, sizeof(::proto3::TestDuration)},
  { 260, -1, -1, sizeof(::proto3::TestFieldMask)},
  { 267, -1, -1, sizeof(::proto3::TestStruct)},
  { 275, -1, -1, sizeof(::proto3::TestAny)},
  { 283, -1, -1, sizeof(::proto3::TestValue)},
  { 291, -1, -1, sizeof(::proto3::TestListValue)},
  { 299, 307, -1, sizeof(::proto3::TestBoolValue_BoolMapEntry_DoNotUse)},
  { 309, -1, -1, sizeof(::proto3::TestBoolValue)},
  { 317, -1, -1, sizeof(::proto3::TestCustomJsonName)},
  { 324, -1, -1, sizeof(::proto3::TestExtensions)},
  { 331, -1, -1, sizeof(::proto3::TestEnumValue)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_MessageType_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestMessage_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestOneof_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestMap_BoolMapEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestMap_Int32MapEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestMap_Int64MapEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestMap_Uint32MapEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestMap_Uint64MapEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestMap_StringMapEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestMap_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestNestedMap_BoolMapEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestNestedMap_Int32MapEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestNestedMap_Int64MapEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestNestedMap_Uint32MapEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestNestedMap_Uint64MapEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestNestedMap_StringMapEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestNestedMap_MapMapEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestNestedMap_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestStringMap_StringMapEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestStringMap_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestWrapper_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestTimestamp_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestDuration_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestFieldMask_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestStruct_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestAny_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestValue_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestListValue_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestBoolValue_BoolMapEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestBoolValue_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestCustomJsonName_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestExtensions_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::proto3::_TestEnumValue_default_instance_),
};

const char descriptor_table_protodef_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n-google/protobuf/util/json_format_proto"
  "3.proto\022\006proto3\032\031google/protobuf/any.pro"
  "to\032\036google/protobuf/duration.proto\032 goog"
  "le/protobuf/field_mask.proto\032\034google/pro"
  "tobuf/struct.proto\032\037google/protobuf/time"
  "stamp.proto\032\036google/protobuf/wrappers.pr"
  "oto\032\036google/protobuf/unittest.proto\"\034\n\013M"
  "essageType\022\r\n\005value\030\001 \001(\005\"\224\005\n\013TestMessag"
  "e\022\022\n\nbool_value\030\001 \001(\010\022\023\n\013int32_value\030\002 \001"
  "(\005\022\023\n\013int64_value\030\003 \001(\003\022\024\n\014uint32_value\030"
  "\004 \001(\r\022\024\n\014uint64_value\030\005 \001(\004\022\023\n\013float_val"
  "ue\030\006 \001(\002\022\024\n\014double_value\030\007 \001(\001\022\024\n\014string"
  "_value\030\010 \001(\t\022\023\n\013bytes_value\030\t \001(\014\022$\n\nenu"
  "m_value\030\n \001(\0162\020.proto3.EnumType\022*\n\rmessa"
  "ge_value\030\013 \001(\0132\023.proto3.MessageType\022\033\n\023r"
  "epeated_bool_value\030\025 \003(\010\022\034\n\024repeated_int"
  "32_value\030\026 \003(\005\022\034\n\024repeated_int64_value\030\027"
  " \003(\003\022\035\n\025repeated_uint32_value\030\030 \003(\r\022\035\n\025r"
  "epeated_uint64_value\030\031 \003(\004\022\034\n\024repeated_f"
  "loat_value\030\032 \003(\002\022\035\n\025repeated_double_valu"
  "e\030\033 \003(\001\022\035\n\025repeated_string_value\030\034 \003(\t\022\034"
  "\n\024repeated_bytes_value\030\035 \003(\014\022-\n\023repeated"
  "_enum_value\030\036 \003(\0162\020.proto3.EnumType\0223\n\026r"
  "epeated_message_value\030\037 \003(\0132\023.proto3.Mes"
  "sageType\"\214\002\n\tTestOneof\022\033\n\021oneof_int32_va"
  "lue\030\001 \001(\005H\000\022\034\n\022oneof_string_value\030\002 \001(\tH"
  "\000\022\033\n\021oneof_bytes_value\030\003 \001(\014H\000\022,\n\020oneof_"
  "enum_value\030\004 \001(\0162\020.proto3.EnumTypeH\000\0222\n\023"
  "oneof_message_value\030\005 \001(\0132\023.proto3.Messa"
  "geTypeH\000\0226\n\020oneof_null_value\030\006 \001(\0162\032.goo"
  "gle.protobuf.NullValueH\000B\r\n\013oneof_value\""
  "\341\004\n\007TestMap\022.\n\010bool_map\030\001 \003(\0132\034.proto3.T"
  "estMap.BoolMapEntry\0220\n\tint32_map\030\002 \003(\0132\035"
  ".proto3.TestMap.Int32MapEntry\0220\n\tint64_m"
  "ap\030\003 \003(\0132\035.proto3.TestMap.Int64MapEntry\022"
  "2\n\nuint32_map\030\004 \003(\0132\036.proto3.TestMap.Uin"
  "t32MapEntry\0222\n\nuint64_map\030\005 \003(\0132\036.proto3"
  ".TestMap.Uint64MapEntry\0222\n\nstring_map\030\006 "
  "\003(\0132\036.proto3.TestMap.StringMapEntry\032.\n\014B"
  "oolMapEntry\022\013\n\003key\030\001 \001(\010\022\r\n\005value\030\002 \001(\005:"
  "\0028\001\032/\n\rInt32MapEntry\022\013\n\003key\030\001 \001(\005\022\r\n\005val"
  "ue\030\002 \001(\005:\0028\001\032/\n\rInt64MapEntry\022\013\n\003key\030\001 \001"
  "(\003\022\r\n\005value\030\002 \001(\005:\0028\001\0320\n\016Uint32MapEntry\022"
  "\013\n\003key\030\001 \001(\r\022\r\n\005value\030\002 \001(\005:\0028\001\0320\n\016Uint6"
  "4MapEntry\022\013\n\003key\030\001 \001(\004\022\r\n\005value\030\002 \001(\005:\0028"
  "\001\0320\n\016StringMapEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005valu"
  "e\030\002 \001(\005:\0028\001\"\205\006\n\rTestNestedMap\0224\n\010bool_ma"
  "p\030\001 \003(\0132\".proto3.TestNestedMap.BoolMapEn"
  "try\0226\n\tint32_map\030\002 \003(\0132#.proto3.TestNest"
  "edMap.Int32MapEntry\0226\n\tint64_map\030\003 \003(\0132#"
  ".proto3.TestNestedMap.Int64MapEntry\0228\n\nu"
  "int32_map\030\004 \003(\0132$.proto3.TestNestedMap.U"
  "int32MapEntry\0228\n\nuint64_map\030\005 \003(\0132$.prot"
  "o3.TestNestedMap.Uint64MapEntry\0228\n\nstrin"
  "g_map\030\006 \003(\0132$.proto3.TestNestedMap.Strin"
  "gMapEntry\0222\n\007map_map\030\007 \003(\0132!.proto3.Test"
  "NestedMap.MapMapEntry\032.\n\014BoolMapEntry\022\013\n"
  "\003key\030\001 \001(\010\022\r\n\005value\030\002 \001(\005:\0028\001\032/\n\rInt32Ma"
  "pEntry\022\013\n\003key\030\001 \001(\005\022\r\n\005value\030\002 \001(\005:\0028\001\032/"
  "\n\rInt64MapEntry\022\013\n\003key\030\001 \001(\003\022\r\n\005value\030\002 "
  "\001(\005:\0028\001\0320\n\016Uint32MapEntry\022\013\n\003key\030\001 \001(\r\022\r"
  "\n\005value\030\002 \001(\005:\0028\001\0320\n\016Uint64MapEntry\022\013\n\003k"
  "ey\030\001 \001(\004\022\r\n\005value\030\002 \001(\005:\0028\001\0320\n\016StringMap"
  "Entry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\005:\0028\001\032D\n"
  "\013MapMapEntry\022\013\n\003key\030\001 \001(\t\022$\n\005value\030\002 \001(\013"
  "2\025.proto3.TestNestedMap:\0028\001\"{\n\rTestStrin"
  "gMap\0228\n\nstring_map\030\001 \003(\0132$.proto3.TestSt"
  "ringMap.StringMapEntry\0320\n\016StringMapEntry"
  "\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001\"\356\007\n\013Tes"
  "tWrapper\022.\n\nbool_value\030\001 \001(\0132\032.google.pr"
  "otobuf.BoolValue\0220\n\013int32_value\030\002 \001(\0132\033."
  "google.protobuf.Int32Value\0220\n\013int64_valu"
  "e\030\003 \001(\0132\033.google.protobuf.Int64Value\0222\n\014"
  "uint32_value\030\004 \001(\0132\034.google.protobuf.UIn"
  "t32Value\0222\n\014uint64_value\030\005 \001(\0132\034.google."
  "protobuf.UInt64Value\0220\n\013float_value\030\006 \001("
  "\0132\033.google.protobuf.FloatValue\0222\n\014double"
  "_value\030\007 \001(\0132\034.google.protobuf.DoubleVal"
  "ue\0222\n\014string_value\030\010 \001(\0132\034.google.protob"
  "uf.StringValue\0220\n\013bytes_value\030\t \001(\0132\033.go"
  "ogle.protobuf.BytesValue\0227\n\023repeated_boo"
  "l_value\030\013 \003(\0132\032.google.protobuf.BoolValu"
  "e\0229\n\024repeated_int32_value\030\014 \003(\0132\033.google"
  ".protobuf.Int32Value\0229\n\024repeated_int64_v"
  "alue\030\r \003(\0132\033.google.protobuf.Int64Value\022"
  ";\n\025repeated_uint32_value\030\016 \003(\0132\034.google."
  "protobuf.UInt32Value\022;\n\025repeated_uint64_"
  "value\030\017 \003(\0132\034.google.protobuf.UInt64Valu"
  "e\0229\n\024repeated_float_value\030\020 \003(\0132\033.google"
  ".protobuf.FloatValue\022;\n\025repeated_double_"
  "value\030\021 \003(\0132\034.google.protobuf.DoubleValu"
  "e\022;\n\025repeated_string_value\030\022 \003(\0132\034.googl"
  "e.protobuf.StringValue\0229\n\024repeated_bytes"
  "_value\030\023 \003(\0132\033.google.protobuf.BytesValu"
  "e\"n\n\rTestTimestamp\022)\n\005value\030\001 \001(\0132\032.goog"
  "le.protobuf.Timestamp\0222\n\016repeated_value\030"
  "\002 \003(\0132\032.google.protobuf.Timestamp\"k\n\014Tes"
  "tDuration\022(\n\005value\030\001 \001(\0132\031.google.protob"
  "uf.Duration\0221\n\016repeated_value\030\002 \003(\0132\031.go"
  "ogle.protobuf.Duration\":\n\rTestFieldMask\022"
  ")\n\005value\030\001 \001(\0132\032.google.protobuf.FieldMa"
  "sk\"e\n\nTestStruct\022&\n\005value\030\001 \001(\0132\027.google"
  ".protobuf.Struct\022/\n\016repeated_value\030\002 \003(\013"
  "2\027.google.protobuf.Struct\"\\\n\007TestAny\022#\n\005"
  "value\030\001 \001(\0132\024.google.protobuf.Any\022,\n\016rep"
  "eated_value\030\002 \003(\0132\024.google.protobuf.Any\""
  "b\n\tTestValue\022%\n\005value\030\001 \001(\0132\026.google.pro"
  "tobuf.Value\022.\n\016repeated_value\030\002 \003(\0132\026.go"
  "ogle.protobuf.Value\"n\n\rTestListValue\022)\n\005"
  "value\030\001 \001(\0132\032.google.protobuf.ListValue\022"
  "2\n\016repeated_value\030\002 \003(\0132\032.google.protobu"
  "f.ListValue\"\211\001\n\rTestBoolValue\022\022\n\nbool_va"
  "lue\030\001 \001(\010\0224\n\010bool_map\030\002 \003(\0132\".proto3.Tes"
  "tBoolValue.BoolMapEntry\032.\n\014BoolMapEntry\022"
  "\013\n\003key\030\001 \001(\010\022\r\n\005value\030\002 \001(\005:\0028\001\"+\n\022TestC"
  "ustomJsonName\022\025\n\005value\030\001 \001(\005R\006@value\"J\n\016"
  "TestExtensions\0228\n\nextensions\030\001 \001(\0132$.pro"
  "tobuf_unittest.TestAllExtensions\"\204\001\n\rTes"
  "tEnumValue\022%\n\013enum_value1\030\001 \001(\0162\020.proto3"
  ".EnumType\022%\n\013enum_value2\030\002 \001(\0162\020.proto3."
  "EnumType\022%\n\013enum_value3\030\003 \001(\0162\020.proto3.E"
  "numType*\034\n\010EnumType\022\007\n\003FOO\020\000\022\007\n\003BAR\020\001B,\n"
  "\030com.google.protobuf.utilB\020JsonFormatPro"
  "to3b\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_deps[7] = {
  &::descriptor_table_google_2fprotobuf_2fany_2eproto,
  &::descriptor_table_google_2fprotobuf_2fduration_2eproto,
  &::descriptor_table_google_2fprotobuf_2ffield_5fmask_2eproto,
  &::descriptor_table_google_2fprotobuf_2fstruct_2eproto,
  &::descriptor_table_google_2fprotobuf_2ftimestamp_2eproto,
  &::descriptor_table_google_2fprotobuf_2funittest_2eproto,
  &::descriptor_table_google_2fprotobuf_2fwrappers_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto = {
  false, false, 4931, descriptor_table_protodef_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto, "google/protobuf/util/json_format_proto3.proto", 
  &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once, descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_deps, 7, 33,
  schemas, file_default_instances, TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto::offsets,
  file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto, file_level_enum_descriptors_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto, file_level_service_descriptors_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter() {
  return &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto(&descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto);
namespace proto3 {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* EnumType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto);
  return file_level_enum_descriptors_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[0];
}
bool EnumType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class MessageType::_Internal {
 public:
};

MessageType::MessageType(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:proto3.MessageType)
}
MessageType::MessageType(const MessageType& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  value_ = from.value_;
  // @@protoc_insertion_point(copy_constructor:proto3.MessageType)
}

inline void MessageType::SharedCtor() {
value_ = 0;
}

MessageType::~MessageType() {
  // @@protoc_insertion_point(destructor:proto3.MessageType)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void MessageType::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void MessageType::ArenaDtor(void* object) {
  MessageType* _this = reinterpret_cast< MessageType* >(object);
  (void)_this;
}
void MessageType::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MessageType::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void MessageType::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.MessageType)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  value_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MessageType::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 value = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          value_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* MessageType::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:proto3.MessageType)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 value = 1;
  if (this->_internal_value() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_value(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:proto3.MessageType)
  return target;
}

size_t MessageType::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.MessageType)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 value = 1;
  if (this->_internal_value() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_value());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MessageType::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    MessageType::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MessageType::GetClassData() const { return &_class_data_; }

void MessageType::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<MessageType *>(to)->MergeFrom(
      static_cast<const MessageType &>(from));
}


void MessageType::MergeFrom(const MessageType& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.MessageType)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_value() != 0) {
    _internal_set_value(from._internal_value());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MessageType::CopyFrom(const MessageType& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.MessageType)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MessageType::IsInitialized() const {
  return true;
}

void MessageType::InternalSwap(MessageType* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(value_, other->value_);
}

::PROTOBUF_NAMESPACE_ID::Metadata MessageType::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[0]);
}

// ===================================================================

class TestMessage::_Internal {
 public:
  static const ::proto3::MessageType& message_value(const TestMessage* msg);
};

const ::proto3::MessageType&
TestMessage::_Internal::message_value(const TestMessage* msg) {
  return *msg->message_value_;
}
TestMessage::TestMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  repeated_bool_value_(arena),
  repeated_int32_value_(arena),
  repeated_int64_value_(arena),
  repeated_uint32_value_(arena),
  repeated_uint64_value_(arena),
  repeated_float_value_(arena),
  repeated_double_value_(arena),
  repeated_string_value_(arena),
  repeated_bytes_value_(arena),
  repeated_enum_value_(arena),
  repeated_message_value_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:proto3.TestMessage)
}
TestMessage::TestMessage(const TestMessage& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      repeated_bool_value_(from.repeated_bool_value_),
      repeated_int32_value_(from.repeated_int32_value_),
      repeated_int64_value_(from.repeated_int64_value_),
      repeated_uint32_value_(from.repeated_uint32_value_),
      repeated_uint64_value_(from.repeated_uint64_value_),
      repeated_float_value_(from.repeated_float_value_),
      repeated_double_value_(from.repeated_double_value_),
      repeated_string_value_(from.repeated_string_value_),
      repeated_bytes_value_(from.repeated_bytes_value_),
      repeated_enum_value_(from.repeated_enum_value_),
      repeated_message_value_(from.repeated_message_value_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  string_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    string_value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_string_value().empty()) {
    string_value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_string_value(), 
      GetArenaForAllocation());
  }
  bytes_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    bytes_value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_bytes_value().empty()) {
    bytes_value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_bytes_value(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_message_value()) {
    message_value_ = new ::proto3::MessageType(*from.message_value_);
  } else {
    message_value_ = nullptr;
  }
  ::memcpy(&bool_value_, &from.bool_value_,
    static_cast<size_t>(reinterpret_cast<char*>(&enum_value_) -
    reinterpret_cast<char*>(&bool_value_)) + sizeof(enum_value_));
  // @@protoc_insertion_point(copy_constructor:proto3.TestMessage)
}

inline void TestMessage::SharedCtor() {
string_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  string_value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
bytes_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  bytes_value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&message_value_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&enum_value_) -
    reinterpret_cast<char*>(&message_value_)) + sizeof(enum_value_));
}

TestMessage::~TestMessage() {
  // @@protoc_insertion_point(destructor:proto3.TestMessage)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestMessage::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  string_value_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  bytes_value_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete message_value_;
}

void TestMessage::ArenaDtor(void* object) {
  TestMessage* _this = reinterpret_cast< TestMessage* >(object);
  (void)_this;
}
void TestMessage::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TestMessage::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestMessage)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  repeated_bool_value_.Clear();
  repeated_int32_value_.Clear();
  repeated_int64_value_.Clear();
  repeated_uint32_value_.Clear();
  repeated_uint64_value_.Clear();
  repeated_float_value_.Clear();
  repeated_double_value_.Clear();
  repeated_string_value_.Clear();
  repeated_bytes_value_.Clear();
  repeated_enum_value_.Clear();
  repeated_message_value_.Clear();
  string_value_.ClearToEmpty();
  bytes_value_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && message_value_ != nullptr) {
    delete message_value_;
  }
  message_value_ = nullptr;
  ::memset(&bool_value_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&enum_value_) -
      reinterpret_cast<char*>(&bool_value_)) + sizeof(enum_value_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestMessage::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool bool_value = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          bool_value_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 int32_value = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          int32_value_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 int64_value = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          int64_value_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 uint32_value = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          uint32_value_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 uint64_value = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          uint64_value_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float float_value = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 53)) {
          float_value_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // double double_value = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 57)) {
          double_value_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // string string_value = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          auto str = _internal_mutable_string_value();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "proto3.TestMessage.string_value"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bytes bytes_value = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 74)) {
          auto str = _internal_mutable_bytes_value();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .proto3.EnumType enum_value = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 80)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_enum_value(static_cast<::proto3::EnumType>(val));
        } else
          goto handle_unusual;
        continue;
      // .proto3.MessageType message_value = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 90)) {
          ptr = ctx->ParseMessage(_internal_mutable_message_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated bool repeated_bool_value = 21;
      case 21:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 170)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedBoolParser(_internal_mutable_repeated_bool_value(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 168) {
          _internal_add_repeated_bool_value(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated int32 repeated_int32_value = 22;
      case 22:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 178)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt32Parser(_internal_mutable_repeated_int32_value(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 176) {
          _internal_add_repeated_int32_value(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated int64 repeated_int64_value = 23;
      case 23:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 186)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt64Parser(_internal_mutable_repeated_int64_value(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 184) {
          _internal_add_repeated_int64_value(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated uint32 repeated_uint32_value = 24;
      case 24:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 194)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedUInt32Parser(_internal_mutable_repeated_uint32_value(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 192) {
          _internal_add_repeated_uint32_value(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated uint64 repeated_uint64_value = 25;
      case 25:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 202)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedUInt64Parser(_internal_mutable_repeated_uint64_value(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 200) {
          _internal_add_repeated_uint64_value(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated float repeated_float_value = 26;
      case 26:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 210)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedFloatParser(_internal_mutable_repeated_float_value(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 213) {
          _internal_add_repeated_float_value(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr));
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // repeated double repeated_double_value = 27;
      case 27:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 218)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedDoubleParser(_internal_mutable_repeated_double_value(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 217) {
          _internal_add_repeated_double_value(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr));
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // repeated string repeated_string_value = 28;
      case 28:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 226)) {
          ptr -= 2;
          do {
            ptr += 2;
            auto str = _internal_add_repeated_string_value();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "proto3.TestMessage.repeated_string_value"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<226>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated bytes repeated_bytes_value = 29;
      case 29:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 234)) {
          ptr -= 2;
          do {
            ptr += 2;
            auto str = _internal_add_repeated_bytes_value();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<234>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .proto3.EnumType repeated_enum_value = 30;
      case 30:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 242)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedEnumParser(_internal_mutable_repeated_enum_value(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 240) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_add_repeated_enum_value(static_cast<::proto3::EnumType>(val));
        } else
          goto handle_unusual;
        continue;
      // repeated .proto3.MessageType repeated_message_value = 31;
      case 31:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 250)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(_internal_add_repeated_message_value(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<250>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestMessage::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestMessage)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool bool_value = 1;
  if (this->_internal_bool_value() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_bool_value(), target);
  }

  // int32 int32_value = 2;
  if (this->_internal_int32_value() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_int32_value(), target);
  }

  // int64 int64_value = 3;
  if (this->_internal_int64_value() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(3, this->_internal_int64_value(), target);
  }

  // uint32 uint32_value = 4;
  if (this->_internal_uint32_value() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_uint32_value(), target);
  }

  // uint64 uint64_value = 5;
  if (this->_internal_uint64_value() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(5, this->_internal_uint64_value(), target);
  }

  // float float_value = 6;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_float_value = this->_internal_float_value();
  uint32_t raw_float_value;
  memcpy(&raw_float_value, &tmp_float_value, sizeof(tmp_float_value));
  if (raw_float_value != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(6, this->_internal_float_value(), target);
  }

  // double double_value = 7;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_double_value = this->_internal_double_value();
  uint64_t raw_double_value;
  memcpy(&raw_double_value, &tmp_double_value, sizeof(tmp_double_value));
  if (raw_double_value != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(7, this->_internal_double_value(), target);
  }

  // string string_value = 8;
  if (!this->_internal_string_value().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_string_value().data(), static_cast<int>(this->_internal_string_value().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "proto3.TestMessage.string_value");
    target = stream->WriteStringMaybeAliased(
        8, this->_internal_string_value(), target);
  }

  // bytes bytes_value = 9;
  if (!this->_internal_bytes_value().empty()) {
    target = stream->WriteBytesMaybeAliased(
        9, this->_internal_bytes_value(), target);
  }

  // .proto3.EnumType enum_value = 10;
  if (this->_internal_enum_value() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      10, this->_internal_enum_value(), target);
  }

  // .proto3.MessageType message_value = 11;
  if (this->_internal_has_message_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        11, _Internal::message_value(this), target, stream);
  }

  // repeated bool repeated_bool_value = 21;
  if (this->_internal_repeated_bool_value_size() > 0) {
    target = stream->WriteFixedPacked(21, _internal_repeated_bool_value(), target);
  }

  // repeated int32 repeated_int32_value = 22;
  {
    int byte_size = _repeated_int32_value_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt32Packed(
          22, _internal_repeated_int32_value(), byte_size, target);
    }
  }

  // repeated int64 repeated_int64_value = 23;
  {
    int byte_size = _repeated_int64_value_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt64Packed(
          23, _internal_repeated_int64_value(), byte_size, target);
    }
  }

  // repeated uint32 repeated_uint32_value = 24;
  {
    int byte_size = _repeated_uint32_value_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteUInt32Packed(
          24, _internal_repeated_uint32_value(), byte_size, target);
    }
  }

  // repeated uint64 repeated_uint64_value = 25;
  {
    int byte_size = _repeated_uint64_value_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteUInt64Packed(
          25, _internal_repeated_uint64_value(), byte_size, target);
    }
  }

  // repeated float repeated_float_value = 26;
  if (this->_internal_repeated_float_value_size() > 0) {
    target = stream->WriteFixedPacked(26, _internal_repeated_float_value(), target);
  }

  // repeated double repeated_double_value = 27;
  if (this->_internal_repeated_double_value_size() > 0) {
    target = stream->WriteFixedPacked(27, _internal_repeated_double_value(), target);
  }

  // repeated string repeated_string_value = 28;
  for (int i = 0, n = this->_internal_repeated_string_value_size(); i < n; i++) {
    const auto& s = this->_internal_repeated_string_value(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "proto3.TestMessage.repeated_string_value");
    target = stream->WriteString(28, s, target);
  }

  // repeated bytes repeated_bytes_value = 29;
  for (int i = 0, n = this->_internal_repeated_bytes_value_size(); i < n; i++) {
    const auto& s = this->_internal_repeated_bytes_value(i);
    target = stream->WriteBytes(29, s, target);
  }

  // repeated .proto3.EnumType repeated_enum_value = 30;
  {
    int byte_size = _repeated_enum_value_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteEnumPacked(
          30, repeated_enum_value_, byte_size, target);
    }
  }

  // repeated .proto3.MessageType repeated_message_value = 31;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_repeated_message_value_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(31, this->_internal_repeated_message_value(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestMessage)
  return target;
}

size_t TestMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestMessage)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated bool repeated_bool_value = 21;
  {
    unsigned int count = static_cast<unsigned int>(this->_internal_repeated_bool_value_size());
    size_t data_size = 1UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    total_size += data_size;
  }

  // repeated int32 repeated_int32_value = 22;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int32Size(this->repeated_int32_value_);
    if (data_size > 0) {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _repeated_int32_value_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated int64 repeated_int64_value = 23;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int64Size(this->repeated_int64_value_);
    if (data_size > 0) {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _repeated_int64_value_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated uint32 repeated_uint32_value = 24;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      UInt32Size(this->repeated_uint32_value_);
    if (data_size > 0) {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _repeated_uint32_value_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated uint64 repeated_uint64_value = 25;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      UInt64Size(this->repeated_uint64_value_);
    if (data_size > 0) {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _repeated_uint64_value_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated float repeated_float_value = 26;
  {
    unsigned int count = static_cast<unsigned int>(this->_internal_repeated_float_value_size());
    size_t data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    total_size += data_size;
  }

  // repeated double repeated_double_value = 27;
  {
    unsigned int count = static_cast<unsigned int>(this->_internal_repeated_double_value_size());
    size_t data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    total_size += data_size;
  }

  // repeated string repeated_string_value = 28;
  total_size += 2 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(repeated_string_value_.size());
  for (int i = 0, n = repeated_string_value_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      repeated_string_value_.Get(i));
  }

  // repeated bytes repeated_bytes_value = 29;
  total_size += 2 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(repeated_bytes_value_.size());
  for (int i = 0, n = repeated_bytes_value_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
      repeated_bytes_value_.Get(i));
  }

  // repeated .proto3.EnumType repeated_enum_value = 30;
  {
    size_t data_size = 0;
    unsigned int count = static_cast<unsigned int>(this->_internal_repeated_enum_value_size());for (unsigned int i = 0; i < count; i++) {
      data_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(
        this->_internal_repeated_enum_value(static_cast<int>(i)));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _repeated_enum_value_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated .proto3.MessageType repeated_message_value = 31;
  total_size += 2UL * this->_internal_repeated_message_value_size();
  for (const auto& msg : this->repeated_message_value_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string string_value = 8;
  if (!this->_internal_string_value().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_string_value());
  }

  // bytes bytes_value = 9;
  if (!this->_internal_bytes_value().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_bytes_value());
  }

  // .proto3.MessageType message_value = 11;
  if (this->_internal_has_message_value()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *message_value_);
  }

  // bool bool_value = 1;
  if (this->_internal_bool_value() != 0) {
    total_size += 1 + 1;
  }

  // int32 int32_value = 2;
  if (this->_internal_int32_value() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_int32_value());
  }

  // int64 int64_value = 3;
  if (this->_internal_int64_value() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_int64_value());
  }

  // uint64 uint64_value = 5;
  if (this->_internal_uint64_value() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_uint64_value());
  }

  // uint32 uint32_value = 4;
  if (this->_internal_uint32_value() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_uint32_value());
  }

  // float float_value = 6;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_float_value = this->_internal_float_value();
  uint32_t raw_float_value;
  memcpy(&raw_float_value, &tmp_float_value, sizeof(tmp_float_value));
  if (raw_float_value != 0) {
    total_size += 1 + 4;
  }

  // double double_value = 7;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_double_value = this->_internal_double_value();
  uint64_t raw_double_value;
  memcpy(&raw_double_value, &tmp_double_value, sizeof(tmp_double_value));
  if (raw_double_value != 0) {
    total_size += 1 + 8;
  }

  // .proto3.EnumType enum_value = 10;
  if (this->_internal_enum_value() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_enum_value());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestMessage::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestMessage::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestMessage::GetClassData() const { return &_class_data_; }

void TestMessage::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestMessage *>(to)->MergeFrom(
      static_cast<const TestMessage &>(from));
}


void TestMessage::MergeFrom(const TestMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestMessage)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  repeated_bool_value_.MergeFrom(from.repeated_bool_value_);
  repeated_int32_value_.MergeFrom(from.repeated_int32_value_);
  repeated_int64_value_.MergeFrom(from.repeated_int64_value_);
  repeated_uint32_value_.MergeFrom(from.repeated_uint32_value_);
  repeated_uint64_value_.MergeFrom(from.repeated_uint64_value_);
  repeated_float_value_.MergeFrom(from.repeated_float_value_);
  repeated_double_value_.MergeFrom(from.repeated_double_value_);
  repeated_string_value_.MergeFrom(from.repeated_string_value_);
  repeated_bytes_value_.MergeFrom(from.repeated_bytes_value_);
  repeated_enum_value_.MergeFrom(from.repeated_enum_value_);
  repeated_message_value_.MergeFrom(from.repeated_message_value_);
  if (!from._internal_string_value().empty()) {
    _internal_set_string_value(from._internal_string_value());
  }
  if (!from._internal_bytes_value().empty()) {
    _internal_set_bytes_value(from._internal_bytes_value());
  }
  if (from._internal_has_message_value()) {
    _internal_mutable_message_value()->::proto3::MessageType::MergeFrom(from._internal_message_value());
  }
  if (from._internal_bool_value() != 0) {
    _internal_set_bool_value(from._internal_bool_value());
  }
  if (from._internal_int32_value() != 0) {
    _internal_set_int32_value(from._internal_int32_value());
  }
  if (from._internal_int64_value() != 0) {
    _internal_set_int64_value(from._internal_int64_value());
  }
  if (from._internal_uint64_value() != 0) {
    _internal_set_uint64_value(from._internal_uint64_value());
  }
  if (from._internal_uint32_value() != 0) {
    _internal_set_uint32_value(from._internal_uint32_value());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_float_value = from._internal_float_value();
  uint32_t raw_float_value;
  memcpy(&raw_float_value, &tmp_float_value, sizeof(tmp_float_value));
  if (raw_float_value != 0) {
    _internal_set_float_value(from._internal_float_value());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_double_value = from._internal_double_value();
  uint64_t raw_double_value;
  memcpy(&raw_double_value, &tmp_double_value, sizeof(tmp_double_value));
  if (raw_double_value != 0) {
    _internal_set_double_value(from._internal_double_value());
  }
  if (from._internal_enum_value() != 0) {
    _internal_set_enum_value(from._internal_enum_value());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestMessage::CopyFrom(const TestMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestMessage::IsInitialized() const {
  return true;
}

void TestMessage::InternalSwap(TestMessage* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  repeated_bool_value_.InternalSwap(&other->repeated_bool_value_);
  repeated_int32_value_.InternalSwap(&other->repeated_int32_value_);
  repeated_int64_value_.InternalSwap(&other->repeated_int64_value_);
  repeated_uint32_value_.InternalSwap(&other->repeated_uint32_value_);
  repeated_uint64_value_.InternalSwap(&other->repeated_uint64_value_);
  repeated_float_value_.InternalSwap(&other->repeated_float_value_);
  repeated_double_value_.InternalSwap(&other->repeated_double_value_);
  repeated_string_value_.InternalSwap(&other->repeated_string_value_);
  repeated_bytes_value_.InternalSwap(&other->repeated_bytes_value_);
  repeated_enum_value_.InternalSwap(&other->repeated_enum_value_);
  repeated_message_value_.InternalSwap(&other->repeated_message_value_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &string_value_, lhs_arena,
      &other->string_value_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &bytes_value_, lhs_arena,
      &other->bytes_value_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TestMessage, enum_value_)
      + sizeof(TestMessage::enum_value_)
      - PROTOBUF_FIELD_OFFSET(TestMessage, message_value_)>(
          reinterpret_cast<char*>(&message_value_),
          reinterpret_cast<char*>(&other->message_value_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TestMessage::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[1]);
}

// ===================================================================

class TestOneof::_Internal {
 public:
  static const ::proto3::MessageType& oneof_message_value(const TestOneof* msg);
};

const ::proto3::MessageType&
TestOneof::_Internal::oneof_message_value(const TestOneof* msg) {
  return *msg->oneof_value_.oneof_message_value_;
}
void TestOneof::set_allocated_oneof_message_value(::proto3::MessageType* oneof_message_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_oneof_value();
  if (oneof_message_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::proto3::MessageType>::GetOwningArena(oneof_message_value);
    if (message_arena != submessage_arena) {
      oneof_message_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, oneof_message_value, submessage_arena);
    }
    set_has_oneof_message_value();
    oneof_value_.oneof_message_value_ = oneof_message_value;
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestOneof.oneof_message_value)
}
TestOneof::TestOneof(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:proto3.TestOneof)
}
TestOneof::TestOneof(const TestOneof& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  clear_has_oneof_value();
  switch (from.oneof_value_case()) {
    case kOneofInt32Value: {
      _internal_set_oneof_int32_value(from._internal_oneof_int32_value());
      break;
    }
    case kOneofStringValue: {
      _internal_set_oneof_string_value(from._internal_oneof_string_value());
      break;
    }
    case kOneofBytesValue: {
      _internal_set_oneof_bytes_value(from._internal_oneof_bytes_value());
      break;
    }
    case kOneofEnumValue: {
      _internal_set_oneof_enum_value(from._internal_oneof_enum_value());
      break;
    }
    case kOneofMessageValue: {
      _internal_mutable_oneof_message_value()->::proto3::MessageType::MergeFrom(from._internal_oneof_message_value());
      break;
    }
    case kOneofNullValue: {
      _internal_set_oneof_null_value(from._internal_oneof_null_value());
      break;
    }
    case ONEOF_VALUE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:proto3.TestOneof)
}

inline void TestOneof::SharedCtor() {
clear_has_oneof_value();
}

TestOneof::~TestOneof() {
  // @@protoc_insertion_point(destructor:proto3.TestOneof)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestOneof::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (has_oneof_value()) {
    clear_oneof_value();
  }
}

void TestOneof::ArenaDtor(void* object) {
  TestOneof* _this = reinterpret_cast< TestOneof* >(object);
  (void)_this;
}
void TestOneof::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TestOneof::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestOneof::clear_oneof_value() {
// @@protoc_insertion_point(one_of_clear_start:proto3.TestOneof)
  switch (oneof_value_case()) {
    case kOneofInt32Value: {
      // No need to clear
      break;
    }
    case kOneofStringValue: {
      oneof_value_.oneof_string_value_.Destroy(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
      break;
    }
    case kOneofBytesValue: {
      oneof_value_.oneof_bytes_value_.Destroy(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
      break;
    }
    case kOneofEnumValue: {
      // No need to clear
      break;
    }
    case kOneofMessageValue: {
      if (GetArenaForAllocation() == nullptr) {
        delete oneof_value_.oneof_message_value_;
      }
      break;
    }
    case kOneofNullValue: {
      // No need to clear
      break;
    }
    case ONEOF_VALUE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = ONEOF_VALUE_NOT_SET;
}


void TestOneof::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestOneof)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_oneof_value();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestOneof::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 oneof_int32_value = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _internal_set_oneof_int32_value(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string oneof_string_value = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_oneof_string_value();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "proto3.TestOneof.oneof_string_value"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bytes oneof_bytes_value = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_oneof_bytes_value();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .proto3.EnumType oneof_enum_value = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_oneof_enum_value(static_cast<::proto3::EnumType>(val));
        } else
          goto handle_unusual;
        continue;
      // .proto3.MessageType oneof_message_value = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_oneof_message_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .google.protobuf.NullValue oneof_null_value = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_oneof_null_value(static_cast<::PROTOBUF_NAMESPACE_ID::NullValue>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestOneof::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestOneof)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 oneof_int32_value = 1;
  if (_internal_has_oneof_int32_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_oneof_int32_value(), target);
  }

  // string oneof_string_value = 2;
  if (_internal_has_oneof_string_value()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_oneof_string_value().data(), static_cast<int>(this->_internal_oneof_string_value().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "proto3.TestOneof.oneof_string_value");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_oneof_string_value(), target);
  }

  // bytes oneof_bytes_value = 3;
  if (_internal_has_oneof_bytes_value()) {
    target = stream->WriteBytesMaybeAliased(
        3, this->_internal_oneof_bytes_value(), target);
  }

  // .proto3.EnumType oneof_enum_value = 4;
  if (_internal_has_oneof_enum_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      4, this->_internal_oneof_enum_value(), target);
  }

  // .proto3.MessageType oneof_message_value = 5;
  if (_internal_has_oneof_message_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::oneof_message_value(this), target, stream);
  }

  // .google.protobuf.NullValue oneof_null_value = 6;
  if (_internal_has_oneof_null_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      6, this->_internal_oneof_null_value(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestOneof)
  return target;
}

size_t TestOneof::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestOneof)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  switch (oneof_value_case()) {
    // int32 oneof_int32_value = 1;
    case kOneofInt32Value: {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_oneof_int32_value());
      break;
    }
    // string oneof_string_value = 2;
    case kOneofStringValue: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_oneof_string_value());
      break;
    }
    // bytes oneof_bytes_value = 3;
    case kOneofBytesValue: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
          this->_internal_oneof_bytes_value());
      break;
    }
    // .proto3.EnumType oneof_enum_value = 4;
    case kOneofEnumValue: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_oneof_enum_value());
      break;
    }
    // .proto3.MessageType oneof_message_value = 5;
    case kOneofMessageValue: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *oneof_value_.oneof_message_value_);
      break;
    }
    // .google.protobuf.NullValue oneof_null_value = 6;
    case kOneofNullValue: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_oneof_null_value());
      break;
    }
    case ONEOF_VALUE_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestOneof::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestOneof::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestOneof::GetClassData() const { return &_class_data_; }

void TestOneof::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestOneof *>(to)->MergeFrom(
      static_cast<const TestOneof &>(from));
}


void TestOneof::MergeFrom(const TestOneof& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestOneof)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.oneof_value_case()) {
    case kOneofInt32Value: {
      _internal_set_oneof_int32_value(from._internal_oneof_int32_value());
      break;
    }
    case kOneofStringValue: {
      _internal_set_oneof_string_value(from._internal_oneof_string_value());
      break;
    }
    case kOneofBytesValue: {
      _internal_set_oneof_bytes_value(from._internal_oneof_bytes_value());
      break;
    }
    case kOneofEnumValue: {
      _internal_set_oneof_enum_value(from._internal_oneof_enum_value());
      break;
    }
    case kOneofMessageValue: {
      _internal_mutable_oneof_message_value()->::proto3::MessageType::MergeFrom(from._internal_oneof_message_value());
      break;
    }
    case kOneofNullValue: {
      _internal_set_oneof_null_value(from._internal_oneof_null_value());
      break;
    }
    case ONEOF_VALUE_NOT_SET: {
      break;
    }
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestOneof::CopyFrom(const TestOneof& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestOneof)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestOneof::IsInitialized() const {
  return true;
}

void TestOneof::InternalSwap(TestOneof* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(oneof_value_, other->oneof_value_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata TestOneof::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[2]);
}

// ===================================================================

TestMap_BoolMapEntry_DoNotUse::TestMap_BoolMapEntry_DoNotUse() {}
TestMap_BoolMapEntry_DoNotUse::TestMap_BoolMapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void TestMap_BoolMapEntry_DoNotUse::MergeFrom(const TestMap_BoolMapEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata TestMap_BoolMapEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[3]);
}

// ===================================================================

TestMap_Int32MapEntry_DoNotUse::TestMap_Int32MapEntry_DoNotUse() {}
TestMap_Int32MapEntry_DoNotUse::TestMap_Int32MapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void TestMap_Int32MapEntry_DoNotUse::MergeFrom(const TestMap_Int32MapEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata TestMap_Int32MapEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[4]);
}

// ===================================================================

TestMap_Int64MapEntry_DoNotUse::TestMap_Int64MapEntry_DoNotUse() {}
TestMap_Int64MapEntry_DoNotUse::TestMap_Int64MapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void TestMap_Int64MapEntry_DoNotUse::MergeFrom(const TestMap_Int64MapEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata TestMap_Int64MapEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[5]);
}

// ===================================================================

TestMap_Uint32MapEntry_DoNotUse::TestMap_Uint32MapEntry_DoNotUse() {}
TestMap_Uint32MapEntry_DoNotUse::TestMap_Uint32MapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void TestMap_Uint32MapEntry_DoNotUse::MergeFrom(const TestMap_Uint32MapEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata TestMap_Uint32MapEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[6]);
}

// ===================================================================

TestMap_Uint64MapEntry_DoNotUse::TestMap_Uint64MapEntry_DoNotUse() {}
TestMap_Uint64MapEntry_DoNotUse::TestMap_Uint64MapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void TestMap_Uint64MapEntry_DoNotUse::MergeFrom(const TestMap_Uint64MapEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata TestMap_Uint64MapEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[7]);
}

// ===================================================================

TestMap_StringMapEntry_DoNotUse::TestMap_StringMapEntry_DoNotUse() {}
TestMap_StringMapEntry_DoNotUse::TestMap_StringMapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void TestMap_StringMapEntry_DoNotUse::MergeFrom(const TestMap_StringMapEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata TestMap_StringMapEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[8]);
}

// ===================================================================

class TestMap::_Internal {
 public:
};

TestMap::TestMap(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  bool_map_(arena),
  int32_map_(arena),
  int64_map_(arena),
  uint32_map_(arena),
  uint64_map_(arena),
  string_map_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:proto3.TestMap)
}
TestMap::TestMap(const TestMap& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  bool_map_.MergeFrom(from.bool_map_);
  int32_map_.MergeFrom(from.int32_map_);
  int64_map_.MergeFrom(from.int64_map_);
  uint32_map_.MergeFrom(from.uint32_map_);
  uint64_map_.MergeFrom(from.uint64_map_);
  string_map_.MergeFrom(from.string_map_);
  // @@protoc_insertion_point(copy_constructor:proto3.TestMap)
}

inline void TestMap::SharedCtor() {
}

TestMap::~TestMap() {
  // @@protoc_insertion_point(destructor:proto3.TestMap)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestMap::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void TestMap::ArenaDtor(void* object) {
  TestMap* _this = reinterpret_cast< TestMap* >(object);
  (void)_this;
  _this->bool_map_. ~MapField();
  _this->int32_map_. ~MapField();
  _this->int64_map_. ~MapField();
  _this->uint32_map_. ~MapField();
  _this->uint64_map_. ~MapField();
  _this->string_map_. ~MapField();
}
inline void TestMap::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &TestMap::ArenaDtor);
  }
}
void TestMap::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestMap::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestMap)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  bool_map_.Clear();
  int32_map_.Clear();
  int64_map_.Clear();
  uint32_map_.Clear();
  uint64_map_.Clear();
  string_map_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestMap::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // map<bool, int32> bool_map = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&bool_map_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // map<int32, int32> int32_map = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&int32_map_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      // map<int64, int32> int64_map = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&int64_map_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      // map<uint32, int32> uint32_map = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&uint32_map_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      // map<uint64, int32> uint64_map = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&uint64_map_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else
          goto handle_unusual;
        continue;
      // map<string, int32> string_map = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&string_map_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<50>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestMap::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestMap)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // map<bool, int32> bool_map = 1;
  if (!this->_internal_bool_map().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >::const_pointer
        ConstPtr;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::SortItem< bool, ConstPtr > SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByFirstField<SortItem> Less;

    if (stream->IsSerializationDeterministic() &&
        this->_internal_bool_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_bool_map().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >::const_iterator
          it = this->_internal_bool_map().begin();
          it != this->_internal_bool_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = TestMap_BoolMapEntry_DoNotUse::Funcs::InternalSerialize(1, items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second, target, stream);
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >::const_iterator
          it = this->_internal_bool_map().begin();
          it != this->_internal_bool_map().end(); ++it) {
        target = TestMap_BoolMapEntry_DoNotUse::Funcs::InternalSerialize(1, it->first, it->second, target, stream);
      }
    }
  }

  // map<int32, int32> int32_map = 2;
  if (!this->_internal_int32_map().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int32_t >::const_pointer
        ConstPtr;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::SortItem< int32_t, ConstPtr > SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByFirstField<SortItem> Less;

    if (stream->IsSerializationDeterministic() &&
        this->_internal_int32_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_int32_map().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int32_t >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, int32_t >::const_iterator
          it = this->_internal_int32_map().begin();
          it != this->_internal_int32_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = TestMap_Int32MapEntry_DoNotUse::Funcs::InternalSerialize(2, items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second, target, stream);
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, int32_t >::const_iterator
          it = this->_internal_int32_map().begin();
          it != this->_internal_int32_map().end(); ++it) {
        target = TestMap_Int32MapEntry_DoNotUse::Funcs::InternalSerialize(2, it->first, it->second, target, stream);
      }
    }
  }

  // map<int64, int32> int64_map = 3;
  if (!this->_internal_int64_map().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >::const_pointer
        ConstPtr;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::SortItem< int64_t, ConstPtr > SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByFirstField<SortItem> Less;

    if (stream->IsSerializationDeterministic() &&
        this->_internal_int64_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_int64_map().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >::const_iterator
          it = this->_internal_int64_map().begin();
          it != this->_internal_int64_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = TestMap_Int64MapEntry_DoNotUse::Funcs::InternalSerialize(3, items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second, target, stream);
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >::const_iterator
          it = this->_internal_int64_map().begin();
          it != this->_internal_int64_map().end(); ++it) {
        target = TestMap_Int64MapEntry_DoNotUse::Funcs::InternalSerialize(3, it->first, it->second, target, stream);
      }
    }
  }

  // map<uint32, int32> uint32_map = 4;
  if (!this->_internal_uint32_map().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, int32_t >::const_pointer
        ConstPtr;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::SortItem< uint32_t, ConstPtr > SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByFirstField<SortItem> Less;

    if (stream->IsSerializationDeterministic() &&
        this->_internal_uint32_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_uint32_map().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, int32_t >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< uint32_t, int32_t >::const_iterator
          it = this->_internal_uint32_map().begin();
          it != this->_internal_uint32_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = TestMap_Uint32MapEntry_DoNotUse::Funcs::InternalSerialize(4, items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second, target, stream);
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< uint32_t, int32_t >::const_iterator
          it = this->_internal_uint32_map().begin();
          it != this->_internal_uint32_map().end(); ++it) {
        target = TestMap_Uint32MapEntry_DoNotUse::Funcs::InternalSerialize(4, it->first, it->second, target, stream);
      }
    }
  }

  // map<uint64, int32> uint64_map = 5;
  if (!this->_internal_uint64_map().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, int32_t >::const_pointer
        ConstPtr;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::SortItem< uint64_t, ConstPtr > SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByFirstField<SortItem> Less;

    if (stream->IsSerializationDeterministic() &&
        this->_internal_uint64_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_uint64_map().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, int32_t >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< uint64_t, int32_t >::const_iterator
          it = this->_internal_uint64_map().begin();
          it != this->_internal_uint64_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = TestMap_Uint64MapEntry_DoNotUse::Funcs::InternalSerialize(5, items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second, target, stream);
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< uint64_t, int32_t >::const_iterator
          it = this->_internal_uint64_map().begin();
          it != this->_internal_uint64_map().end(); ++it) {
        target = TestMap_Uint64MapEntry_DoNotUse::Funcs::InternalSerialize(5, it->first, it->second, target, stream);
      }
    }
  }

  // map<string, int32> string_map = 6;
  if (!this->_internal_string_map().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "proto3.TestMap.StringMapEntry.key");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_string_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_string_map().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >::const_iterator
          it = this->_internal_string_map().begin();
          it != this->_internal_string_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = TestMap_StringMapEntry_DoNotUse::Funcs::InternalSerialize(6, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >::const_iterator
          it = this->_internal_string_map().begin();
          it != this->_internal_string_map().end(); ++it) {
        target = TestMap_StringMapEntry_DoNotUse::Funcs::InternalSerialize(6, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestMap)
  return target;
}

size_t TestMap::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestMap)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<bool, int32> bool_map = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_bool_map_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >::const_iterator
      it = this->_internal_bool_map().begin();
      it != this->_internal_bool_map().end(); ++it) {
    total_size += TestMap_BoolMapEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // map<int32, int32> int32_map = 2;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_int32_map_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, int32_t >::const_iterator
      it = this->_internal_int32_map().begin();
      it != this->_internal_int32_map().end(); ++it) {
    total_size += TestMap_Int32MapEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // map<int64, int32> int64_map = 3;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_int64_map_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >::const_iterator
      it = this->_internal_int64_map().begin();
      it != this->_internal_int64_map().end(); ++it) {
    total_size += TestMap_Int64MapEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // map<uint32, int32> uint32_map = 4;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_uint32_map_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< uint32_t, int32_t >::const_iterator
      it = this->_internal_uint32_map().begin();
      it != this->_internal_uint32_map().end(); ++it) {
    total_size += TestMap_Uint32MapEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // map<uint64, int32> uint64_map = 5;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_uint64_map_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< uint64_t, int32_t >::const_iterator
      it = this->_internal_uint64_map().begin();
      it != this->_internal_uint64_map().end(); ++it) {
    total_size += TestMap_Uint64MapEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // map<string, int32> string_map = 6;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_string_map_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >::const_iterator
      it = this->_internal_string_map().begin();
      it != this->_internal_string_map().end(); ++it) {
    total_size += TestMap_StringMapEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestMap::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestMap::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestMap::GetClassData() const { return &_class_data_; }

void TestMap::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestMap *>(to)->MergeFrom(
      static_cast<const TestMap &>(from));
}


void TestMap::MergeFrom(const TestMap& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestMap)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  bool_map_.MergeFrom(from.bool_map_);
  int32_map_.MergeFrom(from.int32_map_);
  int64_map_.MergeFrom(from.int64_map_);
  uint32_map_.MergeFrom(from.uint32_map_);
  uint64_map_.MergeFrom(from.uint64_map_);
  string_map_.MergeFrom(from.string_map_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestMap::CopyFrom(const TestMap& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestMap)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestMap::IsInitialized() const {
  return true;
}

void TestMap::InternalSwap(TestMap* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  bool_map_.InternalSwap(&other->bool_map_);
  int32_map_.InternalSwap(&other->int32_map_);
  int64_map_.InternalSwap(&other->int64_map_);
  uint32_map_.InternalSwap(&other->uint32_map_);
  uint64_map_.InternalSwap(&other->uint64_map_);
  string_map_.InternalSwap(&other->string_map_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TestMap::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[9]);
}

// ===================================================================

TestNestedMap_BoolMapEntry_DoNotUse::TestNestedMap_BoolMapEntry_DoNotUse() {}
TestNestedMap_BoolMapEntry_DoNotUse::TestNestedMap_BoolMapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void TestNestedMap_BoolMapEntry_DoNotUse::MergeFrom(const TestNestedMap_BoolMapEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata TestNestedMap_BoolMapEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[10]);
}

// ===================================================================

TestNestedMap_Int32MapEntry_DoNotUse::TestNestedMap_Int32MapEntry_DoNotUse() {}
TestNestedMap_Int32MapEntry_DoNotUse::TestNestedMap_Int32MapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void TestNestedMap_Int32MapEntry_DoNotUse::MergeFrom(const TestNestedMap_Int32MapEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata TestNestedMap_Int32MapEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[11]);
}

// ===================================================================

TestNestedMap_Int64MapEntry_DoNotUse::TestNestedMap_Int64MapEntry_DoNotUse() {}
TestNestedMap_Int64MapEntry_DoNotUse::TestNestedMap_Int64MapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void TestNestedMap_Int64MapEntry_DoNotUse::MergeFrom(const TestNestedMap_Int64MapEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata TestNestedMap_Int64MapEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[12]);
}

// ===================================================================

TestNestedMap_Uint32MapEntry_DoNotUse::TestNestedMap_Uint32MapEntry_DoNotUse() {}
TestNestedMap_Uint32MapEntry_DoNotUse::TestNestedMap_Uint32MapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void TestNestedMap_Uint32MapEntry_DoNotUse::MergeFrom(const TestNestedMap_Uint32MapEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata TestNestedMap_Uint32MapEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[13]);
}

// ===================================================================

TestNestedMap_Uint64MapEntry_DoNotUse::TestNestedMap_Uint64MapEntry_DoNotUse() {}
TestNestedMap_Uint64MapEntry_DoNotUse::TestNestedMap_Uint64MapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void TestNestedMap_Uint64MapEntry_DoNotUse::MergeFrom(const TestNestedMap_Uint64MapEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata TestNestedMap_Uint64MapEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[14]);
}

// ===================================================================

TestNestedMap_StringMapEntry_DoNotUse::TestNestedMap_StringMapEntry_DoNotUse() {}
TestNestedMap_StringMapEntry_DoNotUse::TestNestedMap_StringMapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void TestNestedMap_StringMapEntry_DoNotUse::MergeFrom(const TestNestedMap_StringMapEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata TestNestedMap_StringMapEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[15]);
}

// ===================================================================

TestNestedMap_MapMapEntry_DoNotUse::TestNestedMap_MapMapEntry_DoNotUse() {}
TestNestedMap_MapMapEntry_DoNotUse::TestNestedMap_MapMapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void TestNestedMap_MapMapEntry_DoNotUse::MergeFrom(const TestNestedMap_MapMapEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata TestNestedMap_MapMapEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[16]);
}

// ===================================================================

class TestNestedMap::_Internal {
 public:
};

TestNestedMap::TestNestedMap(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  bool_map_(arena),
  int32_map_(arena),
  int64_map_(arena),
  uint32_map_(arena),
  uint64_map_(arena),
  string_map_(arena),
  map_map_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:proto3.TestNestedMap)
}
TestNestedMap::TestNestedMap(const TestNestedMap& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  bool_map_.MergeFrom(from.bool_map_);
  int32_map_.MergeFrom(from.int32_map_);
  int64_map_.MergeFrom(from.int64_map_);
  uint32_map_.MergeFrom(from.uint32_map_);
  uint64_map_.MergeFrom(from.uint64_map_);
  string_map_.MergeFrom(from.string_map_);
  map_map_.MergeFrom(from.map_map_);
  // @@protoc_insertion_point(copy_constructor:proto3.TestNestedMap)
}

inline void TestNestedMap::SharedCtor() {
}

TestNestedMap::~TestNestedMap() {
  // @@protoc_insertion_point(destructor:proto3.TestNestedMap)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestNestedMap::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void TestNestedMap::ArenaDtor(void* object) {
  TestNestedMap* _this = reinterpret_cast< TestNestedMap* >(object);
  (void)_this;
  _this->bool_map_. ~MapField();
  _this->int32_map_. ~MapField();
  _this->int64_map_. ~MapField();
  _this->uint32_map_. ~MapField();
  _this->uint64_map_. ~MapField();
  _this->string_map_. ~MapField();
  _this->map_map_. ~MapField();
}
inline void TestNestedMap::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &TestNestedMap::ArenaDtor);
  }
}
void TestNestedMap::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestNestedMap::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestNestedMap)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  bool_map_.Clear();
  int32_map_.Clear();
  int64_map_.Clear();
  uint32_map_.Clear();
  uint64_map_.Clear();
  string_map_.Clear();
  map_map_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestNestedMap::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // map<bool, int32> bool_map = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&bool_map_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // map<int32, int32> int32_map = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&int32_map_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      // map<int64, int32> int64_map = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&int64_map_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      // map<uint32, int32> uint32_map = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&uint32_map_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      // map<uint64, int32> uint64_map = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&uint64_map_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else
          goto handle_unusual;
        continue;
      // map<string, int32> string_map = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&string_map_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<50>(ptr));
        } else
          goto handle_unusual;
        continue;
      // map<string, .proto3.TestNestedMap> map_map = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&map_map_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<58>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestNestedMap::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestNestedMap)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // map<bool, int32> bool_map = 1;
  if (!this->_internal_bool_map().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >::const_pointer
        ConstPtr;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::SortItem< bool, ConstPtr > SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByFirstField<SortItem> Less;

    if (stream->IsSerializationDeterministic() &&
        this->_internal_bool_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_bool_map().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >::const_iterator
          it = this->_internal_bool_map().begin();
          it != this->_internal_bool_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = TestNestedMap_BoolMapEntry_DoNotUse::Funcs::InternalSerialize(1, items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second, target, stream);
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >::const_iterator
          it = this->_internal_bool_map().begin();
          it != this->_internal_bool_map().end(); ++it) {
        target = TestNestedMap_BoolMapEntry_DoNotUse::Funcs::InternalSerialize(1, it->first, it->second, target, stream);
      }
    }
  }

  // map<int32, int32> int32_map = 2;
  if (!this->_internal_int32_map().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int32_t >::const_pointer
        ConstPtr;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::SortItem< int32_t, ConstPtr > SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByFirstField<SortItem> Less;

    if (stream->IsSerializationDeterministic() &&
        this->_internal_int32_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_int32_map().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int32_t >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, int32_t >::const_iterator
          it = this->_internal_int32_map().begin();
          it != this->_internal_int32_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = TestNestedMap_Int32MapEntry_DoNotUse::Funcs::InternalSerialize(2, items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second, target, stream);
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, int32_t >::const_iterator
          it = this->_internal_int32_map().begin();
          it != this->_internal_int32_map().end(); ++it) {
        target = TestNestedMap_Int32MapEntry_DoNotUse::Funcs::InternalSerialize(2, it->first, it->second, target, stream);
      }
    }
  }

  // map<int64, int32> int64_map = 3;
  if (!this->_internal_int64_map().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >::const_pointer
        ConstPtr;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::SortItem< int64_t, ConstPtr > SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByFirstField<SortItem> Less;

    if (stream->IsSerializationDeterministic() &&
        this->_internal_int64_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_int64_map().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >::const_iterator
          it = this->_internal_int64_map().begin();
          it != this->_internal_int64_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = TestNestedMap_Int64MapEntry_DoNotUse::Funcs::InternalSerialize(3, items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second, target, stream);
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >::const_iterator
          it = this->_internal_int64_map().begin();
          it != this->_internal_int64_map().end(); ++it) {
        target = TestNestedMap_Int64MapEntry_DoNotUse::Funcs::InternalSerialize(3, it->first, it->second, target, stream);
      }
    }
  }

  // map<uint32, int32> uint32_map = 4;
  if (!this->_internal_uint32_map().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, int32_t >::const_pointer
        ConstPtr;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::SortItem< uint32_t, ConstPtr > SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByFirstField<SortItem> Less;

    if (stream->IsSerializationDeterministic() &&
        this->_internal_uint32_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_uint32_map().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, int32_t >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< uint32_t, int32_t >::const_iterator
          it = this->_internal_uint32_map().begin();
          it != this->_internal_uint32_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = TestNestedMap_Uint32MapEntry_DoNotUse::Funcs::InternalSerialize(4, items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second, target, stream);
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< uint32_t, int32_t >::const_iterator
          it = this->_internal_uint32_map().begin();
          it != this->_internal_uint32_map().end(); ++it) {
        target = TestNestedMap_Uint32MapEntry_DoNotUse::Funcs::InternalSerialize(4, it->first, it->second, target, stream);
      }
    }
  }

  // map<uint64, int32> uint64_map = 5;
  if (!this->_internal_uint64_map().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, int32_t >::const_pointer
        ConstPtr;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::SortItem< uint64_t, ConstPtr > SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByFirstField<SortItem> Less;

    if (stream->IsSerializationDeterministic() &&
        this->_internal_uint64_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_uint64_map().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, int32_t >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< uint64_t, int32_t >::const_iterator
          it = this->_internal_uint64_map().begin();
          it != this->_internal_uint64_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = TestNestedMap_Uint64MapEntry_DoNotUse::Funcs::InternalSerialize(5, items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second, target, stream);
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< uint64_t, int32_t >::const_iterator
          it = this->_internal_uint64_map().begin();
          it != this->_internal_uint64_map().end(); ++it) {
        target = TestNestedMap_Uint64MapEntry_DoNotUse::Funcs::InternalSerialize(5, it->first, it->second, target, stream);
      }
    }
  }

  // map<string, int32> string_map = 6;
  if (!this->_internal_string_map().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "proto3.TestNestedMap.StringMapEntry.key");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_string_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_string_map().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >::const_iterator
          it = this->_internal_string_map().begin();
          it != this->_internal_string_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = TestNestedMap_StringMapEntry_DoNotUse::Funcs::InternalSerialize(6, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >::const_iterator
          it = this->_internal_string_map().begin();
          it != this->_internal_string_map().end(); ++it) {
        target = TestNestedMap_StringMapEntry_DoNotUse::Funcs::InternalSerialize(6, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  // map<string, .proto3.TestNestedMap> map_map = 7;
  if (!this->_internal_map_map().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::proto3::TestNestedMap >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "proto3.TestNestedMap.MapMapEntry.key");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_map_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_map_map().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::proto3::TestNestedMap >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::proto3::TestNestedMap >::const_iterator
          it = this->_internal_map_map().begin();
          it != this->_internal_map_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = TestNestedMap_MapMapEntry_DoNotUse::Funcs::InternalSerialize(7, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::proto3::TestNestedMap >::const_iterator
          it = this->_internal_map_map().begin();
          it != this->_internal_map_map().end(); ++it) {
        target = TestNestedMap_MapMapEntry_DoNotUse::Funcs::InternalSerialize(7, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestNestedMap)
  return target;
}

size_t TestNestedMap::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestNestedMap)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<bool, int32> bool_map = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_bool_map_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >::const_iterator
      it = this->_internal_bool_map().begin();
      it != this->_internal_bool_map().end(); ++it) {
    total_size += TestNestedMap_BoolMapEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // map<int32, int32> int32_map = 2;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_int32_map_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, int32_t >::const_iterator
      it = this->_internal_int32_map().begin();
      it != this->_internal_int32_map().end(); ++it) {
    total_size += TestNestedMap_Int32MapEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // map<int64, int32> int64_map = 3;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_int64_map_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >::const_iterator
      it = this->_internal_int64_map().begin();
      it != this->_internal_int64_map().end(); ++it) {
    total_size += TestNestedMap_Int64MapEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // map<uint32, int32> uint32_map = 4;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_uint32_map_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< uint32_t, int32_t >::const_iterator
      it = this->_internal_uint32_map().begin();
      it != this->_internal_uint32_map().end(); ++it) {
    total_size += TestNestedMap_Uint32MapEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // map<uint64, int32> uint64_map = 5;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_uint64_map_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< uint64_t, int32_t >::const_iterator
      it = this->_internal_uint64_map().begin();
      it != this->_internal_uint64_map().end(); ++it) {
    total_size += TestNestedMap_Uint64MapEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // map<string, int32> string_map = 6;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_string_map_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >::const_iterator
      it = this->_internal_string_map().begin();
      it != this->_internal_string_map().end(); ++it) {
    total_size += TestNestedMap_StringMapEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // map<string, .proto3.TestNestedMap> map_map = 7;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_map_map_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::proto3::TestNestedMap >::const_iterator
      it = this->_internal_map_map().begin();
      it != this->_internal_map_map().end(); ++it) {
    total_size += TestNestedMap_MapMapEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestNestedMap::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestNestedMap::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestNestedMap::GetClassData() const { return &_class_data_; }

void TestNestedMap::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestNestedMap *>(to)->MergeFrom(
      static_cast<const TestNestedMap &>(from));
}


void TestNestedMap::MergeFrom(const TestNestedMap& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestNestedMap)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  bool_map_.MergeFrom(from.bool_map_);
  int32_map_.MergeFrom(from.int32_map_);
  int64_map_.MergeFrom(from.int64_map_);
  uint32_map_.MergeFrom(from.uint32_map_);
  uint64_map_.MergeFrom(from.uint64_map_);
  string_map_.MergeFrom(from.string_map_);
  map_map_.MergeFrom(from.map_map_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestNestedMap::CopyFrom(const TestNestedMap& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestNestedMap)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestNestedMap::IsInitialized() const {
  return true;
}

void TestNestedMap::InternalSwap(TestNestedMap* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  bool_map_.InternalSwap(&other->bool_map_);
  int32_map_.InternalSwap(&other->int32_map_);
  int64_map_.InternalSwap(&other->int64_map_);
  uint32_map_.InternalSwap(&other->uint32_map_);
  uint64_map_.InternalSwap(&other->uint64_map_);
  string_map_.InternalSwap(&other->string_map_);
  map_map_.InternalSwap(&other->map_map_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TestNestedMap::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[17]);
}

// ===================================================================

TestStringMap_StringMapEntry_DoNotUse::TestStringMap_StringMapEntry_DoNotUse() {}
TestStringMap_StringMapEntry_DoNotUse::TestStringMap_StringMapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void TestStringMap_StringMapEntry_DoNotUse::MergeFrom(const TestStringMap_StringMapEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata TestStringMap_StringMapEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[18]);
}

// ===================================================================

class TestStringMap::_Internal {
 public:
};

TestStringMap::TestStringMap(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  string_map_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:proto3.TestStringMap)
}
TestStringMap::TestStringMap(const TestStringMap& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  string_map_.MergeFrom(from.string_map_);
  // @@protoc_insertion_point(copy_constructor:proto3.TestStringMap)
}

inline void TestStringMap::SharedCtor() {
}

TestStringMap::~TestStringMap() {
  // @@protoc_insertion_point(destructor:proto3.TestStringMap)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestStringMap::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void TestStringMap::ArenaDtor(void* object) {
  TestStringMap* _this = reinterpret_cast< TestStringMap* >(object);
  (void)_this;
  _this->string_map_. ~MapField();
}
inline void TestStringMap::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &TestStringMap::ArenaDtor);
  }
}
void TestStringMap::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestStringMap::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestStringMap)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  string_map_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestStringMap::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // map<string, string> string_map = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&string_map_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestStringMap::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestStringMap)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, string> string_map = 1;
  if (!this->_internal_string_map().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "proto3.TestStringMap.StringMapEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "proto3.TestStringMap.StringMapEntry.value");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_string_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_string_map().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_string_map().begin();
          it != this->_internal_string_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = TestStringMap_StringMapEntry_DoNotUse::Funcs::InternalSerialize(1, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_string_map().begin();
          it != this->_internal_string_map().end(); ++it) {
        target = TestStringMap_StringMapEntry_DoNotUse::Funcs::InternalSerialize(1, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestStringMap)
  return target;
}

size_t TestStringMap::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestStringMap)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, string> string_map = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_string_map_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->_internal_string_map().begin();
      it != this->_internal_string_map().end(); ++it) {
    total_size += TestStringMap_StringMapEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestStringMap::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestStringMap::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestStringMap::GetClassData() const { return &_class_data_; }

void TestStringMap::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestStringMap *>(to)->MergeFrom(
      static_cast<const TestStringMap &>(from));
}


void TestStringMap::MergeFrom(const TestStringMap& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestStringMap)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  string_map_.MergeFrom(from.string_map_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestStringMap::CopyFrom(const TestStringMap& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestStringMap)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestStringMap::IsInitialized() const {
  return true;
}

void TestStringMap::InternalSwap(TestStringMap* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  string_map_.InternalSwap(&other->string_map_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TestStringMap::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[19]);
}

// ===================================================================

class TestWrapper::_Internal {
 public:
  static const ::PROTOBUF_NAMESPACE_ID::BoolValue& bool_value(const TestWrapper* msg);
  static const ::PROTOBUF_NAMESPACE_ID::Int32Value& int32_value(const TestWrapper* msg);
  static const ::PROTOBUF_NAMESPACE_ID::Int64Value& int64_value(const TestWrapper* msg);
  static const ::PROTOBUF_NAMESPACE_ID::UInt32Value& uint32_value(const TestWrapper* msg);
  static const ::PROTOBUF_NAMESPACE_ID::UInt64Value& uint64_value(const TestWrapper* msg);
  static const ::PROTOBUF_NAMESPACE_ID::FloatValue& float_value(const TestWrapper* msg);
  static const ::PROTOBUF_NAMESPACE_ID::DoubleValue& double_value(const TestWrapper* msg);
  static const ::PROTOBUF_NAMESPACE_ID::StringValue& string_value(const TestWrapper* msg);
  static const ::PROTOBUF_NAMESPACE_ID::BytesValue& bytes_value(const TestWrapper* msg);
};

const ::PROTOBUF_NAMESPACE_ID::BoolValue&
TestWrapper::_Internal::bool_value(const TestWrapper* msg) {
  return *msg->bool_value_;
}
const ::PROTOBUF_NAMESPACE_ID::Int32Value&
TestWrapper::_Internal::int32_value(const TestWrapper* msg) {
  return *msg->int32_value_;
}
const ::PROTOBUF_NAMESPACE_ID::Int64Value&
TestWrapper::_Internal::int64_value(const TestWrapper* msg) {
  return *msg->int64_value_;
}
const ::PROTOBUF_NAMESPACE_ID::UInt32Value&
TestWrapper::_Internal::uint32_value(const TestWrapper* msg) {
  return *msg->uint32_value_;
}
const ::PROTOBUF_NAMESPACE_ID::UInt64Value&
TestWrapper::_Internal::uint64_value(const TestWrapper* msg) {
  return *msg->uint64_value_;
}
const ::PROTOBUF_NAMESPACE_ID::FloatValue&
TestWrapper::_Internal::float_value(const TestWrapper* msg) {
  return *msg->float_value_;
}
const ::PROTOBUF_NAMESPACE_ID::DoubleValue&
TestWrapper::_Internal::double_value(const TestWrapper* msg) {
  return *msg->double_value_;
}
const ::PROTOBUF_NAMESPACE_ID::StringValue&
TestWrapper::_Internal::string_value(const TestWrapper* msg) {
  return *msg->string_value_;
}
const ::PROTOBUF_NAMESPACE_ID::BytesValue&
TestWrapper::_Internal::bytes_value(const TestWrapper* msg) {
  return *msg->bytes_value_;
}
void TestWrapper::clear_bool_value() {
  if (GetArenaForAllocation() == nullptr && bool_value_ != nullptr) {
    delete bool_value_;
  }
  bool_value_ = nullptr;
}
void TestWrapper::clear_int32_value() {
  if (GetArenaForAllocation() == nullptr && int32_value_ != nullptr) {
    delete int32_value_;
  }
  int32_value_ = nullptr;
}
void TestWrapper::clear_int64_value() {
  if (GetArenaForAllocation() == nullptr && int64_value_ != nullptr) {
    delete int64_value_;
  }
  int64_value_ = nullptr;
}
void TestWrapper::clear_uint32_value() {
  if (GetArenaForAllocation() == nullptr && uint32_value_ != nullptr) {
    delete uint32_value_;
  }
  uint32_value_ = nullptr;
}
void TestWrapper::clear_uint64_value() {
  if (GetArenaForAllocation() == nullptr && uint64_value_ != nullptr) {
    delete uint64_value_;
  }
  uint64_value_ = nullptr;
}
void TestWrapper::clear_float_value() {
  if (GetArenaForAllocation() == nullptr && float_value_ != nullptr) {
    delete float_value_;
  }
  float_value_ = nullptr;
}
void TestWrapper::clear_double_value() {
  if (GetArenaForAllocation() == nullptr && double_value_ != nullptr) {
    delete double_value_;
  }
  double_value_ = nullptr;
}
void TestWrapper::clear_string_value() {
  if (GetArenaForAllocation() == nullptr && string_value_ != nullptr) {
    delete string_value_;
  }
  string_value_ = nullptr;
}
void TestWrapper::clear_bytes_value() {
  if (GetArenaForAllocation() == nullptr && bytes_value_ != nullptr) {
    delete bytes_value_;
  }
  bytes_value_ = nullptr;
}
void TestWrapper::clear_repeated_bool_value() {
  repeated_bool_value_.Clear();
}
void TestWrapper::clear_repeated_int32_value() {
  repeated_int32_value_.Clear();
}
void TestWrapper::clear_repeated_int64_value() {
  repeated_int64_value_.Clear();
}
void TestWrapper::clear_repeated_uint32_value() {
  repeated_uint32_value_.Clear();
}
void TestWrapper::clear_repeated_uint64_value() {
  repeated_uint64_value_.Clear();
}
void TestWrapper::clear_repeated_float_value() {
  repeated_float_value_.Clear();
}
void TestWrapper::clear_repeated_double_value() {
  repeated_double_value_.Clear();
}
void TestWrapper::clear_repeated_string_value() {
  repeated_string_value_.Clear();
}
void TestWrapper::clear_repeated_bytes_value() {
  repeated_bytes_value_.Clear();
}
TestWrapper::TestWrapper(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  repeated_bool_value_(arena),
  repeated_int32_value_(arena),
  repeated_int64_value_(arena),
  repeated_uint32_value_(arena),
  repeated_uint64_value_(arena),
  repeated_float_value_(arena),
  repeated_double_value_(arena),
  repeated_string_value_(arena),
  repeated_bytes_value_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:proto3.TestWrapper)
}
TestWrapper::TestWrapper(const TestWrapper& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      repeated_bool_value_(from.repeated_bool_value_),
      repeated_int32_value_(from.repeated_int32_value_),
      repeated_int64_value_(from.repeated_int64_value_),
      repeated_uint32_value_(from.repeated_uint32_value_),
      repeated_uint64_value_(from.repeated_uint64_value_),
      repeated_float_value_(from.repeated_float_value_),
      repeated_double_value_(from.repeated_double_value_),
      repeated_string_value_(from.repeated_string_value_),
      repeated_bytes_value_(from.repeated_bytes_value_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_bool_value()) {
    bool_value_ = new ::PROTOBUF_NAMESPACE_ID::BoolValue(*from.bool_value_);
  } else {
    bool_value_ = nullptr;
  }
  if (from._internal_has_int32_value()) {
    int32_value_ = new ::PROTOBUF_NAMESPACE_ID::Int32Value(*from.int32_value_);
  } else {
    int32_value_ = nullptr;
  }
  if (from._internal_has_int64_value()) {
    int64_value_ = new ::PROTOBUF_NAMESPACE_ID::Int64Value(*from.int64_value_);
  } else {
    int64_value_ = nullptr;
  }
  if (from._internal_has_uint32_value()) {
    uint32_value_ = new ::PROTOBUF_NAMESPACE_ID::UInt32Value(*from.uint32_value_);
  } else {
    uint32_value_ = nullptr;
  }
  if (from._internal_has_uint64_value()) {
    uint64_value_ = new ::PROTOBUF_NAMESPACE_ID::UInt64Value(*from.uint64_value_);
  } else {
    uint64_value_ = nullptr;
  }
  if (from._internal_has_float_value()) {
    float_value_ = new ::PROTOBUF_NAMESPACE_ID::FloatValue(*from.float_value_);
  } else {
    float_value_ = nullptr;
  }
  if (from._internal_has_double_value()) {
    double_value_ = new ::PROTOBUF_NAMESPACE_ID::DoubleValue(*from.double_value_);
  } else {
    double_value_ = nullptr;
  }
  if (from._internal_has_string_value()) {
    string_value_ = new ::PROTOBUF_NAMESPACE_ID::StringValue(*from.string_value_);
  } else {
    string_value_ = nullptr;
  }
  if (from._internal_has_bytes_value()) {
    bytes_value_ = new ::PROTOBUF_NAMESPACE_ID::BytesValue(*from.bytes_value_);
  } else {
    bytes_value_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:proto3.TestWrapper)
}

inline void TestWrapper::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&bool_value_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&bytes_value_) -
    reinterpret_cast<char*>(&bool_value_)) + sizeof(bytes_value_));
}

TestWrapper::~TestWrapper() {
  // @@protoc_insertion_point(destructor:proto3.TestWrapper)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestWrapper::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete bool_value_;
  if (this != internal_default_instance()) delete int32_value_;
  if (this != internal_default_instance()) delete int64_value_;
  if (this != internal_default_instance()) delete uint32_value_;
  if (this != internal_default_instance()) delete uint64_value_;
  if (this != internal_default_instance()) delete float_value_;
  if (this != internal_default_instance()) delete double_value_;
  if (this != internal_default_instance()) delete string_value_;
  if (this != internal_default_instance()) delete bytes_value_;
}

void TestWrapper::ArenaDtor(void* object) {
  TestWrapper* _this = reinterpret_cast< TestWrapper* >(object);
  (void)_this;
}
void TestWrapper::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TestWrapper::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestWrapper::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestWrapper)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  repeated_bool_value_.Clear();
  repeated_int32_value_.Clear();
  repeated_int64_value_.Clear();
  repeated_uint32_value_.Clear();
  repeated_uint64_value_.Clear();
  repeated_float_value_.Clear();
  repeated_double_value_.Clear();
  repeated_string_value_.Clear();
  repeated_bytes_value_.Clear();
  if (GetArenaForAllocation() == nullptr && bool_value_ != nullptr) {
    delete bool_value_;
  }
  bool_value_ = nullptr;
  if (GetArenaForAllocation() == nullptr && int32_value_ != nullptr) {
    delete int32_value_;
  }
  int32_value_ = nullptr;
  if (GetArenaForAllocation() == nullptr && int64_value_ != nullptr) {
    delete int64_value_;
  }
  int64_value_ = nullptr;
  if (GetArenaForAllocation() == nullptr && uint32_value_ != nullptr) {
    delete uint32_value_;
  }
  uint32_value_ = nullptr;
  if (GetArenaForAllocation() == nullptr && uint64_value_ != nullptr) {
    delete uint64_value_;
  }
  uint64_value_ = nullptr;
  if (GetArenaForAllocation() == nullptr && float_value_ != nullptr) {
    delete float_value_;
  }
  float_value_ = nullptr;
  if (GetArenaForAllocation() == nullptr && double_value_ != nullptr) {
    delete double_value_;
  }
  double_value_ = nullptr;
  if (GetArenaForAllocation() == nullptr && string_value_ != nullptr) {
    delete string_value_;
  }
  string_value_ = nullptr;
  if (GetArenaForAllocation() == nullptr && bytes_value_ != nullptr) {
    delete bytes_value_;
  }
  bytes_value_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestWrapper::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .google.protobuf.BoolValue bool_value = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_bool_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .google.protobuf.Int32Value int32_value = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_int32_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .google.protobuf.Int64Value int64_value = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_int64_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .google.protobuf.UInt32Value uint32_value = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_uint32_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .google.protobuf.UInt64Value uint64_value = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_uint64_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .google.protobuf.FloatValue float_value = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_float_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .google.protobuf.DoubleValue double_value = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_double_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .google.protobuf.StringValue string_value = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          ptr = ctx->ParseMessage(_internal_mutable_string_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .google.protobuf.BytesValue bytes_value = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 74)) {
          ptr = ctx->ParseMessage(_internal_mutable_bytes_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .google.protobuf.BoolValue repeated_bool_value = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 90)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_repeated_bool_value(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<90>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .google.protobuf.Int32Value repeated_int32_value = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 98)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_repeated_int32_value(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<98>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .google.protobuf.Int64Value repeated_int64_value = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 106)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_repeated_int64_value(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<106>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .google.protobuf.UInt32Value repeated_uint32_value = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 114)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_repeated_uint32_value(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<114>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .google.protobuf.UInt64Value repeated_uint64_value = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 122)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_repeated_uint64_value(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<122>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .google.protobuf.FloatValue repeated_float_value = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 130)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(_internal_add_repeated_float_value(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<130>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .google.protobuf.DoubleValue repeated_double_value = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 138)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(_internal_add_repeated_double_value(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<138>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .google.protobuf.StringValue repeated_string_value = 18;
      case 18:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 146)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(_internal_add_repeated_string_value(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<146>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .google.protobuf.BytesValue repeated_bytes_value = 19;
      case 19:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 154)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(_internal_add_repeated_bytes_value(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<154>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestWrapper::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestWrapper)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .google.protobuf.BoolValue bool_value = 1;
  if (this->_internal_has_bool_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::bool_value(this), target, stream);
  }

  // .google.protobuf.Int32Value int32_value = 2;
  if (this->_internal_has_int32_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::int32_value(this), target, stream);
  }

  // .google.protobuf.Int64Value int64_value = 3;
  if (this->_internal_has_int64_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::int64_value(this), target, stream);
  }

  // .google.protobuf.UInt32Value uint32_value = 4;
  if (this->_internal_has_uint32_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::uint32_value(this), target, stream);
  }

  // .google.protobuf.UInt64Value uint64_value = 5;
  if (this->_internal_has_uint64_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::uint64_value(this), target, stream);
  }

  // .google.protobuf.FloatValue float_value = 6;
  if (this->_internal_has_float_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::float_value(this), target, stream);
  }

  // .google.protobuf.DoubleValue double_value = 7;
  if (this->_internal_has_double_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        7, _Internal::double_value(this), target, stream);
  }

  // .google.protobuf.StringValue string_value = 8;
  if (this->_internal_has_string_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        8, _Internal::string_value(this), target, stream);
  }

  // .google.protobuf.BytesValue bytes_value = 9;
  if (this->_internal_has_bytes_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        9, _Internal::bytes_value(this), target, stream);
  }

  // repeated .google.protobuf.BoolValue repeated_bool_value = 11;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_repeated_bool_value_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(11, this->_internal_repeated_bool_value(i), target, stream);
  }

  // repeated .google.protobuf.Int32Value repeated_int32_value = 12;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_repeated_int32_value_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(12, this->_internal_repeated_int32_value(i), target, stream);
  }

  // repeated .google.protobuf.Int64Value repeated_int64_value = 13;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_repeated_int64_value_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(13, this->_internal_repeated_int64_value(i), target, stream);
  }

  // repeated .google.protobuf.UInt32Value repeated_uint32_value = 14;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_repeated_uint32_value_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(14, this->_internal_repeated_uint32_value(i), target, stream);
  }

  // repeated .google.protobuf.UInt64Value repeated_uint64_value = 15;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_repeated_uint64_value_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(15, this->_internal_repeated_uint64_value(i), target, stream);
  }

  // repeated .google.protobuf.FloatValue repeated_float_value = 16;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_repeated_float_value_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(16, this->_internal_repeated_float_value(i), target, stream);
  }

  // repeated .google.protobuf.DoubleValue repeated_double_value = 17;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_repeated_double_value_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(17, this->_internal_repeated_double_value(i), target, stream);
  }

  // repeated .google.protobuf.StringValue repeated_string_value = 18;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_repeated_string_value_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(18, this->_internal_repeated_string_value(i), target, stream);
  }

  // repeated .google.protobuf.BytesValue repeated_bytes_value = 19;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_repeated_bytes_value_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(19, this->_internal_repeated_bytes_value(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestWrapper)
  return target;
}

size_t TestWrapper::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestWrapper)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .google.protobuf.BoolValue repeated_bool_value = 11;
  total_size += 1UL * this->_internal_repeated_bool_value_size();
  for (const auto& msg : this->repeated_bool_value_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .google.protobuf.Int32Value repeated_int32_value = 12;
  total_size += 1UL * this->_internal_repeated_int32_value_size();
  for (const auto& msg : this->repeated_int32_value_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .google.protobuf.Int64Value repeated_int64_value = 13;
  total_size += 1UL * this->_internal_repeated_int64_value_size();
  for (const auto& msg : this->repeated_int64_value_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .google.protobuf.UInt32Value repeated_uint32_value = 14;
  total_size += 1UL * this->_internal_repeated_uint32_value_size();
  for (const auto& msg : this->repeated_uint32_value_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .google.protobuf.UInt64Value repeated_uint64_value = 15;
  total_size += 1UL * this->_internal_repeated_uint64_value_size();
  for (const auto& msg : this->repeated_uint64_value_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .google.protobuf.FloatValue repeated_float_value = 16;
  total_size += 2UL * this->_internal_repeated_float_value_size();
  for (const auto& msg : this->repeated_float_value_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .google.protobuf.DoubleValue repeated_double_value = 17;
  total_size += 2UL * this->_internal_repeated_double_value_size();
  for (const auto& msg : this->repeated_double_value_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .google.protobuf.StringValue repeated_string_value = 18;
  total_size += 2UL * this->_internal_repeated_string_value_size();
  for (const auto& msg : this->repeated_string_value_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .google.protobuf.BytesValue repeated_bytes_value = 19;
  total_size += 2UL * this->_internal_repeated_bytes_value_size();
  for (const auto& msg : this->repeated_bytes_value_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .google.protobuf.BoolValue bool_value = 1;
  if (this->_internal_has_bool_value()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bool_value_);
  }

  // .google.protobuf.Int32Value int32_value = 2;
  if (this->_internal_has_int32_value()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *int32_value_);
  }

  // .google.protobuf.Int64Value int64_value = 3;
  if (this->_internal_has_int64_value()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *int64_value_);
  }

  // .google.protobuf.UInt32Value uint32_value = 4;
  if (this->_internal_has_uint32_value()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *uint32_value_);
  }

  // .google.protobuf.UInt64Value uint64_value = 5;
  if (this->_internal_has_uint64_value()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *uint64_value_);
  }

  // .google.protobuf.FloatValue float_value = 6;
  if (this->_internal_has_float_value()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *float_value_);
  }

  // .google.protobuf.DoubleValue double_value = 7;
  if (this->_internal_has_double_value()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *double_value_);
  }

  // .google.protobuf.StringValue string_value = 8;
  if (this->_internal_has_string_value()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *string_value_);
  }

  // .google.protobuf.BytesValue bytes_value = 9;
  if (this->_internal_has_bytes_value()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bytes_value_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestWrapper::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestWrapper::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestWrapper::GetClassData() const { return &_class_data_; }

void TestWrapper::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestWrapper *>(to)->MergeFrom(
      static_cast<const TestWrapper &>(from));
}


void TestWrapper::MergeFrom(const TestWrapper& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestWrapper)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  repeated_bool_value_.MergeFrom(from.repeated_bool_value_);
  repeated_int32_value_.MergeFrom(from.repeated_int32_value_);
  repeated_int64_value_.MergeFrom(from.repeated_int64_value_);
  repeated_uint32_value_.MergeFrom(from.repeated_uint32_value_);
  repeated_uint64_value_.MergeFrom(from.repeated_uint64_value_);
  repeated_float_value_.MergeFrom(from.repeated_float_value_);
  repeated_double_value_.MergeFrom(from.repeated_double_value_);
  repeated_string_value_.MergeFrom(from.repeated_string_value_);
  repeated_bytes_value_.MergeFrom(from.repeated_bytes_value_);
  if (from._internal_has_bool_value()) {
    _internal_mutable_bool_value()->::PROTOBUF_NAMESPACE_ID::BoolValue::MergeFrom(from._internal_bool_value());
  }
  if (from._internal_has_int32_value()) {
    _internal_mutable_int32_value()->::PROTOBUF_NAMESPACE_ID::Int32Value::MergeFrom(from._internal_int32_value());
  }
  if (from._internal_has_int64_value()) {
    _internal_mutable_int64_value()->::PROTOBUF_NAMESPACE_ID::Int64Value::MergeFrom(from._internal_int64_value());
  }
  if (from._internal_has_uint32_value()) {
    _internal_mutable_uint32_value()->::PROTOBUF_NAMESPACE_ID::UInt32Value::MergeFrom(from._internal_uint32_value());
  }
  if (from._internal_has_uint64_value()) {
    _internal_mutable_uint64_value()->::PROTOBUF_NAMESPACE_ID::UInt64Value::MergeFrom(from._internal_uint64_value());
  }
  if (from._internal_has_float_value()) {
    _internal_mutable_float_value()->::PROTOBUF_NAMESPACE_ID::FloatValue::MergeFrom(from._internal_float_value());
  }
  if (from._internal_has_double_value()) {
    _internal_mutable_double_value()->::PROTOBUF_NAMESPACE_ID::DoubleValue::MergeFrom(from._internal_double_value());
  }
  if (from._internal_has_string_value()) {
    _internal_mutable_string_value()->::PROTOBUF_NAMESPACE_ID::StringValue::MergeFrom(from._internal_string_value());
  }
  if (from._internal_has_bytes_value()) {
    _internal_mutable_bytes_value()->::PROTOBUF_NAMESPACE_ID::BytesValue::MergeFrom(from._internal_bytes_value());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestWrapper::CopyFrom(const TestWrapper& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestWrapper)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestWrapper::IsInitialized() const {
  return true;
}

void TestWrapper::InternalSwap(TestWrapper* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  repeated_bool_value_.InternalSwap(&other->repeated_bool_value_);
  repeated_int32_value_.InternalSwap(&other->repeated_int32_value_);
  repeated_int64_value_.InternalSwap(&other->repeated_int64_value_);
  repeated_uint32_value_.InternalSwap(&other->repeated_uint32_value_);
  repeated_uint64_value_.InternalSwap(&other->repeated_uint64_value_);
  repeated_float_value_.InternalSwap(&other->repeated_float_value_);
  repeated_double_value_.InternalSwap(&other->repeated_double_value_);
  repeated_string_value_.InternalSwap(&other->repeated_string_value_);
  repeated_bytes_value_.InternalSwap(&other->repeated_bytes_value_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TestWrapper, bytes_value_)
      + sizeof(TestWrapper::bytes_value_)
      - PROTOBUF_FIELD_OFFSET(TestWrapper, bool_value_)>(
          reinterpret_cast<char*>(&bool_value_),
          reinterpret_cast<char*>(&other->bool_value_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TestWrapper::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[20]);
}

// ===================================================================

class TestTimestamp::_Internal {
 public:
  static const ::PROTOBUF_NAMESPACE_ID::Timestamp& value(const TestTimestamp* msg);
};

const ::PROTOBUF_NAMESPACE_ID::Timestamp&
TestTimestamp::_Internal::value(const TestTimestamp* msg) {
  return *msg->value_;
}
void TestTimestamp::clear_value() {
  if (GetArenaForAllocation() == nullptr && value_ != nullptr) {
    delete value_;
  }
  value_ = nullptr;
}
void TestTimestamp::clear_repeated_value() {
  repeated_value_.Clear();
}
TestTimestamp::TestTimestamp(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  repeated_value_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:proto3.TestTimestamp)
}
TestTimestamp::TestTimestamp(const TestTimestamp& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      repeated_value_(from.repeated_value_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_value()) {
    value_ = new ::PROTOBUF_NAMESPACE_ID::Timestamp(*from.value_);
  } else {
    value_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:proto3.TestTimestamp)
}

inline void TestTimestamp::SharedCtor() {
value_ = nullptr;
}

TestTimestamp::~TestTimestamp() {
  // @@protoc_insertion_point(destructor:proto3.TestTimestamp)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestTimestamp::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete value_;
}

void TestTimestamp::ArenaDtor(void* object) {
  TestTimestamp* _this = reinterpret_cast< TestTimestamp* >(object);
  (void)_this;
}
void TestTimestamp::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TestTimestamp::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestTimestamp::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestTimestamp)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  repeated_value_.Clear();
  if (GetArenaForAllocation() == nullptr && value_ != nullptr) {
    delete value_;
  }
  value_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestTimestamp::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .google.protobuf.Timestamp value = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .google.protobuf.Timestamp repeated_value = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_repeated_value(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestTimestamp::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestTimestamp)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .google.protobuf.Timestamp value = 1;
  if (this->_internal_has_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::value(this), target, stream);
  }

  // repeated .google.protobuf.Timestamp repeated_value = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_repeated_value_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_repeated_value(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestTimestamp)
  return target;
}

size_t TestTimestamp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestTimestamp)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .google.protobuf.Timestamp repeated_value = 2;
  total_size += 1UL * this->_internal_repeated_value_size();
  for (const auto& msg : this->repeated_value_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .google.protobuf.Timestamp value = 1;
  if (this->_internal_has_value()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *value_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestTimestamp::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestTimestamp::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestTimestamp::GetClassData() const { return &_class_data_; }

void TestTimestamp::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestTimestamp *>(to)->MergeFrom(
      static_cast<const TestTimestamp &>(from));
}


void TestTimestamp::MergeFrom(const TestTimestamp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestTimestamp)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  repeated_value_.MergeFrom(from.repeated_value_);
  if (from._internal_has_value()) {
    _internal_mutable_value()->::PROTOBUF_NAMESPACE_ID::Timestamp::MergeFrom(from._internal_value());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestTimestamp::CopyFrom(const TestTimestamp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestTimestamp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestTimestamp::IsInitialized() const {
  return true;
}

void TestTimestamp::InternalSwap(TestTimestamp* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  repeated_value_.InternalSwap(&other->repeated_value_);
  swap(value_, other->value_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TestTimestamp::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[21]);
}

// ===================================================================

class TestDuration::_Internal {
 public:
  static const ::PROTOBUF_NAMESPACE_ID::Duration& value(const TestDuration* msg);
};

const ::PROTOBUF_NAMESPACE_ID::Duration&
TestDuration::_Internal::value(const TestDuration* msg) {
  return *msg->value_;
}
void TestDuration::clear_value() {
  if (GetArenaForAllocation() == nullptr && value_ != nullptr) {
    delete value_;
  }
  value_ = nullptr;
}
void TestDuration::clear_repeated_value() {
  repeated_value_.Clear();
}
TestDuration::TestDuration(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  repeated_value_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:proto3.TestDuration)
}
TestDuration::TestDuration(const TestDuration& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      repeated_value_(from.repeated_value_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_value()) {
    value_ = new ::PROTOBUF_NAMESPACE_ID::Duration(*from.value_);
  } else {
    value_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:proto3.TestDuration)
}

inline void TestDuration::SharedCtor() {
value_ = nullptr;
}

TestDuration::~TestDuration() {
  // @@protoc_insertion_point(destructor:proto3.TestDuration)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestDuration::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete value_;
}

void TestDuration::ArenaDtor(void* object) {
  TestDuration* _this = reinterpret_cast< TestDuration* >(object);
  (void)_this;
}
void TestDuration::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TestDuration::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestDuration::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestDuration)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  repeated_value_.Clear();
  if (GetArenaForAllocation() == nullptr && value_ != nullptr) {
    delete value_;
  }
  value_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestDuration::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .google.protobuf.Duration value = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .google.protobuf.Duration repeated_value = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_repeated_value(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestDuration::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestDuration)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .google.protobuf.Duration value = 1;
  if (this->_internal_has_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::value(this), target, stream);
  }

  // repeated .google.protobuf.Duration repeated_value = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_repeated_value_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_repeated_value(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestDuration)
  return target;
}

size_t TestDuration::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestDuration)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .google.protobuf.Duration repeated_value = 2;
  total_size += 1UL * this->_internal_repeated_value_size();
  for (const auto& msg : this->repeated_value_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .google.protobuf.Duration value = 1;
  if (this->_internal_has_value()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *value_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestDuration::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestDuration::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestDuration::GetClassData() const { return &_class_data_; }

void TestDuration::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestDuration *>(to)->MergeFrom(
      static_cast<const TestDuration &>(from));
}


void TestDuration::MergeFrom(const TestDuration& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestDuration)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  repeated_value_.MergeFrom(from.repeated_value_);
  if (from._internal_has_value()) {
    _internal_mutable_value()->::PROTOBUF_NAMESPACE_ID::Duration::MergeFrom(from._internal_value());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestDuration::CopyFrom(const TestDuration& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestDuration)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestDuration::IsInitialized() const {
  return true;
}

void TestDuration::InternalSwap(TestDuration* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  repeated_value_.InternalSwap(&other->repeated_value_);
  swap(value_, other->value_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TestDuration::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[22]);
}

// ===================================================================

class TestFieldMask::_Internal {
 public:
  static const ::PROTOBUF_NAMESPACE_ID::FieldMask& value(const TestFieldMask* msg);
};

const ::PROTOBUF_NAMESPACE_ID::FieldMask&
TestFieldMask::_Internal::value(const TestFieldMask* msg) {
  return *msg->value_;
}
void TestFieldMask::clear_value() {
  if (GetArenaForAllocation() == nullptr && value_ != nullptr) {
    delete value_;
  }
  value_ = nullptr;
}
TestFieldMask::TestFieldMask(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:proto3.TestFieldMask)
}
TestFieldMask::TestFieldMask(const TestFieldMask& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_value()) {
    value_ = new ::PROTOBUF_NAMESPACE_ID::FieldMask(*from.value_);
  } else {
    value_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:proto3.TestFieldMask)
}

inline void TestFieldMask::SharedCtor() {
value_ = nullptr;
}

TestFieldMask::~TestFieldMask() {
  // @@protoc_insertion_point(destructor:proto3.TestFieldMask)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestFieldMask::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete value_;
}

void TestFieldMask::ArenaDtor(void* object) {
  TestFieldMask* _this = reinterpret_cast< TestFieldMask* >(object);
  (void)_this;
}
void TestFieldMask::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TestFieldMask::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestFieldMask::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestFieldMask)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && value_ != nullptr) {
    delete value_;
  }
  value_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestFieldMask::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .google.protobuf.FieldMask value = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestFieldMask::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestFieldMask)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .google.protobuf.FieldMask value = 1;
  if (this->_internal_has_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::value(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestFieldMask)
  return target;
}

size_t TestFieldMask::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestFieldMask)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .google.protobuf.FieldMask value = 1;
  if (this->_internal_has_value()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *value_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestFieldMask::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestFieldMask::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestFieldMask::GetClassData() const { return &_class_data_; }

void TestFieldMask::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestFieldMask *>(to)->MergeFrom(
      static_cast<const TestFieldMask &>(from));
}


void TestFieldMask::MergeFrom(const TestFieldMask& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestFieldMask)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_value()) {
    _internal_mutable_value()->::PROTOBUF_NAMESPACE_ID::FieldMask::MergeFrom(from._internal_value());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestFieldMask::CopyFrom(const TestFieldMask& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestFieldMask)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestFieldMask::IsInitialized() const {
  return true;
}

void TestFieldMask::InternalSwap(TestFieldMask* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(value_, other->value_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TestFieldMask::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[23]);
}

// ===================================================================

class TestStruct::_Internal {
 public:
  static const ::PROTOBUF_NAMESPACE_ID::Struct& value(const TestStruct* msg);
};

const ::PROTOBUF_NAMESPACE_ID::Struct&
TestStruct::_Internal::value(const TestStruct* msg) {
  return *msg->value_;
}
void TestStruct::clear_value() {
  if (GetArenaForAllocation() == nullptr && value_ != nullptr) {
    delete value_;
  }
  value_ = nullptr;
}
void TestStruct::clear_repeated_value() {
  repeated_value_.Clear();
}
TestStruct::TestStruct(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  repeated_value_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:proto3.TestStruct)
}
TestStruct::TestStruct(const TestStruct& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      repeated_value_(from.repeated_value_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_value()) {
    value_ = new ::PROTOBUF_NAMESPACE_ID::Struct(*from.value_);
  } else {
    value_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:proto3.TestStruct)
}

inline void TestStruct::SharedCtor() {
value_ = nullptr;
}

TestStruct::~TestStruct() {
  // @@protoc_insertion_point(destructor:proto3.TestStruct)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestStruct::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete value_;
}

void TestStruct::ArenaDtor(void* object) {
  TestStruct* _this = reinterpret_cast< TestStruct* >(object);
  (void)_this;
}
void TestStruct::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TestStruct::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestStruct::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestStruct)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  repeated_value_.Clear();
  if (GetArenaForAllocation() == nullptr && value_ != nullptr) {
    delete value_;
  }
  value_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestStruct::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .google.protobuf.Struct value = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .google.protobuf.Struct repeated_value = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_repeated_value(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestStruct::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestStruct)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .google.protobuf.Struct value = 1;
  if (this->_internal_has_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::value(this), target, stream);
  }

  // repeated .google.protobuf.Struct repeated_value = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_repeated_value_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_repeated_value(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestStruct)
  return target;
}

size_t TestStruct::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestStruct)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .google.protobuf.Struct repeated_value = 2;
  total_size += 1UL * this->_internal_repeated_value_size();
  for (const auto& msg : this->repeated_value_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .google.protobuf.Struct value = 1;
  if (this->_internal_has_value()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *value_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestStruct::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestStruct::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestStruct::GetClassData() const { return &_class_data_; }

void TestStruct::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestStruct *>(to)->MergeFrom(
      static_cast<const TestStruct &>(from));
}


void TestStruct::MergeFrom(const TestStruct& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestStruct)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  repeated_value_.MergeFrom(from.repeated_value_);
  if (from._internal_has_value()) {
    _internal_mutable_value()->::PROTOBUF_NAMESPACE_ID::Struct::MergeFrom(from._internal_value());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestStruct::CopyFrom(const TestStruct& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestStruct)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestStruct::IsInitialized() const {
  return true;
}

void TestStruct::InternalSwap(TestStruct* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  repeated_value_.InternalSwap(&other->repeated_value_);
  swap(value_, other->value_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TestStruct::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[24]);
}

// ===================================================================

class TestAny::_Internal {
 public:
  static const ::PROTOBUF_NAMESPACE_ID::Any& value(const TestAny* msg);
};

const ::PROTOBUF_NAMESPACE_ID::Any&
TestAny::_Internal::value(const TestAny* msg) {
  return *msg->value_;
}
void TestAny::clear_value() {
  if (GetArenaForAllocation() == nullptr && value_ != nullptr) {
    delete value_;
  }
  value_ = nullptr;
}
void TestAny::clear_repeated_value() {
  repeated_value_.Clear();
}
TestAny::TestAny(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  repeated_value_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:proto3.TestAny)
}
TestAny::TestAny(const TestAny& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      repeated_value_(from.repeated_value_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_value()) {
    value_ = new ::PROTOBUF_NAMESPACE_ID::Any(*from.value_);
  } else {
    value_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:proto3.TestAny)
}

inline void TestAny::SharedCtor() {
value_ = nullptr;
}

TestAny::~TestAny() {
  // @@protoc_insertion_point(destructor:proto3.TestAny)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestAny::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete value_;
}

void TestAny::ArenaDtor(void* object) {
  TestAny* _this = reinterpret_cast< TestAny* >(object);
  (void)_this;
}
void TestAny::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TestAny::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestAny::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestAny)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  repeated_value_.Clear();
  if (GetArenaForAllocation() == nullptr && value_ != nullptr) {
    delete value_;
  }
  value_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestAny::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .google.protobuf.Any value = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .google.protobuf.Any repeated_value = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_repeated_value(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestAny::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestAny)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .google.protobuf.Any value = 1;
  if (this->_internal_has_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::value(this), target, stream);
  }

  // repeated .google.protobuf.Any repeated_value = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_repeated_value_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_repeated_value(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestAny)
  return target;
}

size_t TestAny::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestAny)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .google.protobuf.Any repeated_value = 2;
  total_size += 1UL * this->_internal_repeated_value_size();
  for (const auto& msg : this->repeated_value_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .google.protobuf.Any value = 1;
  if (this->_internal_has_value()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *value_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestAny::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestAny::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestAny::GetClassData() const { return &_class_data_; }

void TestAny::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestAny *>(to)->MergeFrom(
      static_cast<const TestAny &>(from));
}


void TestAny::MergeFrom(const TestAny& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestAny)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  repeated_value_.MergeFrom(from.repeated_value_);
  if (from._internal_has_value()) {
    _internal_mutable_value()->::PROTOBUF_NAMESPACE_ID::Any::MergeFrom(from._internal_value());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestAny::CopyFrom(const TestAny& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestAny)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestAny::IsInitialized() const {
  return true;
}

void TestAny::InternalSwap(TestAny* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  repeated_value_.InternalSwap(&other->repeated_value_);
  swap(value_, other->value_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TestAny::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[25]);
}

// ===================================================================

class TestValue::_Internal {
 public:
  static const ::PROTOBUF_NAMESPACE_ID::Value& value(const TestValue* msg);
};

const ::PROTOBUF_NAMESPACE_ID::Value&
TestValue::_Internal::value(const TestValue* msg) {
  return *msg->value_;
}
void TestValue::clear_value() {
  if (GetArenaForAllocation() == nullptr && value_ != nullptr) {
    delete value_;
  }
  value_ = nullptr;
}
void TestValue::clear_repeated_value() {
  repeated_value_.Clear();
}
TestValue::TestValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  repeated_value_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:proto3.TestValue)
}
TestValue::TestValue(const TestValue& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      repeated_value_(from.repeated_value_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_value()) {
    value_ = new ::PROTOBUF_NAMESPACE_ID::Value(*from.value_);
  } else {
    value_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:proto3.TestValue)
}

inline void TestValue::SharedCtor() {
value_ = nullptr;
}

TestValue::~TestValue() {
  // @@protoc_insertion_point(destructor:proto3.TestValue)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestValue::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete value_;
}

void TestValue::ArenaDtor(void* object) {
  TestValue* _this = reinterpret_cast< TestValue* >(object);
  (void)_this;
}
void TestValue::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TestValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestValue::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestValue)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  repeated_value_.Clear();
  if (GetArenaForAllocation() == nullptr && value_ != nullptr) {
    delete value_;
  }
  value_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestValue::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .google.protobuf.Value value = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .google.protobuf.Value repeated_value = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_repeated_value(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestValue::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestValue)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .google.protobuf.Value value = 1;
  if (this->_internal_has_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::value(this), target, stream);
  }

  // repeated .google.protobuf.Value repeated_value = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_repeated_value_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_repeated_value(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestValue)
  return target;
}

size_t TestValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestValue)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .google.protobuf.Value repeated_value = 2;
  total_size += 1UL * this->_internal_repeated_value_size();
  for (const auto& msg : this->repeated_value_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .google.protobuf.Value value = 1;
  if (this->_internal_has_value()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *value_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestValue::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestValue::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestValue::GetClassData() const { return &_class_data_; }

void TestValue::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestValue *>(to)->MergeFrom(
      static_cast<const TestValue &>(from));
}


void TestValue::MergeFrom(const TestValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestValue)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  repeated_value_.MergeFrom(from.repeated_value_);
  if (from._internal_has_value()) {
    _internal_mutable_value()->::PROTOBUF_NAMESPACE_ID::Value::MergeFrom(from._internal_value());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestValue::CopyFrom(const TestValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestValue::IsInitialized() const {
  return true;
}

void TestValue::InternalSwap(TestValue* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  repeated_value_.InternalSwap(&other->repeated_value_);
  swap(value_, other->value_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TestValue::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[26]);
}

// ===================================================================

class TestListValue::_Internal {
 public:
  static const ::PROTOBUF_NAMESPACE_ID::ListValue& value(const TestListValue* msg);
};

const ::PROTOBUF_NAMESPACE_ID::ListValue&
TestListValue::_Internal::value(const TestListValue* msg) {
  return *msg->value_;
}
void TestListValue::clear_value() {
  if (GetArenaForAllocation() == nullptr && value_ != nullptr) {
    delete value_;
  }
  value_ = nullptr;
}
void TestListValue::clear_repeated_value() {
  repeated_value_.Clear();
}
TestListValue::TestListValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  repeated_value_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:proto3.TestListValue)
}
TestListValue::TestListValue(const TestListValue& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      repeated_value_(from.repeated_value_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_value()) {
    value_ = new ::PROTOBUF_NAMESPACE_ID::ListValue(*from.value_);
  } else {
    value_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:proto3.TestListValue)
}

inline void TestListValue::SharedCtor() {
value_ = nullptr;
}

TestListValue::~TestListValue() {
  // @@protoc_insertion_point(destructor:proto3.TestListValue)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestListValue::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete value_;
}

void TestListValue::ArenaDtor(void* object) {
  TestListValue* _this = reinterpret_cast< TestListValue* >(object);
  (void)_this;
}
void TestListValue::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TestListValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestListValue::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestListValue)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  repeated_value_.Clear();
  if (GetArenaForAllocation() == nullptr && value_ != nullptr) {
    delete value_;
  }
  value_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestListValue::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .google.protobuf.ListValue value = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .google.protobuf.ListValue repeated_value = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_repeated_value(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestListValue::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestListValue)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .google.protobuf.ListValue value = 1;
  if (this->_internal_has_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::value(this), target, stream);
  }

  // repeated .google.protobuf.ListValue repeated_value = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_repeated_value_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_repeated_value(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestListValue)
  return target;
}

size_t TestListValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestListValue)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .google.protobuf.ListValue repeated_value = 2;
  total_size += 1UL * this->_internal_repeated_value_size();
  for (const auto& msg : this->repeated_value_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .google.protobuf.ListValue value = 1;
  if (this->_internal_has_value()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *value_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestListValue::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestListValue::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestListValue::GetClassData() const { return &_class_data_; }

void TestListValue::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestListValue *>(to)->MergeFrom(
      static_cast<const TestListValue &>(from));
}


void TestListValue::MergeFrom(const TestListValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestListValue)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  repeated_value_.MergeFrom(from.repeated_value_);
  if (from._internal_has_value()) {
    _internal_mutable_value()->::PROTOBUF_NAMESPACE_ID::ListValue::MergeFrom(from._internal_value());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestListValue::CopyFrom(const TestListValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestListValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestListValue::IsInitialized() const {
  return true;
}

void TestListValue::InternalSwap(TestListValue* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  repeated_value_.InternalSwap(&other->repeated_value_);
  swap(value_, other->value_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TestListValue::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[27]);
}

// ===================================================================

TestBoolValue_BoolMapEntry_DoNotUse::TestBoolValue_BoolMapEntry_DoNotUse() {}
TestBoolValue_BoolMapEntry_DoNotUse::TestBoolValue_BoolMapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void TestBoolValue_BoolMapEntry_DoNotUse::MergeFrom(const TestBoolValue_BoolMapEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata TestBoolValue_BoolMapEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[28]);
}

// ===================================================================

class TestBoolValue::_Internal {
 public:
};

TestBoolValue::TestBoolValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  bool_map_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:proto3.TestBoolValue)
}
TestBoolValue::TestBoolValue(const TestBoolValue& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  bool_map_.MergeFrom(from.bool_map_);
  bool_value_ = from.bool_value_;
  // @@protoc_insertion_point(copy_constructor:proto3.TestBoolValue)
}

inline void TestBoolValue::SharedCtor() {
bool_value_ = false;
}

TestBoolValue::~TestBoolValue() {
  // @@protoc_insertion_point(destructor:proto3.TestBoolValue)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestBoolValue::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void TestBoolValue::ArenaDtor(void* object) {
  TestBoolValue* _this = reinterpret_cast< TestBoolValue* >(object);
  (void)_this;
  _this->bool_map_. ~MapField();
}
inline void TestBoolValue::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &TestBoolValue::ArenaDtor);
  }
}
void TestBoolValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestBoolValue::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestBoolValue)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  bool_map_.Clear();
  bool_value_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestBoolValue::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool bool_value = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          bool_value_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // map<bool, int32> bool_map = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&bool_map_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestBoolValue::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestBoolValue)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool bool_value = 1;
  if (this->_internal_bool_value() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_bool_value(), target);
  }

  // map<bool, int32> bool_map = 2;
  if (!this->_internal_bool_map().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >::const_pointer
        ConstPtr;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::SortItem< bool, ConstPtr > SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByFirstField<SortItem> Less;

    if (stream->IsSerializationDeterministic() &&
        this->_internal_bool_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_bool_map().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >::const_iterator
          it = this->_internal_bool_map().begin();
          it != this->_internal_bool_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = TestBoolValue_BoolMapEntry_DoNotUse::Funcs::InternalSerialize(2, items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second, target, stream);
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >::const_iterator
          it = this->_internal_bool_map().begin();
          it != this->_internal_bool_map().end(); ++it) {
        target = TestBoolValue_BoolMapEntry_DoNotUse::Funcs::InternalSerialize(2, it->first, it->second, target, stream);
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestBoolValue)
  return target;
}

size_t TestBoolValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestBoolValue)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<bool, int32> bool_map = 2;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_bool_map_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >::const_iterator
      it = this->_internal_bool_map().begin();
      it != this->_internal_bool_map().end(); ++it) {
    total_size += TestBoolValue_BoolMapEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // bool bool_value = 1;
  if (this->_internal_bool_value() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestBoolValue::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestBoolValue::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestBoolValue::GetClassData() const { return &_class_data_; }

void TestBoolValue::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestBoolValue *>(to)->MergeFrom(
      static_cast<const TestBoolValue &>(from));
}


void TestBoolValue::MergeFrom(const TestBoolValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestBoolValue)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  bool_map_.MergeFrom(from.bool_map_);
  if (from._internal_bool_value() != 0) {
    _internal_set_bool_value(from._internal_bool_value());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestBoolValue::CopyFrom(const TestBoolValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestBoolValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestBoolValue::IsInitialized() const {
  return true;
}

void TestBoolValue::InternalSwap(TestBoolValue* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  bool_map_.InternalSwap(&other->bool_map_);
  swap(bool_value_, other->bool_value_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TestBoolValue::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[29]);
}

// ===================================================================

class TestCustomJsonName::_Internal {
 public:
};

TestCustomJsonName::TestCustomJsonName(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:proto3.TestCustomJsonName)
}
TestCustomJsonName::TestCustomJsonName(const TestCustomJsonName& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  value_ = from.value_;
  // @@protoc_insertion_point(copy_constructor:proto3.TestCustomJsonName)
}

inline void TestCustomJsonName::SharedCtor() {
value_ = 0;
}

TestCustomJsonName::~TestCustomJsonName() {
  // @@protoc_insertion_point(destructor:proto3.TestCustomJsonName)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestCustomJsonName::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void TestCustomJsonName::ArenaDtor(void* object) {
  TestCustomJsonName* _this = reinterpret_cast< TestCustomJsonName* >(object);
  (void)_this;
}
void TestCustomJsonName::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TestCustomJsonName::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestCustomJsonName::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestCustomJsonName)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  value_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestCustomJsonName::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 value = 1 [json_name = "@value"];
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          value_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestCustomJsonName::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestCustomJsonName)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 value = 1 [json_name = "@value"];
  if (this->_internal_value() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_value(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestCustomJsonName)
  return target;
}

size_t TestCustomJsonName::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestCustomJsonName)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 value = 1 [json_name = "@value"];
  if (this->_internal_value() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_value());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestCustomJsonName::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestCustomJsonName::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestCustomJsonName::GetClassData() const { return &_class_data_; }

void TestCustomJsonName::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestCustomJsonName *>(to)->MergeFrom(
      static_cast<const TestCustomJsonName &>(from));
}


void TestCustomJsonName::MergeFrom(const TestCustomJsonName& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestCustomJsonName)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_value() != 0) {
    _internal_set_value(from._internal_value());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestCustomJsonName::CopyFrom(const TestCustomJsonName& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestCustomJsonName)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestCustomJsonName::IsInitialized() const {
  return true;
}

void TestCustomJsonName::InternalSwap(TestCustomJsonName* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(value_, other->value_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TestCustomJsonName::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[30]);
}

// ===================================================================

class TestExtensions::_Internal {
 public:
  static const ::protobuf_unittest::TestAllExtensions& extensions(const TestExtensions* msg);
};

const ::protobuf_unittest::TestAllExtensions&
TestExtensions::_Internal::extensions(const TestExtensions* msg) {
  return *msg->extensions_;
}
void TestExtensions::clear_extensions() {
  if (GetArenaForAllocation() == nullptr && extensions_ != nullptr) {
    delete extensions_;
  }
  extensions_ = nullptr;
}
TestExtensions::TestExtensions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:proto3.TestExtensions)
}
TestExtensions::TestExtensions(const TestExtensions& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_extensions()) {
    extensions_ = new ::protobuf_unittest::TestAllExtensions(*from.extensions_);
  } else {
    extensions_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:proto3.TestExtensions)
}

inline void TestExtensions::SharedCtor() {
extensions_ = nullptr;
}

TestExtensions::~TestExtensions() {
  // @@protoc_insertion_point(destructor:proto3.TestExtensions)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestExtensions::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete extensions_;
}

void TestExtensions::ArenaDtor(void* object) {
  TestExtensions* _this = reinterpret_cast< TestExtensions* >(object);
  (void)_this;
}
void TestExtensions::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TestExtensions::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestExtensions::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestExtensions)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && extensions_ != nullptr) {
    delete extensions_;
  }
  extensions_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestExtensions::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .protobuf_unittest.TestAllExtensions extensions = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_extensions(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestExtensions::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestExtensions)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .protobuf_unittest.TestAllExtensions extensions = 1;
  if (this->_internal_has_extensions()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::extensions(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestExtensions)
  return target;
}

size_t TestExtensions::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestExtensions)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .protobuf_unittest.TestAllExtensions extensions = 1;
  if (this->_internal_has_extensions()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *extensions_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestExtensions::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestExtensions::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestExtensions::GetClassData() const { return &_class_data_; }

void TestExtensions::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestExtensions *>(to)->MergeFrom(
      static_cast<const TestExtensions &>(from));
}


void TestExtensions::MergeFrom(const TestExtensions& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestExtensions)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_extensions()) {
    _internal_mutable_extensions()->::protobuf_unittest::TestAllExtensions::MergeFrom(from._internal_extensions());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestExtensions::CopyFrom(const TestExtensions& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestExtensions)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestExtensions::IsInitialized() const {
  if (_internal_has_extensions()) {
    if (!extensions_->IsInitialized()) return false;
  }
  return true;
}

void TestExtensions::InternalSwap(TestExtensions* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(extensions_, other->extensions_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TestExtensions::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[31]);
}

// ===================================================================

class TestEnumValue::_Internal {
 public:
};

TestEnumValue::TestEnumValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:proto3.TestEnumValue)
}
TestEnumValue::TestEnumValue(const TestEnumValue& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&enum_value1_, &from.enum_value1_,
    static_cast<size_t>(reinterpret_cast<char*>(&enum_value3_) -
    reinterpret_cast<char*>(&enum_value1_)) + sizeof(enum_value3_));
  // @@protoc_insertion_point(copy_constructor:proto3.TestEnumValue)
}

inline void TestEnumValue::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&enum_value1_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&enum_value3_) -
    reinterpret_cast<char*>(&enum_value1_)) + sizeof(enum_value3_));
}

TestEnumValue::~TestEnumValue() {
  // @@protoc_insertion_point(destructor:proto3.TestEnumValue)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestEnumValue::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void TestEnumValue::ArenaDtor(void* object) {
  TestEnumValue* _this = reinterpret_cast< TestEnumValue* >(object);
  (void)_this;
}
void TestEnumValue::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TestEnumValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestEnumValue::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestEnumValue)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&enum_value1_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&enum_value3_) -
      reinterpret_cast<char*>(&enum_value1_)) + sizeof(enum_value3_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestEnumValue::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .proto3.EnumType enum_value1 = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_enum_value1(static_cast<::proto3::EnumType>(val));
        } else
          goto handle_unusual;
        continue;
      // .proto3.EnumType enum_value2 = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_enum_value2(static_cast<::proto3::EnumType>(val));
        } else
          goto handle_unusual;
        continue;
      // .proto3.EnumType enum_value3 = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_enum_value3(static_cast<::proto3::EnumType>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestEnumValue::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestEnumValue)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .proto3.EnumType enum_value1 = 1;
  if (this->_internal_enum_value1() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_enum_value1(), target);
  }

  // .proto3.EnumType enum_value2 = 2;
  if (this->_internal_enum_value2() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_enum_value2(), target);
  }

  // .proto3.EnumType enum_value3 = 3;
  if (this->_internal_enum_value3() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->_internal_enum_value3(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestEnumValue)
  return target;
}

size_t TestEnumValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestEnumValue)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .proto3.EnumType enum_value1 = 1;
  if (this->_internal_enum_value1() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_enum_value1());
  }

  // .proto3.EnumType enum_value2 = 2;
  if (this->_internal_enum_value2() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_enum_value2());
  }

  // .proto3.EnumType enum_value3 = 3;
  if (this->_internal_enum_value3() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_enum_value3());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestEnumValue::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestEnumValue::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestEnumValue::GetClassData() const { return &_class_data_; }

void TestEnumValue::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestEnumValue *>(to)->MergeFrom(
      static_cast<const TestEnumValue &>(from));
}


void TestEnumValue::MergeFrom(const TestEnumValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestEnumValue)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_enum_value1() != 0) {
    _internal_set_enum_value1(from._internal_enum_value1());
  }
  if (from._internal_enum_value2() != 0) {
    _internal_set_enum_value2(from._internal_enum_value2());
  }
  if (from._internal_enum_value3() != 0) {
    _internal_set_enum_value3(from._internal_enum_value3());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestEnumValue::CopyFrom(const TestEnumValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestEnumValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestEnumValue::IsInitialized() const {
  return true;
}

void TestEnumValue::InternalSwap(TestEnumValue* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TestEnumValue, enum_value3_)
      + sizeof(TestEnumValue::enum_value3_)
      - PROTOBUF_FIELD_OFFSET(TestEnumValue, enum_value1_)>(
          reinterpret_cast<char*>(&enum_value1_),
          reinterpret_cast<char*>(&other->enum_value1_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TestEnumValue::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto[32]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace proto3
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::proto3::MessageType* Arena::CreateMaybeMessage< ::proto3::MessageType >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::MessageType >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestMessage* Arena::CreateMaybeMessage< ::proto3::TestMessage >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestMessage >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestOneof* Arena::CreateMaybeMessage< ::proto3::TestOneof >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestOneof >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestMap_BoolMapEntry_DoNotUse* Arena::CreateMaybeMessage< ::proto3::TestMap_BoolMapEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestMap_BoolMapEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestMap_Int32MapEntry_DoNotUse* Arena::CreateMaybeMessage< ::proto3::TestMap_Int32MapEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestMap_Int32MapEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestMap_Int64MapEntry_DoNotUse* Arena::CreateMaybeMessage< ::proto3::TestMap_Int64MapEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestMap_Int64MapEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestMap_Uint32MapEntry_DoNotUse* Arena::CreateMaybeMessage< ::proto3::TestMap_Uint32MapEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestMap_Uint32MapEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestMap_Uint64MapEntry_DoNotUse* Arena::CreateMaybeMessage< ::proto3::TestMap_Uint64MapEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestMap_Uint64MapEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestMap_StringMapEntry_DoNotUse* Arena::CreateMaybeMessage< ::proto3::TestMap_StringMapEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestMap_StringMapEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestMap* Arena::CreateMaybeMessage< ::proto3::TestMap >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestMap >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestNestedMap_BoolMapEntry_DoNotUse* Arena::CreateMaybeMessage< ::proto3::TestNestedMap_BoolMapEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestNestedMap_BoolMapEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestNestedMap_Int32MapEntry_DoNotUse* Arena::CreateMaybeMessage< ::proto3::TestNestedMap_Int32MapEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestNestedMap_Int32MapEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestNestedMap_Int64MapEntry_DoNotUse* Arena::CreateMaybeMessage< ::proto3::TestNestedMap_Int64MapEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestNestedMap_Int64MapEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestNestedMap_Uint32MapEntry_DoNotUse* Arena::CreateMaybeMessage< ::proto3::TestNestedMap_Uint32MapEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestNestedMap_Uint32MapEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestNestedMap_Uint64MapEntry_DoNotUse* Arena::CreateMaybeMessage< ::proto3::TestNestedMap_Uint64MapEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestNestedMap_Uint64MapEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestNestedMap_StringMapEntry_DoNotUse* Arena::CreateMaybeMessage< ::proto3::TestNestedMap_StringMapEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestNestedMap_StringMapEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestNestedMap_MapMapEntry_DoNotUse* Arena::CreateMaybeMessage< ::proto3::TestNestedMap_MapMapEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestNestedMap_MapMapEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestNestedMap* Arena::CreateMaybeMessage< ::proto3::TestNestedMap >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestNestedMap >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestStringMap_StringMapEntry_DoNotUse* Arena::CreateMaybeMessage< ::proto3::TestStringMap_StringMapEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestStringMap_StringMapEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestStringMap* Arena::CreateMaybeMessage< ::proto3::TestStringMap >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestStringMap >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestWrapper* Arena::CreateMaybeMessage< ::proto3::TestWrapper >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestWrapper >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestTimestamp* Arena::CreateMaybeMessage< ::proto3::TestTimestamp >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestTimestamp >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestDuration* Arena::CreateMaybeMessage< ::proto3::TestDuration >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestDuration >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestFieldMask* Arena::CreateMaybeMessage< ::proto3::TestFieldMask >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestFieldMask >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestStruct* Arena::CreateMaybeMessage< ::proto3::TestStruct >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestStruct >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestAny* Arena::CreateMaybeMessage< ::proto3::TestAny >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestAny >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestValue* Arena::CreateMaybeMessage< ::proto3::TestValue >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestValue >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestListValue* Arena::CreateMaybeMessage< ::proto3::TestListValue >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestListValue >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestBoolValue_BoolMapEntry_DoNotUse* Arena::CreateMaybeMessage< ::proto3::TestBoolValue_BoolMapEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestBoolValue_BoolMapEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestBoolValue* Arena::CreateMaybeMessage< ::proto3::TestBoolValue >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestBoolValue >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestCustomJsonName* Arena::CreateMaybeMessage< ::proto3::TestCustomJsonName >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestCustomJsonName >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestExtensions* Arena::CreateMaybeMessage< ::proto3::TestExtensions >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestExtensions >(arena);
}
template<> PROTOBUF_NOINLINE ::proto3::TestEnumValue* Arena::CreateMaybeMessage< ::proto3::TestEnumValue >(Arena* arena) {
  return Arena::CreateMessageInternal< ::proto3::TestEnumValue >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
