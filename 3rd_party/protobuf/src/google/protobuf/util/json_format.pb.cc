// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/util/json_format.proto

#include "google/protobuf/util/json_format.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace protobuf_unittest {
constexpr TestFlagsAndStrings_RepeatedGroup::TestFlagsAndStrings_RepeatedGroup(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : f_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct TestFlagsAndStrings_RepeatedGroupDefaultTypeInternal {
  constexpr TestFlagsAndStrings_RepeatedGroupDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestFlagsAndStrings_RepeatedGroupDefaultTypeInternal() {}
  union {
    TestFlagsAndStrings_RepeatedGroup _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestFlagsAndStrings_RepeatedGroupDefaultTypeInternal _TestFlagsAndStrings_RepeatedGroup_default_instance_;
constexpr TestFlagsAndStrings::TestFlagsAndStrings(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : repeatedgroup_()
  , a_(0){}
struct TestFlagsAndStringsDefaultTypeInternal {
  constexpr TestFlagsAndStringsDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestFlagsAndStringsDefaultTypeInternal() {}
  union {
    TestFlagsAndStrings _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestFlagsAndStringsDefaultTypeInternal _TestFlagsAndStrings_default_instance_;
constexpr TestBase64ByteArrays::TestBase64ByteArrays(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : a_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct TestBase64ByteArraysDefaultTypeInternal {
  constexpr TestBase64ByteArraysDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestBase64ByteArraysDefaultTypeInternal() {}
  union {
    TestBase64ByteArrays _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestBase64ByteArraysDefaultTypeInternal _TestBase64ByteArrays_default_instance_;
constexpr TestJavaScriptJSON::TestJavaScriptJSON(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : in_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , var_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , a_(0)
  , final_(0){}
struct TestJavaScriptJSONDefaultTypeInternal {
  constexpr TestJavaScriptJSONDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestJavaScriptJSONDefaultTypeInternal() {}
  union {
    TestJavaScriptJSON _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestJavaScriptJSONDefaultTypeInternal _TestJavaScriptJSON_default_instance_;
constexpr TestJavaScriptOrderJSON1::TestJavaScriptOrderJSON1(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : d_(0)
  , c_(0)
  , x_(false)
  , b_(0)
  , a_(0){}
struct TestJavaScriptOrderJSON1DefaultTypeInternal {
  constexpr TestJavaScriptOrderJSON1DefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestJavaScriptOrderJSON1DefaultTypeInternal() {}
  union {
    TestJavaScriptOrderJSON1 _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestJavaScriptOrderJSON1DefaultTypeInternal _TestJavaScriptOrderJSON1_default_instance_;
constexpr TestJavaScriptOrderJSON2::TestJavaScriptOrderJSON2(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : z_()
  , d_(0)
  , c_(0)
  , x_(false)
  , b_(0)
  , a_(0){}
struct TestJavaScriptOrderJSON2DefaultTypeInternal {
  constexpr TestJavaScriptOrderJSON2DefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestJavaScriptOrderJSON2DefaultTypeInternal() {}
  union {
    TestJavaScriptOrderJSON2 _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestJavaScriptOrderJSON2DefaultTypeInternal _TestJavaScriptOrderJSON2_default_instance_;
constexpr TestLargeInt::TestLargeInt(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : a_(int64_t{0})
  , b_(uint64_t{0u}){}
struct TestLargeIntDefaultTypeInternal {
  constexpr TestLargeIntDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestLargeIntDefaultTypeInternal() {}
  union {
    TestLargeInt _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestLargeIntDefaultTypeInternal _TestLargeInt_default_instance_;
constexpr TestNumbers::TestNumbers(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : a_(0)

  , b_(0)
  , c_(0)
  , d_(false)
  , e_(0)
  , f_(0u){}
struct TestNumbersDefaultTypeInternal {
  constexpr TestNumbersDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestNumbersDefaultTypeInternal() {}
  union {
    TestNumbers _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestNumbersDefaultTypeInternal _TestNumbers_default_instance_;
constexpr TestCamelCase::TestCamelCase(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : normal_field_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , capital_field_(0)
  , camelcasefield_(0){}
struct TestCamelCaseDefaultTypeInternal {
  constexpr TestCamelCaseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestCamelCaseDefaultTypeInternal() {}
  union {
    TestCamelCase _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestCamelCaseDefaultTypeInternal _TestCamelCase_default_instance_;
constexpr TestBoolMap_BoolMapEntry_DoNotUse::TestBoolMap_BoolMapEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct TestBoolMap_BoolMapEntry_DoNotUseDefaultTypeInternal {
  constexpr TestBoolMap_BoolMapEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestBoolMap_BoolMapEntry_DoNotUseDefaultTypeInternal() {}
  union {
    TestBoolMap_BoolMapEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestBoolMap_BoolMapEntry_DoNotUseDefaultTypeInternal _TestBoolMap_BoolMapEntry_DoNotUse_default_instance_;
constexpr TestBoolMap::TestBoolMap(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : bool_map_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}){}
struct TestBoolMapDefaultTypeInternal {
  constexpr TestBoolMapDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestBoolMapDefaultTypeInternal() {}
  union {
    TestBoolMap _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestBoolMapDefaultTypeInternal _TestBoolMap_default_instance_;
constexpr TestRecursion::TestRecursion(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : child_(nullptr)
  , value_(0){}
struct TestRecursionDefaultTypeInternal {
  constexpr TestRecursionDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestRecursionDefaultTypeInternal() {}
  union {
    TestRecursion _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestRecursionDefaultTypeInternal _TestRecursion_default_instance_;
constexpr TestStringMap_StringMapEntry_DoNotUse::TestStringMap_StringMapEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct TestStringMap_StringMapEntry_DoNotUseDefaultTypeInternal {
  constexpr TestStringMap_StringMapEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestStringMap_StringMapEntry_DoNotUseDefaultTypeInternal() {}
  union {
    TestStringMap_StringMapEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestStringMap_StringMapEntry_DoNotUseDefaultTypeInternal _TestStringMap_StringMapEntry_DoNotUse_default_instance_;
constexpr TestStringMap::TestStringMap(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : string_map_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}){}
struct TestStringMapDefaultTypeInternal {
  constexpr TestStringMapDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestStringMapDefaultTypeInternal() {}
  union {
    TestStringMap _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestStringMapDefaultTypeInternal _TestStringMap_default_instance_;
constexpr TestStringSerializer_StringMapEntry_DoNotUse::TestStringSerializer_StringMapEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct TestStringSerializer_StringMapEntry_DoNotUseDefaultTypeInternal {
  constexpr TestStringSerializer_StringMapEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestStringSerializer_StringMapEntry_DoNotUseDefaultTypeInternal() {}
  union {
    TestStringSerializer_StringMapEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestStringSerializer_StringMapEntry_DoNotUseDefaultTypeInternal _TestStringSerializer_StringMapEntry_DoNotUse_default_instance_;
constexpr TestStringSerializer::TestStringSerializer(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : repeated_string_()
  , string_map_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , scalar_string_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct TestStringSerializerDefaultTypeInternal {
  constexpr TestStringSerializerDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestStringSerializerDefaultTypeInternal() {}
  union {
    TestStringSerializer _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestStringSerializerDefaultTypeInternal _TestStringSerializer_default_instance_;
constexpr TestMessageWithExtension::TestMessageWithExtension(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct TestMessageWithExtensionDefaultTypeInternal {
  constexpr TestMessageWithExtensionDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestMessageWithExtensionDefaultTypeInternal() {}
  union {
    TestMessageWithExtension _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestMessageWithExtensionDefaultTypeInternal _TestMessageWithExtension_default_instance_;
constexpr TestExtension::TestExtension(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : value_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct TestExtensionDefaultTypeInternal {
  constexpr TestExtensionDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestExtensionDefaultTypeInternal() {}
  union {
    TestExtension _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestExtensionDefaultTypeInternal _TestExtension_default_instance_;
constexpr TestDefaultEnumValue::TestDefaultEnumValue(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : enum_value_(2)
{}
struct TestDefaultEnumValueDefaultTypeInternal {
  constexpr TestDefaultEnumValueDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TestDefaultEnumValueDefaultTypeInternal() {}
  union {
    TestDefaultEnumValue _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TestDefaultEnumValueDefaultTypeInternal _TestDefaultEnumValue_default_instance_;
}  // namespace protobuf_unittest
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_2eproto[19];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_google_2fprotobuf_2futil_2fjson_5fformat_2eproto[2];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_google_2fprotobuf_2futil_2fjson_5fformat_2eproto = nullptr;

const uint32_t TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestFlagsAndStrings_RepeatedGroup, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestFlagsAndStrings_RepeatedGroup, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestFlagsAndStrings_RepeatedGroup, f_),
  0,
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestFlagsAndStrings, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestFlagsAndStrings, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestFlagsAndStrings, a_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestFlagsAndStrings, repeatedgroup_),
  0,
  ~0u,
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestBase64ByteArrays, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestBase64ByteArrays, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestBase64ByteArrays, a_),
  0,
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestJavaScriptJSON, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestJavaScriptJSON, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestJavaScriptJSON, a_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestJavaScriptJSON, final_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestJavaScriptJSON, in_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestJavaScriptJSON, var_),
  2,
  3,
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestJavaScriptOrderJSON1, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestJavaScriptOrderJSON1, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestJavaScriptOrderJSON1, d_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestJavaScriptOrderJSON1, c_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestJavaScriptOrderJSON1, x_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestJavaScriptOrderJSON1, b_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestJavaScriptOrderJSON1, a_),
  0,
  1,
  2,
  3,
  4,
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestJavaScriptOrderJSON2, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestJavaScriptOrderJSON2, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestJavaScriptOrderJSON2, d_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestJavaScriptOrderJSON2, c_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestJavaScriptOrderJSON2, x_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestJavaScriptOrderJSON2, b_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestJavaScriptOrderJSON2, a_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestJavaScriptOrderJSON2, z_),
  0,
  1,
  2,
  3,
  4,
  ~0u,
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestLargeInt, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestLargeInt, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestLargeInt, a_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestLargeInt, b_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestNumbers, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestNumbers, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestNumbers, a_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestNumbers, b_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestNumbers, c_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestNumbers, d_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestNumbers, e_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestNumbers, f_),
  0,
  1,
  2,
  3,
  4,
  5,
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestCamelCase, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestCamelCase, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestCamelCase, normal_field_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestCamelCase, capital_field_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestCamelCase, camelcasefield_),
  0,
  1,
  2,
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestBoolMap_BoolMapEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestBoolMap_BoolMapEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestBoolMap_BoolMapEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestBoolMap_BoolMapEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestBoolMap, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestBoolMap, bool_map_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestRecursion, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestRecursion, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestRecursion, value_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestRecursion, child_),
  1,
  0,
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestStringMap_StringMapEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestStringMap_StringMapEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestStringMap_StringMapEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestStringMap_StringMapEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestStringMap, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestStringMap, string_map_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestStringSerializer_StringMapEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestStringSerializer_StringMapEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestStringSerializer_StringMapEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestStringSerializer_StringMapEntry_DoNotUse, value_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestStringSerializer, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestStringSerializer, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestStringSerializer, scalar_string_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestStringSerializer, repeated_string_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestStringSerializer, string_map_),
  0,
  ~0u,
  ~0u,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestMessageWithExtension, _internal_metadata_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestMessageWithExtension, _extensions_),
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestExtension, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestExtension, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestExtension, value_),
  0,
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestDefaultEnumValue, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestDefaultEnumValue, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::protobuf_unittest::TestDefaultEnumValue, enum_value_),
  0,
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, 7, -1, sizeof(::protobuf_unittest::TestFlagsAndStrings_RepeatedGroup)},
  { 8, 16, -1, sizeof(::protobuf_unittest::TestFlagsAndStrings)},
  { 18, 25, -1, sizeof(::protobuf_unittest::TestBase64ByteArrays)},
  { 26, 36, -1, sizeof(::protobuf_unittest::TestJavaScriptJSON)},
  { 40, 51, -1, sizeof(::protobuf_unittest::TestJavaScriptOrderJSON1)},
  { 56, 68, -1, sizeof(::protobuf_unittest::TestJavaScriptOrderJSON2)},
  { 74, 82, -1, sizeof(::protobuf_unittest::TestLargeInt)},
  { 84, 96, -1, sizeof(::protobuf_unittest::TestNumbers)},
  { 102, 111, -1, sizeof(::protobuf_unittest::TestCamelCase)},
  { 114, 122, -1, sizeof(::protobuf_unittest::TestBoolMap_BoolMapEntry_DoNotUse)},
  { 124, -1, -1, sizeof(::protobuf_unittest::TestBoolMap)},
  { 131, 139, -1, sizeof(::protobuf_unittest::TestRecursion)},
  { 141, 149, -1, sizeof(::protobuf_unittest::TestStringMap_StringMapEntry_DoNotUse)},
  { 151, -1, -1, sizeof(::protobuf_unittest::TestStringMap)},
  { 158, 166, -1, sizeof(::protobuf_unittest::TestStringSerializer_StringMapEntry_DoNotUse)},
  { 168, 177, -1, sizeof(::protobuf_unittest::TestStringSerializer)},
  { 180, -1, -1, sizeof(::protobuf_unittest::TestMessageWithExtension)},
  { 186, 193, -1, sizeof(::protobuf_unittest::TestExtension)},
  { 194, 201, -1, sizeof(::protobuf_unittest::TestDefaultEnumValue)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::protobuf_unittest::_TestFlagsAndStrings_RepeatedGroup_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::protobuf_unittest::_TestFlagsAndStrings_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::protobuf_unittest::_TestBase64ByteArrays_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::protobuf_unittest::_TestJavaScriptJSON_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::protobuf_unittest::_TestJavaScriptOrderJSON1_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::protobuf_unittest::_TestJavaScriptOrderJSON2_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::protobuf_unittest::_TestLargeInt_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::protobuf_unittest::_TestNumbers_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::protobuf_unittest::_TestCamelCase_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::protobuf_unittest::_TestBoolMap_BoolMapEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::protobuf_unittest::_TestBoolMap_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::protobuf_unittest::_TestRecursion_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::protobuf_unittest::_TestStringMap_StringMapEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::protobuf_unittest::_TestStringMap_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::protobuf_unittest::_TestStringSerializer_StringMapEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::protobuf_unittest::_TestStringSerializer_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::protobuf_unittest::_TestMessageWithExtension_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::protobuf_unittest::_TestExtension_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::protobuf_unittest::_TestDefaultEnumValue_default_instance_),
};

const char descriptor_table_protodef_google_2fprotobuf_2futil_2fjson_5fformat_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n&google/protobuf/util/json_format.proto"
  "\022\021protobuf_unittest\"\211\001\n\023TestFlagsAndStri"
  "ngs\022\t\n\001A\030\001 \002(\005\022K\n\rrepeatedgroup\030\002 \003(\n24."
  "protobuf_unittest.TestFlagsAndStrings.Re"
  "peatedGroup\032\032\n\rRepeatedGroup\022\t\n\001f\030\003 \002(\t\""
  "!\n\024TestBase64ByteArrays\022\t\n\001a\030\001 \002(\014\"G\n\022Te"
  "stJavaScriptJSON\022\t\n\001a\030\001 \001(\005\022\r\n\005final\030\002 \001"
  "(\002\022\n\n\002in\030\003 \001(\t\022\013\n\003Var\030\004 \001(\t\"Q\n\030TestJavaS"
  "criptOrderJSON1\022\t\n\001d\030\001 \001(\005\022\t\n\001c\030\002 \001(\005\022\t\n"
  "\001x\030\003 \001(\010\022\t\n\001b\030\004 \001(\005\022\t\n\001a\030\005 \001(\005\"\211\001\n\030TestJ"
  "avaScriptOrderJSON2\022\t\n\001d\030\001 \001(\005\022\t\n\001c\030\002 \001("
  "\005\022\t\n\001x\030\003 \001(\010\022\t\n\001b\030\004 \001(\005\022\t\n\001a\030\005 \001(\005\0226\n\001z\030"
  "\006 \003(\0132+.protobuf_unittest.TestJavaScript"
  "OrderJSON1\"$\n\014TestLargeInt\022\t\n\001a\030\001 \002(\003\022\t\n"
  "\001b\030\002 \002(\004\"\240\001\n\013TestNumbers\0220\n\001a\030\001 \001(\0162%.pr"
  "otobuf_unittest.TestNumbers.MyType\022\t\n\001b\030"
  "\002 \001(\005\022\t\n\001c\030\003 \001(\002\022\t\n\001d\030\004 \001(\010\022\t\n\001e\030\005 \001(\001\022\t"
  "\n\001f\030\006 \001(\r\"(\n\006MyType\022\006\n\002OK\020\000\022\013\n\007WARNING\020\001"
  "\022\t\n\005ERROR\020\002\"T\n\rTestCamelCase\022\024\n\014normal_f"
  "ield\030\001 \001(\t\022\025\n\rCAPITAL_FIELD\030\002 \001(\005\022\026\n\016Cam"
  "elCaseField\030\003 \001(\005\"|\n\013TestBoolMap\022=\n\010bool"
  "_map\030\001 \003(\0132+.protobuf_unittest.TestBoolM"
  "ap.BoolMapEntry\032.\n\014BoolMapEntry\022\013\n\003key\030\001"
  " \001(\010\022\r\n\005value\030\002 \001(\005:\0028\001\"O\n\rTestRecursion"
  "\022\r\n\005value\030\001 \001(\005\022/\n\005child\030\002 \001(\0132 .protobu"
  "f_unittest.TestRecursion\"\206\001\n\rTestStringM"
  "ap\022C\n\nstring_map\030\001 \003(\0132/.protobuf_unitte"
  "st.TestStringMap.StringMapEntry\0320\n\016Strin"
  "gMapEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028"
  "\001\"\304\001\n\024TestStringSerializer\022\025\n\rscalar_str"
  "ing\030\001 \001(\t\022\027\n\017repeated_string\030\002 \003(\t\022J\n\nst"
  "ring_map\030\003 \003(\01326.protobuf_unittest.TestS"
  "tringSerializer.StringMapEntry\0320\n\016String"
  "MapEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001"
  "\"$\n\030TestMessageWithExtension*\010\010d\020\200\200\200\200\002\"z"
  "\n\rTestExtension\022\r\n\005value\030\001 \001(\t2Z\n\003ext\022+."
  "protobuf_unittest.TestMessageWithExtensi"
  "on\030d \001(\0132 .protobuf_unittest.TestExtensi"
  "on\"Q\n\024TestDefaultEnumValue\0229\n\nenum_value"
  "\030\001 \001(\0162\034.protobuf_unittest.EnumValue:\007DE"
  "FAULT*2\n\tEnumValue\022\014\n\010PROTOCOL\020\000\022\n\n\006BUFF"
  "ER\020\001\022\013\n\007DEFAULT\020\002"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto = {
  false, false, 1657, descriptor_table_protodef_google_2fprotobuf_2futil_2fjson_5fformat_2eproto, "google/protobuf/util/json_format.proto", 
  &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_once, nullptr, 0, 19,
  schemas, file_default_instances, TableStruct_google_2fprotobuf_2futil_2fjson_5fformat_2eproto::offsets,
  file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_2eproto, file_level_enum_descriptors_google_2fprotobuf_2futil_2fjson_5fformat_2eproto, file_level_service_descriptors_google_2fprotobuf_2futil_2fjson_5fformat_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_getter() {
  return &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_google_2fprotobuf_2futil_2fjson_5fformat_2eproto(&descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto);
namespace protobuf_unittest {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TestNumbers_MyType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto);
  return file_level_enum_descriptors_google_2fprotobuf_2futil_2fjson_5fformat_2eproto[0];
}
bool TestNumbers_MyType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr TestNumbers_MyType TestNumbers::OK;
constexpr TestNumbers_MyType TestNumbers::WARNING;
constexpr TestNumbers_MyType TestNumbers::ERROR;
constexpr TestNumbers_MyType TestNumbers::MyType_MIN;
constexpr TestNumbers_MyType TestNumbers::MyType_MAX;
constexpr int TestNumbers::MyType_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* EnumValue_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto);
  return file_level_enum_descriptors_google_2fprotobuf_2futil_2fjson_5fformat_2eproto[1];
}
bool EnumValue_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class TestFlagsAndStrings_RepeatedGroup::_Internal {
 public:
  using HasBits = decltype(std::declval<TestFlagsAndStrings_RepeatedGroup>()._has_bits_);
  static void set_has_f(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000001) ^ 0x00000001) != 0;
  }
};

TestFlagsAndStrings_RepeatedGroup::TestFlagsAndStrings_RepeatedGroup(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestFlagsAndStrings.RepeatedGroup)
}
TestFlagsAndStrings_RepeatedGroup::TestFlagsAndStrings_RepeatedGroup(const TestFlagsAndStrings_RepeatedGroup& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  f_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    f_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_f()) {
    f_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_f(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestFlagsAndStrings.RepeatedGroup)
}

inline void TestFlagsAndStrings_RepeatedGroup::SharedCtor() {
f_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  f_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

TestFlagsAndStrings_RepeatedGroup::~TestFlagsAndStrings_RepeatedGroup() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestFlagsAndStrings.RepeatedGroup)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestFlagsAndStrings_RepeatedGroup::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  f_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void TestFlagsAndStrings_RepeatedGroup::ArenaDtor(void* object) {
  TestFlagsAndStrings_RepeatedGroup* _this = reinterpret_cast< TestFlagsAndStrings_RepeatedGroup* >(object);
  (void)_this;
}
void TestFlagsAndStrings_RepeatedGroup::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TestFlagsAndStrings_RepeatedGroup::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestFlagsAndStrings_RepeatedGroup::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestFlagsAndStrings.RepeatedGroup)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    f_.ClearNonDefaultToEmpty();
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestFlagsAndStrings_RepeatedGroup::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // required string f = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_f();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "protobuf_unittest.TestFlagsAndStrings.RepeatedGroup.f");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestFlagsAndStrings_RepeatedGroup::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestFlagsAndStrings.RepeatedGroup)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required string f = 3;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_f().data(), static_cast<int>(this->_internal_f().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestFlagsAndStrings.RepeatedGroup.f");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_f(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestFlagsAndStrings.RepeatedGroup)
  return target;
}

size_t TestFlagsAndStrings_RepeatedGroup::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestFlagsAndStrings.RepeatedGroup)
  size_t total_size = 0;

  // required string f = 3;
  if (_internal_has_f()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_f());
  }
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestFlagsAndStrings_RepeatedGroup::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestFlagsAndStrings_RepeatedGroup::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestFlagsAndStrings_RepeatedGroup::GetClassData() const { return &_class_data_; }

void TestFlagsAndStrings_RepeatedGroup::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestFlagsAndStrings_RepeatedGroup *>(to)->MergeFrom(
      static_cast<const TestFlagsAndStrings_RepeatedGroup &>(from));
}


void TestFlagsAndStrings_RepeatedGroup::MergeFrom(const TestFlagsAndStrings_RepeatedGroup& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestFlagsAndStrings.RepeatedGroup)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_f()) {
    _internal_set_f(from._internal_f());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestFlagsAndStrings_RepeatedGroup::CopyFrom(const TestFlagsAndStrings_RepeatedGroup& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestFlagsAndStrings.RepeatedGroup)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestFlagsAndStrings_RepeatedGroup::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  return true;
}

void TestFlagsAndStrings_RepeatedGroup::InternalSwap(TestFlagsAndStrings_RepeatedGroup* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &f_, lhs_arena,
      &other->f_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata TestFlagsAndStrings_RepeatedGroup::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_2eproto[0]);
}

// ===================================================================

class TestFlagsAndStrings::_Internal {
 public:
  using HasBits = decltype(std::declval<TestFlagsAndStrings>()._has_bits_);
  static void set_has_a(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000001) ^ 0x00000001) != 0;
  }
};

TestFlagsAndStrings::TestFlagsAndStrings(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  repeatedgroup_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestFlagsAndStrings)
}
TestFlagsAndStrings::TestFlagsAndStrings(const TestFlagsAndStrings& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_),
      repeatedgroup_(from.repeatedgroup_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  a_ = from.a_;
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestFlagsAndStrings)
}

inline void TestFlagsAndStrings::SharedCtor() {
a_ = 0;
}

TestFlagsAndStrings::~TestFlagsAndStrings() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestFlagsAndStrings)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestFlagsAndStrings::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void TestFlagsAndStrings::ArenaDtor(void* object) {
  TestFlagsAndStrings* _this = reinterpret_cast< TestFlagsAndStrings* >(object);
  (void)_this;
}
void TestFlagsAndStrings::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TestFlagsAndStrings::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestFlagsAndStrings::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestFlagsAndStrings)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  repeatedgroup_.Clear();
  a_ = 0;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestFlagsAndStrings::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // required int32 A = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _Internal::set_has_a(&has_bits);
          a_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated group RepeatedGroup = 2 { ... };
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 19)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseGroup(_internal_add_repeatedgroup(), ptr, 19);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<19>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestFlagsAndStrings::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestFlagsAndStrings)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required int32 A = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_a(), target);
  }

  // repeated group RepeatedGroup = 2 { ... };
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_repeatedgroup_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteGroup(2, this->_internal_repeatedgroup(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestFlagsAndStrings)
  return target;
}

size_t TestFlagsAndStrings::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestFlagsAndStrings)
  size_t total_size = 0;

  // required int32 A = 1;
  if (_internal_has_a()) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_a());
  }
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated group RepeatedGroup = 2 { ... };
  total_size += 2UL * this->_internal_repeatedgroup_size();
  for (const auto& msg : this->repeatedgroup_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GroupSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestFlagsAndStrings::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestFlagsAndStrings::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestFlagsAndStrings::GetClassData() const { return &_class_data_; }

void TestFlagsAndStrings::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestFlagsAndStrings *>(to)->MergeFrom(
      static_cast<const TestFlagsAndStrings &>(from));
}


void TestFlagsAndStrings::MergeFrom(const TestFlagsAndStrings& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestFlagsAndStrings)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  repeatedgroup_.MergeFrom(from.repeatedgroup_);
  if (from._internal_has_a()) {
    _internal_set_a(from._internal_a());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestFlagsAndStrings::CopyFrom(const TestFlagsAndStrings& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestFlagsAndStrings)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestFlagsAndStrings::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  if (!::PROTOBUF_NAMESPACE_ID::internal::AllAreInitialized(repeatedgroup_))
    return false;
  return true;
}

void TestFlagsAndStrings::InternalSwap(TestFlagsAndStrings* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  repeatedgroup_.InternalSwap(&other->repeatedgroup_);
  swap(a_, other->a_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TestFlagsAndStrings::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_2eproto[1]);
}

// ===================================================================

class TestBase64ByteArrays::_Internal {
 public:
  using HasBits = decltype(std::declval<TestBase64ByteArrays>()._has_bits_);
  static void set_has_a(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000001) ^ 0x00000001) != 0;
  }
};

TestBase64ByteArrays::TestBase64ByteArrays(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestBase64ByteArrays)
}
TestBase64ByteArrays::TestBase64ByteArrays(const TestBase64ByteArrays& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  a_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    a_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_a()) {
    a_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_a(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestBase64ByteArrays)
}

inline void TestBase64ByteArrays::SharedCtor() {
a_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  a_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

TestBase64ByteArrays::~TestBase64ByteArrays() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestBase64ByteArrays)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestBase64ByteArrays::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  a_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void TestBase64ByteArrays::ArenaDtor(void* object) {
  TestBase64ByteArrays* _this = reinterpret_cast< TestBase64ByteArrays* >(object);
  (void)_this;
}
void TestBase64ByteArrays::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TestBase64ByteArrays::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestBase64ByteArrays::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestBase64ByteArrays)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    a_.ClearNonDefaultToEmpty();
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestBase64ByteArrays::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // required bytes a = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_a();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestBase64ByteArrays::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestBase64ByteArrays)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required bytes a = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->WriteBytesMaybeAliased(
        1, this->_internal_a(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestBase64ByteArrays)
  return target;
}

size_t TestBase64ByteArrays::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestBase64ByteArrays)
  size_t total_size = 0;

  // required bytes a = 1;
  if (_internal_has_a()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_a());
  }
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestBase64ByteArrays::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestBase64ByteArrays::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestBase64ByteArrays::GetClassData() const { return &_class_data_; }

void TestBase64ByteArrays::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestBase64ByteArrays *>(to)->MergeFrom(
      static_cast<const TestBase64ByteArrays &>(from));
}


void TestBase64ByteArrays::MergeFrom(const TestBase64ByteArrays& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestBase64ByteArrays)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_a()) {
    _internal_set_a(from._internal_a());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestBase64ByteArrays::CopyFrom(const TestBase64ByteArrays& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestBase64ByteArrays)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestBase64ByteArrays::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  return true;
}

void TestBase64ByteArrays::InternalSwap(TestBase64ByteArrays* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &a_, lhs_arena,
      &other->a_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata TestBase64ByteArrays::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_2eproto[2]);
}

// ===================================================================

class TestJavaScriptJSON::_Internal {
 public:
  using HasBits = decltype(std::declval<TestJavaScriptJSON>()._has_bits_);
  static void set_has_a(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_final(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_in(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_var(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

TestJavaScriptJSON::TestJavaScriptJSON(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestJavaScriptJSON)
}
TestJavaScriptJSON::TestJavaScriptJSON(const TestJavaScriptJSON& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  in_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    in_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_in()) {
    in_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_in(), 
      GetArenaForAllocation());
  }
  var_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    var_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_var()) {
    var_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_var(), 
      GetArenaForAllocation());
  }
  ::memcpy(&a_, &from.a_,
    static_cast<size_t>(reinterpret_cast<char*>(&final_) -
    reinterpret_cast<char*>(&a_)) + sizeof(final_));
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestJavaScriptJSON)
}

inline void TestJavaScriptJSON::SharedCtor() {
in_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  in_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
var_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  var_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&a_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&final_) -
    reinterpret_cast<char*>(&a_)) + sizeof(final_));
}

TestJavaScriptJSON::~TestJavaScriptJSON() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestJavaScriptJSON)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestJavaScriptJSON::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  in_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  var_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void TestJavaScriptJSON::ArenaDtor(void* object) {
  TestJavaScriptJSON* _this = reinterpret_cast< TestJavaScriptJSON* >(object);
  (void)_this;
}
void TestJavaScriptJSON::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TestJavaScriptJSON::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestJavaScriptJSON::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestJavaScriptJSON)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      in_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      var_.ClearNonDefaultToEmpty();
    }
  }
  if (cached_has_bits & 0x0000000cu) {
    ::memset(&a_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&final_) -
        reinterpret_cast<char*>(&a_)) + sizeof(final_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestJavaScriptJSON::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional int32 a = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _Internal::set_has_a(&has_bits);
          a_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional float final = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          _Internal::set_has_final(&has_bits);
          final_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // optional string in = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_in();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "protobuf_unittest.TestJavaScriptJSON.in");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional string Var = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_var();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "protobuf_unittest.TestJavaScriptJSON.Var");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestJavaScriptJSON::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestJavaScriptJSON)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional int32 a = 1;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_a(), target);
  }

  // optional float final = 2;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_final(), target);
  }

  // optional string in = 3;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_in().data(), static_cast<int>(this->_internal_in().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestJavaScriptJSON.in");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_in(), target);
  }

  // optional string Var = 4;
  if (cached_has_bits & 0x00000002u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_var().data(), static_cast<int>(this->_internal_var().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestJavaScriptJSON.Var");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_var(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestJavaScriptJSON)
  return target;
}

size_t TestJavaScriptJSON::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestJavaScriptJSON)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    // optional string in = 3;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_in());
    }

    // optional string Var = 4;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_var());
    }

    // optional int32 a = 1;
    if (cached_has_bits & 0x00000004u) {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_a());
    }

    // optional float final = 2;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 + 4;
    }

  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestJavaScriptJSON::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestJavaScriptJSON::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestJavaScriptJSON::GetClassData() const { return &_class_data_; }

void TestJavaScriptJSON::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestJavaScriptJSON *>(to)->MergeFrom(
      static_cast<const TestJavaScriptJSON &>(from));
}


void TestJavaScriptJSON::MergeFrom(const TestJavaScriptJSON& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestJavaScriptJSON)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_in(from._internal_in());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_set_var(from._internal_var());
    }
    if (cached_has_bits & 0x00000004u) {
      a_ = from.a_;
    }
    if (cached_has_bits & 0x00000008u) {
      final_ = from.final_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestJavaScriptJSON::CopyFrom(const TestJavaScriptJSON& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestJavaScriptJSON)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestJavaScriptJSON::IsInitialized() const {
  return true;
}

void TestJavaScriptJSON::InternalSwap(TestJavaScriptJSON* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &in_, lhs_arena,
      &other->in_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &var_, lhs_arena,
      &other->var_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TestJavaScriptJSON, final_)
      + sizeof(TestJavaScriptJSON::final_)
      - PROTOBUF_FIELD_OFFSET(TestJavaScriptJSON, a_)>(
          reinterpret_cast<char*>(&a_),
          reinterpret_cast<char*>(&other->a_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TestJavaScriptJSON::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_2eproto[3]);
}

// ===================================================================

class TestJavaScriptOrderJSON1::_Internal {
 public:
  using HasBits = decltype(std::declval<TestJavaScriptOrderJSON1>()._has_bits_);
  static void set_has_d(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_c(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_x(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_b(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_a(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
};

TestJavaScriptOrderJSON1::TestJavaScriptOrderJSON1(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestJavaScriptOrderJSON1)
}
TestJavaScriptOrderJSON1::TestJavaScriptOrderJSON1(const TestJavaScriptOrderJSON1& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&d_, &from.d_,
    static_cast<size_t>(reinterpret_cast<char*>(&a_) -
    reinterpret_cast<char*>(&d_)) + sizeof(a_));
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestJavaScriptOrderJSON1)
}

inline void TestJavaScriptOrderJSON1::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&d_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&a_) -
    reinterpret_cast<char*>(&d_)) + sizeof(a_));
}

TestJavaScriptOrderJSON1::~TestJavaScriptOrderJSON1() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestJavaScriptOrderJSON1)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestJavaScriptOrderJSON1::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void TestJavaScriptOrderJSON1::ArenaDtor(void* object) {
  TestJavaScriptOrderJSON1* _this = reinterpret_cast< TestJavaScriptOrderJSON1* >(object);
  (void)_this;
}
void TestJavaScriptOrderJSON1::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TestJavaScriptOrderJSON1::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestJavaScriptOrderJSON1::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestJavaScriptOrderJSON1)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    ::memset(&d_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&a_) -
        reinterpret_cast<char*>(&d_)) + sizeof(a_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestJavaScriptOrderJSON1::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional int32 d = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _Internal::set_has_d(&has_bits);
          d_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 c = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _Internal::set_has_c(&has_bits);
          c_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional bool x = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _Internal::set_has_x(&has_bits);
          x_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 b = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _Internal::set_has_b(&has_bits);
          b_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 a = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          _Internal::set_has_a(&has_bits);
          a_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestJavaScriptOrderJSON1::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestJavaScriptOrderJSON1)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional int32 d = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_d(), target);
  }

  // optional int32 c = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_c(), target);
  }

  // optional bool x = 3;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_x(), target);
  }

  // optional int32 b = 4;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(4, this->_internal_b(), target);
  }

  // optional int32 a = 5;
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(5, this->_internal_a(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestJavaScriptOrderJSON1)
  return target;
}

size_t TestJavaScriptOrderJSON1::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestJavaScriptOrderJSON1)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    // optional int32 d = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_d());
    }

    // optional int32 c = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_c());
    }

    // optional bool x = 3;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 + 1;
    }

    // optional int32 b = 4;
    if (cached_has_bits & 0x00000008u) {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_b());
    }

    // optional int32 a = 5;
    if (cached_has_bits & 0x00000010u) {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_a());
    }

  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestJavaScriptOrderJSON1::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestJavaScriptOrderJSON1::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestJavaScriptOrderJSON1::GetClassData() const { return &_class_data_; }

void TestJavaScriptOrderJSON1::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestJavaScriptOrderJSON1 *>(to)->MergeFrom(
      static_cast<const TestJavaScriptOrderJSON1 &>(from));
}


void TestJavaScriptOrderJSON1::MergeFrom(const TestJavaScriptOrderJSON1& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestJavaScriptOrderJSON1)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    if (cached_has_bits & 0x00000001u) {
      d_ = from.d_;
    }
    if (cached_has_bits & 0x00000002u) {
      c_ = from.c_;
    }
    if (cached_has_bits & 0x00000004u) {
      x_ = from.x_;
    }
    if (cached_has_bits & 0x00000008u) {
      b_ = from.b_;
    }
    if (cached_has_bits & 0x00000010u) {
      a_ = from.a_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestJavaScriptOrderJSON1::CopyFrom(const TestJavaScriptOrderJSON1& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestJavaScriptOrderJSON1)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestJavaScriptOrderJSON1::IsInitialized() const {
  return true;
}

void TestJavaScriptOrderJSON1::InternalSwap(TestJavaScriptOrderJSON1* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TestJavaScriptOrderJSON1, a_)
      + sizeof(TestJavaScriptOrderJSON1::a_)
      - PROTOBUF_FIELD_OFFSET(TestJavaScriptOrderJSON1, d_)>(
          reinterpret_cast<char*>(&d_),
          reinterpret_cast<char*>(&other->d_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TestJavaScriptOrderJSON1::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_2eproto[4]);
}

// ===================================================================

class TestJavaScriptOrderJSON2::_Internal {
 public:
  using HasBits = decltype(std::declval<TestJavaScriptOrderJSON2>()._has_bits_);
  static void set_has_d(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_c(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_x(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_b(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_a(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
};

TestJavaScriptOrderJSON2::TestJavaScriptOrderJSON2(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  z_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestJavaScriptOrderJSON2)
}
TestJavaScriptOrderJSON2::TestJavaScriptOrderJSON2(const TestJavaScriptOrderJSON2& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_),
      z_(from.z_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&d_, &from.d_,
    static_cast<size_t>(reinterpret_cast<char*>(&a_) -
    reinterpret_cast<char*>(&d_)) + sizeof(a_));
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestJavaScriptOrderJSON2)
}

inline void TestJavaScriptOrderJSON2::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&d_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&a_) -
    reinterpret_cast<char*>(&d_)) + sizeof(a_));
}

TestJavaScriptOrderJSON2::~TestJavaScriptOrderJSON2() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestJavaScriptOrderJSON2)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestJavaScriptOrderJSON2::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void TestJavaScriptOrderJSON2::ArenaDtor(void* object) {
  TestJavaScriptOrderJSON2* _this = reinterpret_cast< TestJavaScriptOrderJSON2* >(object);
  (void)_this;
}
void TestJavaScriptOrderJSON2::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TestJavaScriptOrderJSON2::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestJavaScriptOrderJSON2::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestJavaScriptOrderJSON2)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  z_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    ::memset(&d_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&a_) -
        reinterpret_cast<char*>(&d_)) + sizeof(a_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestJavaScriptOrderJSON2::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional int32 d = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _Internal::set_has_d(&has_bits);
          d_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 c = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _Internal::set_has_c(&has_bits);
          c_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional bool x = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _Internal::set_has_x(&has_bits);
          x_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 b = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _Internal::set_has_b(&has_bits);
          b_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 a = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          _Internal::set_has_a(&has_bits);
          a_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .protobuf_unittest.TestJavaScriptOrderJSON1 z = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_z(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<50>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestJavaScriptOrderJSON2::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestJavaScriptOrderJSON2)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional int32 d = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_d(), target);
  }

  // optional int32 c = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_c(), target);
  }

  // optional bool x = 3;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_x(), target);
  }

  // optional int32 b = 4;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(4, this->_internal_b(), target);
  }

  // optional int32 a = 5;
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(5, this->_internal_a(), target);
  }

  // repeated .protobuf_unittest.TestJavaScriptOrderJSON1 z = 6;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_z_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(6, this->_internal_z(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestJavaScriptOrderJSON2)
  return target;
}

size_t TestJavaScriptOrderJSON2::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestJavaScriptOrderJSON2)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .protobuf_unittest.TestJavaScriptOrderJSON1 z = 6;
  total_size += 1UL * this->_internal_z_size();
  for (const auto& msg : this->z_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    // optional int32 d = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_d());
    }

    // optional int32 c = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_c());
    }

    // optional bool x = 3;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 + 1;
    }

    // optional int32 b = 4;
    if (cached_has_bits & 0x00000008u) {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_b());
    }

    // optional int32 a = 5;
    if (cached_has_bits & 0x00000010u) {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_a());
    }

  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestJavaScriptOrderJSON2::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestJavaScriptOrderJSON2::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestJavaScriptOrderJSON2::GetClassData() const { return &_class_data_; }

void TestJavaScriptOrderJSON2::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestJavaScriptOrderJSON2 *>(to)->MergeFrom(
      static_cast<const TestJavaScriptOrderJSON2 &>(from));
}


void TestJavaScriptOrderJSON2::MergeFrom(const TestJavaScriptOrderJSON2& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestJavaScriptOrderJSON2)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  z_.MergeFrom(from.z_);
  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    if (cached_has_bits & 0x00000001u) {
      d_ = from.d_;
    }
    if (cached_has_bits & 0x00000002u) {
      c_ = from.c_;
    }
    if (cached_has_bits & 0x00000004u) {
      x_ = from.x_;
    }
    if (cached_has_bits & 0x00000008u) {
      b_ = from.b_;
    }
    if (cached_has_bits & 0x00000010u) {
      a_ = from.a_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestJavaScriptOrderJSON2::CopyFrom(const TestJavaScriptOrderJSON2& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestJavaScriptOrderJSON2)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestJavaScriptOrderJSON2::IsInitialized() const {
  return true;
}

void TestJavaScriptOrderJSON2::InternalSwap(TestJavaScriptOrderJSON2* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  z_.InternalSwap(&other->z_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TestJavaScriptOrderJSON2, a_)
      + sizeof(TestJavaScriptOrderJSON2::a_)
      - PROTOBUF_FIELD_OFFSET(TestJavaScriptOrderJSON2, d_)>(
          reinterpret_cast<char*>(&d_),
          reinterpret_cast<char*>(&other->d_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TestJavaScriptOrderJSON2::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_2eproto[5]);
}

// ===================================================================

class TestLargeInt::_Internal {
 public:
  using HasBits = decltype(std::declval<TestLargeInt>()._has_bits_);
  static void set_has_a(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_b(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000003) ^ 0x00000003) != 0;
  }
};

TestLargeInt::TestLargeInt(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestLargeInt)
}
TestLargeInt::TestLargeInt(const TestLargeInt& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&a_, &from.a_,
    static_cast<size_t>(reinterpret_cast<char*>(&b_) -
    reinterpret_cast<char*>(&a_)) + sizeof(b_));
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestLargeInt)
}

inline void TestLargeInt::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&a_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&b_) -
    reinterpret_cast<char*>(&a_)) + sizeof(b_));
}

TestLargeInt::~TestLargeInt() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestLargeInt)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestLargeInt::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void TestLargeInt::ArenaDtor(void* object) {
  TestLargeInt* _this = reinterpret_cast< TestLargeInt* >(object);
  (void)_this;
}
void TestLargeInt::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TestLargeInt::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestLargeInt::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestLargeInt)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    ::memset(&a_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&b_) -
        reinterpret_cast<char*>(&a_)) + sizeof(b_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestLargeInt::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // required int64 a = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _Internal::set_has_a(&has_bits);
          a_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // required uint64 b = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _Internal::set_has_b(&has_bits);
          b_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestLargeInt::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestLargeInt)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required int64 a = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_a(), target);
  }

  // required uint64 b = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(2, this->_internal_b(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestLargeInt)
  return target;
}

size_t TestLargeInt::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:protobuf_unittest.TestLargeInt)
  size_t total_size = 0;

  if (_internal_has_a()) {
    // required int64 a = 1;
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_a());
  }

  if (_internal_has_b()) {
    // required uint64 b = 2;
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_b());
  }

  return total_size;
}
size_t TestLargeInt::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestLargeInt)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000003) ^ 0x00000003) == 0) {  // All required fields are present.
    // required int64 a = 1;
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_a());

    // required uint64 b = 2;
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_b());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestLargeInt::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestLargeInt::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestLargeInt::GetClassData() const { return &_class_data_; }

void TestLargeInt::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestLargeInt *>(to)->MergeFrom(
      static_cast<const TestLargeInt &>(from));
}


void TestLargeInt::MergeFrom(const TestLargeInt& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestLargeInt)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      a_ = from.a_;
    }
    if (cached_has_bits & 0x00000002u) {
      b_ = from.b_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestLargeInt::CopyFrom(const TestLargeInt& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestLargeInt)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestLargeInt::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  return true;
}

void TestLargeInt::InternalSwap(TestLargeInt* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TestLargeInt, b_)
      + sizeof(TestLargeInt::b_)
      - PROTOBUF_FIELD_OFFSET(TestLargeInt, a_)>(
          reinterpret_cast<char*>(&a_),
          reinterpret_cast<char*>(&other->a_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TestLargeInt::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_2eproto[6]);
}

// ===================================================================

class TestNumbers::_Internal {
 public:
  using HasBits = decltype(std::declval<TestNumbers>()._has_bits_);
  static void set_has_a(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_b(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_c(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_d(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_e(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static void set_has_f(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
};

TestNumbers::TestNumbers(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestNumbers)
}
TestNumbers::TestNumbers(const TestNumbers& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&a_, &from.a_,
    static_cast<size_t>(reinterpret_cast<char*>(&f_) -
    reinterpret_cast<char*>(&a_)) + sizeof(f_));
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestNumbers)
}

inline void TestNumbers::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&a_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&f_) -
    reinterpret_cast<char*>(&a_)) + sizeof(f_));
}

TestNumbers::~TestNumbers() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestNumbers)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestNumbers::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void TestNumbers::ArenaDtor(void* object) {
  TestNumbers* _this = reinterpret_cast< TestNumbers* >(object);
  (void)_this;
}
void TestNumbers::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TestNumbers::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestNumbers::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestNumbers)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000003fu) {
    ::memset(&a_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&f_) -
        reinterpret_cast<char*>(&a_)) + sizeof(f_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestNumbers::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional .protobuf_unittest.TestNumbers.MyType a = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::protobuf_unittest::TestNumbers_MyType_IsValid(val))) {
            _internal_set_a(static_cast<::protobuf_unittest::TestNumbers_MyType>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(1, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      // optional int32 b = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _Internal::set_has_b(&has_bits);
          b_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional float c = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          _Internal::set_has_c(&has_bits);
          c_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // optional bool d = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _Internal::set_has_d(&has_bits);
          d_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional double e = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 41)) {
          _Internal::set_has_e(&has_bits);
          e_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // optional uint32 f = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          _Internal::set_has_f(&has_bits);
          f_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestNumbers::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestNumbers)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional .protobuf_unittest.TestNumbers.MyType a = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_a(), target);
  }

  // optional int32 b = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_b(), target);
  }

  // optional float c = 3;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_c(), target);
  }

  // optional bool d = 4;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(4, this->_internal_d(), target);
  }

  // optional double e = 5;
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(5, this->_internal_e(), target);
  }

  // optional uint32 f = 6;
  if (cached_has_bits & 0x00000020u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_f(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestNumbers)
  return target;
}

size_t TestNumbers::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestNumbers)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000003fu) {
    // optional .protobuf_unittest.TestNumbers.MyType a = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_a());
    }

    // optional int32 b = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_b());
    }

    // optional float c = 3;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 + 4;
    }

    // optional bool d = 4;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 + 1;
    }

    // optional double e = 5;
    if (cached_has_bits & 0x00000010u) {
      total_size += 1 + 8;
    }

    // optional uint32 f = 6;
    if (cached_has_bits & 0x00000020u) {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_f());
    }

  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestNumbers::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestNumbers::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestNumbers::GetClassData() const { return &_class_data_; }

void TestNumbers::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestNumbers *>(to)->MergeFrom(
      static_cast<const TestNumbers &>(from));
}


void TestNumbers::MergeFrom(const TestNumbers& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestNumbers)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000003fu) {
    if (cached_has_bits & 0x00000001u) {
      a_ = from.a_;
    }
    if (cached_has_bits & 0x00000002u) {
      b_ = from.b_;
    }
    if (cached_has_bits & 0x00000004u) {
      c_ = from.c_;
    }
    if (cached_has_bits & 0x00000008u) {
      d_ = from.d_;
    }
    if (cached_has_bits & 0x00000010u) {
      e_ = from.e_;
    }
    if (cached_has_bits & 0x00000020u) {
      f_ = from.f_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestNumbers::CopyFrom(const TestNumbers& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestNumbers)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestNumbers::IsInitialized() const {
  return true;
}

void TestNumbers::InternalSwap(TestNumbers* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TestNumbers, f_)
      + sizeof(TestNumbers::f_)
      - PROTOBUF_FIELD_OFFSET(TestNumbers, a_)>(
          reinterpret_cast<char*>(&a_),
          reinterpret_cast<char*>(&other->a_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TestNumbers::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_2eproto[7]);
}

// ===================================================================

class TestCamelCase::_Internal {
 public:
  using HasBits = decltype(std::declval<TestCamelCase>()._has_bits_);
  static void set_has_normal_field(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_capital_field(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_camelcasefield(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
};

TestCamelCase::TestCamelCase(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestCamelCase)
}
TestCamelCase::TestCamelCase(const TestCamelCase& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  normal_field_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    normal_field_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_normal_field()) {
    normal_field_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_normal_field(), 
      GetArenaForAllocation());
  }
  ::memcpy(&capital_field_, &from.capital_field_,
    static_cast<size_t>(reinterpret_cast<char*>(&camelcasefield_) -
    reinterpret_cast<char*>(&capital_field_)) + sizeof(camelcasefield_));
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestCamelCase)
}

inline void TestCamelCase::SharedCtor() {
normal_field_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  normal_field_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&capital_field_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&camelcasefield_) -
    reinterpret_cast<char*>(&capital_field_)) + sizeof(camelcasefield_));
}

TestCamelCase::~TestCamelCase() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestCamelCase)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestCamelCase::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  normal_field_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void TestCamelCase::ArenaDtor(void* object) {
  TestCamelCase* _this = reinterpret_cast< TestCamelCase* >(object);
  (void)_this;
}
void TestCamelCase::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TestCamelCase::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestCamelCase::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestCamelCase)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    normal_field_.ClearNonDefaultToEmpty();
  }
  if (cached_has_bits & 0x00000006u) {
    ::memset(&capital_field_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&camelcasefield_) -
        reinterpret_cast<char*>(&capital_field_)) + sizeof(camelcasefield_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestCamelCase::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional string normal_field = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_normal_field();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "protobuf_unittest.TestCamelCase.normal_field");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 CAPITAL_FIELD = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _Internal::set_has_capital_field(&has_bits);
          capital_field_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 CamelCaseField = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _Internal::set_has_camelcasefield(&has_bits);
          camelcasefield_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestCamelCase::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestCamelCase)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string normal_field = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_normal_field().data(), static_cast<int>(this->_internal_normal_field().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestCamelCase.normal_field");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_normal_field(), target);
  }

  // optional int32 CAPITAL_FIELD = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_capital_field(), target);
  }

  // optional int32 CamelCaseField = 3;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_camelcasefield(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestCamelCase)
  return target;
}

size_t TestCamelCase::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestCamelCase)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    // optional string normal_field = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_normal_field());
    }

    // optional int32 CAPITAL_FIELD = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_capital_field());
    }

    // optional int32 CamelCaseField = 3;
    if (cached_has_bits & 0x00000004u) {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_camelcasefield());
    }

  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestCamelCase::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestCamelCase::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestCamelCase::GetClassData() const { return &_class_data_; }

void TestCamelCase::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestCamelCase *>(to)->MergeFrom(
      static_cast<const TestCamelCase &>(from));
}


void TestCamelCase::MergeFrom(const TestCamelCase& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestCamelCase)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_normal_field(from._internal_normal_field());
    }
    if (cached_has_bits & 0x00000002u) {
      capital_field_ = from.capital_field_;
    }
    if (cached_has_bits & 0x00000004u) {
      camelcasefield_ = from.camelcasefield_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestCamelCase::CopyFrom(const TestCamelCase& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestCamelCase)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestCamelCase::IsInitialized() const {
  return true;
}

void TestCamelCase::InternalSwap(TestCamelCase* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &normal_field_, lhs_arena,
      &other->normal_field_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TestCamelCase, camelcasefield_)
      + sizeof(TestCamelCase::camelcasefield_)
      - PROTOBUF_FIELD_OFFSET(TestCamelCase, capital_field_)>(
          reinterpret_cast<char*>(&capital_field_),
          reinterpret_cast<char*>(&other->capital_field_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TestCamelCase::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_2eproto[8]);
}

// ===================================================================

TestBoolMap_BoolMapEntry_DoNotUse::TestBoolMap_BoolMapEntry_DoNotUse() {}
TestBoolMap_BoolMapEntry_DoNotUse::TestBoolMap_BoolMapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void TestBoolMap_BoolMapEntry_DoNotUse::MergeFrom(const TestBoolMap_BoolMapEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata TestBoolMap_BoolMapEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_2eproto[9]);
}

// ===================================================================

class TestBoolMap::_Internal {
 public:
};

TestBoolMap::TestBoolMap(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  bool_map_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestBoolMap)
}
TestBoolMap::TestBoolMap(const TestBoolMap& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  bool_map_.MergeFrom(from.bool_map_);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestBoolMap)
}

inline void TestBoolMap::SharedCtor() {
}

TestBoolMap::~TestBoolMap() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestBoolMap)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestBoolMap::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void TestBoolMap::ArenaDtor(void* object) {
  TestBoolMap* _this = reinterpret_cast< TestBoolMap* >(object);
  (void)_this;
  _this->bool_map_. ~MapField();
}
inline void TestBoolMap::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &TestBoolMap::ArenaDtor);
  }
}
void TestBoolMap::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestBoolMap::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestBoolMap)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  bool_map_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestBoolMap::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // map<bool, int32> bool_map = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&bool_map_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestBoolMap::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestBoolMap)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // map<bool, int32> bool_map = 1;
  if (!this->_internal_bool_map().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >::const_pointer
        ConstPtr;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::SortItem< bool, ConstPtr > SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByFirstField<SortItem> Less;

    if (stream->IsSerializationDeterministic() &&
        this->_internal_bool_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_bool_map().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >::const_iterator
          it = this->_internal_bool_map().begin();
          it != this->_internal_bool_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = TestBoolMap_BoolMapEntry_DoNotUse::Funcs::InternalSerialize(1, items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second, target, stream);
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >::const_iterator
          it = this->_internal_bool_map().begin();
          it != this->_internal_bool_map().end(); ++it) {
        target = TestBoolMap_BoolMapEntry_DoNotUse::Funcs::InternalSerialize(1, it->first, it->second, target, stream);
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestBoolMap)
  return target;
}

size_t TestBoolMap::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestBoolMap)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<bool, int32> bool_map = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_bool_map_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< bool, int32_t >::const_iterator
      it = this->_internal_bool_map().begin();
      it != this->_internal_bool_map().end(); ++it) {
    total_size += TestBoolMap_BoolMapEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestBoolMap::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestBoolMap::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestBoolMap::GetClassData() const { return &_class_data_; }

void TestBoolMap::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestBoolMap *>(to)->MergeFrom(
      static_cast<const TestBoolMap &>(from));
}


void TestBoolMap::MergeFrom(const TestBoolMap& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestBoolMap)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  bool_map_.MergeFrom(from.bool_map_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestBoolMap::CopyFrom(const TestBoolMap& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestBoolMap)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestBoolMap::IsInitialized() const {
  return true;
}

void TestBoolMap::InternalSwap(TestBoolMap* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  bool_map_.InternalSwap(&other->bool_map_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TestBoolMap::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_2eproto[10]);
}

// ===================================================================

class TestRecursion::_Internal {
 public:
  using HasBits = decltype(std::declval<TestRecursion>()._has_bits_);
  static void set_has_value(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static const ::protobuf_unittest::TestRecursion& child(const TestRecursion* msg);
  static void set_has_child(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

const ::protobuf_unittest::TestRecursion&
TestRecursion::_Internal::child(const TestRecursion* msg) {
  return *msg->child_;
}
TestRecursion::TestRecursion(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestRecursion)
}
TestRecursion::TestRecursion(const TestRecursion& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_child()) {
    child_ = new ::protobuf_unittest::TestRecursion(*from.child_);
  } else {
    child_ = nullptr;
  }
  value_ = from.value_;
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestRecursion)
}

inline void TestRecursion::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&child_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&value_) -
    reinterpret_cast<char*>(&child_)) + sizeof(value_));
}

TestRecursion::~TestRecursion() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestRecursion)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestRecursion::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete child_;
}

void TestRecursion::ArenaDtor(void* object) {
  TestRecursion* _this = reinterpret_cast< TestRecursion* >(object);
  (void)_this;
}
void TestRecursion::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TestRecursion::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestRecursion::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestRecursion)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    GOOGLE_DCHECK(child_ != nullptr);
    child_->Clear();
  }
  value_ = 0;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestRecursion::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional int32 value = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _Internal::set_has_value(&has_bits);
          value_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional .protobuf_unittest.TestRecursion child = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_child(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestRecursion::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestRecursion)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional int32 value = 1;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_value(), target);
  }

  // optional .protobuf_unittest.TestRecursion child = 2;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::child(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestRecursion)
  return target;
}

size_t TestRecursion::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestRecursion)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional .protobuf_unittest.TestRecursion child = 2;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *child_);
    }

    // optional int32 value = 1;
    if (cached_has_bits & 0x00000002u) {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_value());
    }

  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestRecursion::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestRecursion::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestRecursion::GetClassData() const { return &_class_data_; }

void TestRecursion::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestRecursion *>(to)->MergeFrom(
      static_cast<const TestRecursion &>(from));
}


void TestRecursion::MergeFrom(const TestRecursion& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestRecursion)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_mutable_child()->::protobuf_unittest::TestRecursion::MergeFrom(from._internal_child());
    }
    if (cached_has_bits & 0x00000002u) {
      value_ = from.value_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestRecursion::CopyFrom(const TestRecursion& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestRecursion)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestRecursion::IsInitialized() const {
  return true;
}

void TestRecursion::InternalSwap(TestRecursion* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TestRecursion, value_)
      + sizeof(TestRecursion::value_)
      - PROTOBUF_FIELD_OFFSET(TestRecursion, child_)>(
          reinterpret_cast<char*>(&child_),
          reinterpret_cast<char*>(&other->child_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TestRecursion::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_2eproto[11]);
}

// ===================================================================

TestStringMap_StringMapEntry_DoNotUse::TestStringMap_StringMapEntry_DoNotUse() {}
TestStringMap_StringMapEntry_DoNotUse::TestStringMap_StringMapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void TestStringMap_StringMapEntry_DoNotUse::MergeFrom(const TestStringMap_StringMapEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata TestStringMap_StringMapEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_2eproto[12]);
}

// ===================================================================

class TestStringMap::_Internal {
 public:
};

TestStringMap::TestStringMap(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  string_map_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestStringMap)
}
TestStringMap::TestStringMap(const TestStringMap& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  string_map_.MergeFrom(from.string_map_);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestStringMap)
}

inline void TestStringMap::SharedCtor() {
}

TestStringMap::~TestStringMap() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestStringMap)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestStringMap::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void TestStringMap::ArenaDtor(void* object) {
  TestStringMap* _this = reinterpret_cast< TestStringMap* >(object);
  (void)_this;
  _this->string_map_. ~MapField();
}
inline void TestStringMap::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &TestStringMap::ArenaDtor);
  }
}
void TestStringMap::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestStringMap::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestStringMap)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  string_map_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestStringMap::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // map<string, string> string_map = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&string_map_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestStringMap::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestStringMap)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, string> string_map = 1;
  if (!this->_internal_string_map().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
          "protobuf_unittest.TestStringMap.StringMapEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
          "protobuf_unittest.TestStringMap.StringMapEntry.value");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_string_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_string_map().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_string_map().begin();
          it != this->_internal_string_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = TestStringMap_StringMapEntry_DoNotUse::Funcs::InternalSerialize(1, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_string_map().begin();
          it != this->_internal_string_map().end(); ++it) {
        target = TestStringMap_StringMapEntry_DoNotUse::Funcs::InternalSerialize(1, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestStringMap)
  return target;
}

size_t TestStringMap::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestStringMap)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, string> string_map = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_string_map_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->_internal_string_map().begin();
      it != this->_internal_string_map().end(); ++it) {
    total_size += TestStringMap_StringMapEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestStringMap::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestStringMap::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestStringMap::GetClassData() const { return &_class_data_; }

void TestStringMap::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestStringMap *>(to)->MergeFrom(
      static_cast<const TestStringMap &>(from));
}


void TestStringMap::MergeFrom(const TestStringMap& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestStringMap)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  string_map_.MergeFrom(from.string_map_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestStringMap::CopyFrom(const TestStringMap& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestStringMap)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestStringMap::IsInitialized() const {
  return true;
}

void TestStringMap::InternalSwap(TestStringMap* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  string_map_.InternalSwap(&other->string_map_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TestStringMap::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_2eproto[13]);
}

// ===================================================================

TestStringSerializer_StringMapEntry_DoNotUse::TestStringSerializer_StringMapEntry_DoNotUse() {}
TestStringSerializer_StringMapEntry_DoNotUse::TestStringSerializer_StringMapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void TestStringSerializer_StringMapEntry_DoNotUse::MergeFrom(const TestStringSerializer_StringMapEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata TestStringSerializer_StringMapEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_2eproto[14]);
}

// ===================================================================

class TestStringSerializer::_Internal {
 public:
  using HasBits = decltype(std::declval<TestStringSerializer>()._has_bits_);
  static void set_has_scalar_string(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

TestStringSerializer::TestStringSerializer(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  repeated_string_(arena),
  string_map_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestStringSerializer)
}
TestStringSerializer::TestStringSerializer(const TestStringSerializer& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_),
      repeated_string_(from.repeated_string_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  string_map_.MergeFrom(from.string_map_);
  scalar_string_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    scalar_string_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_scalar_string()) {
    scalar_string_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_scalar_string(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestStringSerializer)
}

inline void TestStringSerializer::SharedCtor() {
scalar_string_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  scalar_string_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

TestStringSerializer::~TestStringSerializer() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestStringSerializer)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestStringSerializer::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  scalar_string_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void TestStringSerializer::ArenaDtor(void* object) {
  TestStringSerializer* _this = reinterpret_cast< TestStringSerializer* >(object);
  (void)_this;
  _this->string_map_. ~MapField();
}
inline void TestStringSerializer::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &TestStringSerializer::ArenaDtor);
  }
}
void TestStringSerializer::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestStringSerializer::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestStringSerializer)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  repeated_string_.Clear();
  string_map_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    scalar_string_.ClearNonDefaultToEmpty();
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestStringSerializer::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional string scalar_string = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_scalar_string();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "protobuf_unittest.TestStringSerializer.scalar_string");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated string repeated_string = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_repeated_string();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            #ifndef NDEBUG
            ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "protobuf_unittest.TestStringSerializer.repeated_string");
            #endif  // !NDEBUG
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      // map<string, string> string_map = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&string_map_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestStringSerializer::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestStringSerializer)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string scalar_string = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_scalar_string().data(), static_cast<int>(this->_internal_scalar_string().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestStringSerializer.scalar_string");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_scalar_string(), target);
  }

  // repeated string repeated_string = 2;
  for (int i = 0, n = this->_internal_repeated_string_size(); i < n; i++) {
    const auto& s = this->_internal_repeated_string(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestStringSerializer.repeated_string");
    target = stream->WriteString(2, s, target);
  }

  // map<string, string> string_map = 3;
  if (!this->_internal_string_map().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
          "protobuf_unittest.TestStringSerializer.StringMapEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
          "protobuf_unittest.TestStringSerializer.StringMapEntry.value");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_string_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_string_map().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_string_map().begin();
          it != this->_internal_string_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = TestStringSerializer_StringMapEntry_DoNotUse::Funcs::InternalSerialize(3, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_string_map().begin();
          it != this->_internal_string_map().end(); ++it) {
        target = TestStringSerializer_StringMapEntry_DoNotUse::Funcs::InternalSerialize(3, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestStringSerializer)
  return target;
}

size_t TestStringSerializer::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestStringSerializer)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string repeated_string = 2;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(repeated_string_.size());
  for (int i = 0, n = repeated_string_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      repeated_string_.Get(i));
  }

  // map<string, string> string_map = 3;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_string_map_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->_internal_string_map().begin();
      it != this->_internal_string_map().end(); ++it) {
    total_size += TestStringSerializer_StringMapEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // optional string scalar_string = 1;
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_scalar_string());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestStringSerializer::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestStringSerializer::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestStringSerializer::GetClassData() const { return &_class_data_; }

void TestStringSerializer::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestStringSerializer *>(to)->MergeFrom(
      static_cast<const TestStringSerializer &>(from));
}


void TestStringSerializer::MergeFrom(const TestStringSerializer& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestStringSerializer)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  repeated_string_.MergeFrom(from.repeated_string_);
  string_map_.MergeFrom(from.string_map_);
  if (from._internal_has_scalar_string()) {
    _internal_set_scalar_string(from._internal_scalar_string());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestStringSerializer::CopyFrom(const TestStringSerializer& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestStringSerializer)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestStringSerializer::IsInitialized() const {
  return true;
}

void TestStringSerializer::InternalSwap(TestStringSerializer* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  repeated_string_.InternalSwap(&other->repeated_string_);
  string_map_.InternalSwap(&other->string_map_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &scalar_string_, lhs_arena,
      &other->scalar_string_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata TestStringSerializer::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_2eproto[15]);
}

// ===================================================================

class TestMessageWithExtension::_Internal {
 public:
};

TestMessageWithExtension::TestMessageWithExtension(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  _extensions_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestMessageWithExtension)
}
TestMessageWithExtension::TestMessageWithExtension(const TestMessageWithExtension& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _extensions_.MergeFrom(internal_default_instance(), from._extensions_);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestMessageWithExtension)
}

inline void TestMessageWithExtension::SharedCtor() {
}

TestMessageWithExtension::~TestMessageWithExtension() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestMessageWithExtension)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestMessageWithExtension::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void TestMessageWithExtension::ArenaDtor(void* object) {
  TestMessageWithExtension* _this = reinterpret_cast< TestMessageWithExtension* >(object);
  (void)_this;
}
void TestMessageWithExtension::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TestMessageWithExtension::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestMessageWithExtension::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestMessageWithExtension)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _extensions_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestMessageWithExtension::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    if ((800u <= tag)) {
      ptr = _extensions_.ParseField(tag, ptr, internal_default_instance(), &_internal_metadata_, ctx);
      CHK_(ptr != nullptr);
      continue;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestMessageWithExtension::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestMessageWithExtension)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // Extension range [100, 536870912)
  target = _extensions_._InternalSerialize(
  internal_default_instance(), 100, 536870912, target, stream);

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestMessageWithExtension)
  return target;
}

size_t TestMessageWithExtension::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestMessageWithExtension)
  size_t total_size = 0;

  total_size += _extensions_.ByteSize();

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestMessageWithExtension::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestMessageWithExtension::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestMessageWithExtension::GetClassData() const { return &_class_data_; }

void TestMessageWithExtension::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestMessageWithExtension *>(to)->MergeFrom(
      static_cast<const TestMessageWithExtension &>(from));
}


void TestMessageWithExtension::MergeFrom(const TestMessageWithExtension& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestMessageWithExtension)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _extensions_.MergeFrom(internal_default_instance(), from._extensions_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestMessageWithExtension::CopyFrom(const TestMessageWithExtension& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestMessageWithExtension)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestMessageWithExtension::IsInitialized() const {
  if (!_extensions_.IsInitialized()) {
    return false;
  }

  return true;
}

void TestMessageWithExtension::InternalSwap(TestMessageWithExtension* other) {
  using std::swap;
  _extensions_.InternalSwap(&other->_extensions_);
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TestMessageWithExtension::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_2eproto[16]);
}

// ===================================================================

class TestExtension::_Internal {
 public:
  using HasBits = decltype(std::declval<TestExtension>()._has_bits_);
  static void set_has_value(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

TestExtension::TestExtension(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestExtension)
}
TestExtension::TestExtension(const TestExtension& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_value()) {
    value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_value(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestExtension)
}

inline void TestExtension::SharedCtor() {
value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

TestExtension::~TestExtension() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestExtension)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestExtension::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  value_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void TestExtension::ArenaDtor(void* object) {
  TestExtension* _this = reinterpret_cast< TestExtension* >(object);
  (void)_this;
}
void TestExtension::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TestExtension::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestExtension::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestExtension)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    value_.ClearNonDefaultToEmpty();
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestExtension::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional string value = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_value();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "protobuf_unittest.TestExtension.value");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestExtension::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestExtension)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string value = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_value().data(), static_cast<int>(this->_internal_value().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestExtension.value");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_value(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestExtension)
  return target;
}

size_t TestExtension::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestExtension)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // optional string value = 1;
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_value());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestExtension::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestExtension::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestExtension::GetClassData() const { return &_class_data_; }

void TestExtension::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestExtension *>(to)->MergeFrom(
      static_cast<const TestExtension &>(from));
}


void TestExtension::MergeFrom(const TestExtension& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestExtension)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_value()) {
    _internal_set_value(from._internal_value());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestExtension::CopyFrom(const TestExtension& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestExtension)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestExtension::IsInitialized() const {
  return true;
}

void TestExtension::InternalSwap(TestExtension* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &value_, lhs_arena,
      &other->value_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata TestExtension::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_2eproto[17]);
}

// ===================================================================

class TestDefaultEnumValue::_Internal {
 public:
  using HasBits = decltype(std::declval<TestDefaultEnumValue>()._has_bits_);
  static void set_has_enum_value(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

TestDefaultEnumValue::TestDefaultEnumValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestDefaultEnumValue)
}
TestDefaultEnumValue::TestDefaultEnumValue(const TestDefaultEnumValue& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  enum_value_ = from.enum_value_;
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestDefaultEnumValue)
}

inline void TestDefaultEnumValue::SharedCtor() {
enum_value_ = 2;
}

TestDefaultEnumValue::~TestDefaultEnumValue() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestDefaultEnumValue)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TestDefaultEnumValue::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void TestDefaultEnumValue::ArenaDtor(void* object) {
  TestDefaultEnumValue* _this = reinterpret_cast< TestDefaultEnumValue* >(object);
  (void)_this;
}
void TestDefaultEnumValue::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TestDefaultEnumValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TestDefaultEnumValue::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestDefaultEnumValue)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  enum_value_ = 2;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TestDefaultEnumValue::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional .protobuf_unittest.EnumValue enum_value = 1 [default = DEFAULT];
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::protobuf_unittest::EnumValue_IsValid(val))) {
            _internal_set_enum_value(static_cast<::protobuf_unittest::EnumValue>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(1, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TestDefaultEnumValue::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestDefaultEnumValue)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional .protobuf_unittest.EnumValue enum_value = 1 [default = DEFAULT];
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_enum_value(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestDefaultEnumValue)
  return target;
}

size_t TestDefaultEnumValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestDefaultEnumValue)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // optional .protobuf_unittest.EnumValue enum_value = 1 [default = DEFAULT];
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_enum_value());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TestDefaultEnumValue::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TestDefaultEnumValue::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TestDefaultEnumValue::GetClassData() const { return &_class_data_; }

void TestDefaultEnumValue::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TestDefaultEnumValue *>(to)->MergeFrom(
      static_cast<const TestDefaultEnumValue &>(from));
}


void TestDefaultEnumValue::MergeFrom(const TestDefaultEnumValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestDefaultEnumValue)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_enum_value()) {
    _internal_set_enum_value(from._internal_enum_value());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TestDefaultEnumValue::CopyFrom(const TestDefaultEnumValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestDefaultEnumValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TestDefaultEnumValue::IsInitialized() const {
  return true;
}

void TestDefaultEnumValue::InternalSwap(TestDefaultEnumValue* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  swap(enum_value_, other->enum_value_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TestDefaultEnumValue::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_getter, &descriptor_table_google_2fprotobuf_2futil_2fjson_5fformat_2eproto_once,
      file_level_metadata_google_2fprotobuf_2futil_2fjson_5fformat_2eproto[18]);
}
#if !defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912)
const int TestExtension::kExtFieldNumber;
#endif
PROTOBUF_ATTRIBUTE_INIT_PRIORITY ::PROTOBUF_NAMESPACE_ID::internal::ExtensionIdentifier< ::protobuf_unittest::TestMessageWithExtension,
    ::PROTOBUF_NAMESPACE_ID::internal::MessageTypeTraits< ::protobuf_unittest::TestExtension >, 11, false >
  TestExtension::ext(kExtFieldNumber, ::protobuf_unittest::TestExtension::default_instance());

// @@protoc_insertion_point(namespace_scope)
}  // namespace protobuf_unittest
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::protobuf_unittest::TestFlagsAndStrings_RepeatedGroup* Arena::CreateMaybeMessage< ::protobuf_unittest::TestFlagsAndStrings_RepeatedGroup >(Arena* arena) {
  return Arena::CreateMessageInternal< ::protobuf_unittest::TestFlagsAndStrings_RepeatedGroup >(arena);
}
template<> PROTOBUF_NOINLINE ::protobuf_unittest::TestFlagsAndStrings* Arena::CreateMaybeMessage< ::protobuf_unittest::TestFlagsAndStrings >(Arena* arena) {
  return Arena::CreateMessageInternal< ::protobuf_unittest::TestFlagsAndStrings >(arena);
}
template<> PROTOBUF_NOINLINE ::protobuf_unittest::TestBase64ByteArrays* Arena::CreateMaybeMessage< ::protobuf_unittest::TestBase64ByteArrays >(Arena* arena) {
  return Arena::CreateMessageInternal< ::protobuf_unittest::TestBase64ByteArrays >(arena);
}
template<> PROTOBUF_NOINLINE ::protobuf_unittest::TestJavaScriptJSON* Arena::CreateMaybeMessage< ::protobuf_unittest::TestJavaScriptJSON >(Arena* arena) {
  return Arena::CreateMessageInternal< ::protobuf_unittest::TestJavaScriptJSON >(arena);
}
template<> PROTOBUF_NOINLINE ::protobuf_unittest::TestJavaScriptOrderJSON1* Arena::CreateMaybeMessage< ::protobuf_unittest::TestJavaScriptOrderJSON1 >(Arena* arena) {
  return Arena::CreateMessageInternal< ::protobuf_unittest::TestJavaScriptOrderJSON1 >(arena);
}
template<> PROTOBUF_NOINLINE ::protobuf_unittest::TestJavaScriptOrderJSON2* Arena::CreateMaybeMessage< ::protobuf_unittest::TestJavaScriptOrderJSON2 >(Arena* arena) {
  return Arena::CreateMessageInternal< ::protobuf_unittest::TestJavaScriptOrderJSON2 >(arena);
}
template<> PROTOBUF_NOINLINE ::protobuf_unittest::TestLargeInt* Arena::CreateMaybeMessage< ::protobuf_unittest::TestLargeInt >(Arena* arena) {
  return Arena::CreateMessageInternal< ::protobuf_unittest::TestLargeInt >(arena);
}
template<> PROTOBUF_NOINLINE ::protobuf_unittest::TestNumbers* Arena::CreateMaybeMessage< ::protobuf_unittest::TestNumbers >(Arena* arena) {
  return Arena::CreateMessageInternal< ::protobuf_unittest::TestNumbers >(arena);
}
template<> PROTOBUF_NOINLINE ::protobuf_unittest::TestCamelCase* Arena::CreateMaybeMessage< ::protobuf_unittest::TestCamelCase >(Arena* arena) {
  return Arena::CreateMessageInternal< ::protobuf_unittest::TestCamelCase >(arena);
}
template<> PROTOBUF_NOINLINE ::protobuf_unittest::TestBoolMap_BoolMapEntry_DoNotUse* Arena::CreateMaybeMessage< ::protobuf_unittest::TestBoolMap_BoolMapEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::protobuf_unittest::TestBoolMap_BoolMapEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::protobuf_unittest::TestBoolMap* Arena::CreateMaybeMessage< ::protobuf_unittest::TestBoolMap >(Arena* arena) {
  return Arena::CreateMessageInternal< ::protobuf_unittest::TestBoolMap >(arena);
}
template<> PROTOBUF_NOINLINE ::protobuf_unittest::TestRecursion* Arena::CreateMaybeMessage< ::protobuf_unittest::TestRecursion >(Arena* arena) {
  return Arena::CreateMessageInternal< ::protobuf_unittest::TestRecursion >(arena);
}
template<> PROTOBUF_NOINLINE ::protobuf_unittest::TestStringMap_StringMapEntry_DoNotUse* Arena::CreateMaybeMessage< ::protobuf_unittest::TestStringMap_StringMapEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::protobuf_unittest::TestStringMap_StringMapEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::protobuf_unittest::TestStringMap* Arena::CreateMaybeMessage< ::protobuf_unittest::TestStringMap >(Arena* arena) {
  return Arena::CreateMessageInternal< ::protobuf_unittest::TestStringMap >(arena);
}
template<> PROTOBUF_NOINLINE ::protobuf_unittest::TestStringSerializer_StringMapEntry_DoNotUse* Arena::CreateMaybeMessage< ::protobuf_unittest::TestStringSerializer_StringMapEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::protobuf_unittest::TestStringSerializer_StringMapEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::protobuf_unittest::TestStringSerializer* Arena::CreateMaybeMessage< ::protobuf_unittest::TestStringSerializer >(Arena* arena) {
  return Arena::CreateMessageInternal< ::protobuf_unittest::TestStringSerializer >(arena);
}
template<> PROTOBUF_NOINLINE ::protobuf_unittest::TestMessageWithExtension* Arena::CreateMaybeMessage< ::protobuf_unittest::TestMessageWithExtension >(Arena* arena) {
  return Arena::CreateMessageInternal< ::protobuf_unittest::TestMessageWithExtension >(arena);
}
template<> PROTOBUF_NOINLINE ::protobuf_unittest::TestExtension* Arena::CreateMaybeMessage< ::protobuf_unittest::TestExtension >(Arena* arena) {
  return Arena::CreateMessageInternal< ::protobuf_unittest::TestExtension >(arena);
}
template<> PROTOBUF_NOINLINE ::protobuf_unittest::TestDefaultEnumValue* Arena::CreateMaybeMessage< ::protobuf_unittest::TestDefaultEnumValue >(Arena* arena) {
  return Arena::CreateMessageInternal< ::protobuf_unittest::TestDefaultEnumValue >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
