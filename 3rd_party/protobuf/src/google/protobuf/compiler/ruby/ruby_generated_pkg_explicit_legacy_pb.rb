# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: ruby_generated_pkg_explicit_legacy.proto

require 'google/protobuf'

Google::Protobuf::DescriptorPool.generated_pool.build do
  add_file("ruby_generated_pkg_explicit_legacy.proto", :syntax => :proto3) do
    add_message "one.two.a_three.and.Four" do
      optional :another_string, :string, 1
    end
  end
end

module AA
  module BB
    module CC
      Four = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("one.two.a_three.and.Four").msgclass
    end
  end
end
