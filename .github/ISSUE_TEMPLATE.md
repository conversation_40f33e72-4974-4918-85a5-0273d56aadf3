# 平台(如果交叉编译请再附上交叉编译目标平台):  
# Platform(Include target platform as well if cross-compiling):  


# Github版本:
# Github Version:

直接下载ZIP包请提供下载日期以及压缩包注释里的git版本(可通过``7z l zip包路径``命令并在输出信息中搜索``Comment`` 获得，形如``Comment = bc80b11110cd440aacdabbf59658d630527a7f2b``)。 git clone请提供 ``git commit`` 第一行的commit id

Provide date (or better yet, git revision from the comment section of the zip. Obtainable using ``7z l PATH/TO/ZIP`` and search for ``Comment`` in the output) if downloading source as zip,otherwise provide the first commit id from the output of ``git commit``

# 编译方式:
# Compiling Method

```
请在这里粘贴cmake参数或使用的cmake脚本路径以及完整输出
Paste cmake arguments or path of the build script used here as well as the full log of the cmake proess here or pastebin
```

# 编译日志:
# Build Log:

```
粘贴在这里
Paste log here or pastebin
```
