name: ios
on:
  push:
    branches:
     - master
     - 'feature/**'
    paths:
      - 'source/**'
      - 'express/**'
      - 'test/**'
      - 'project/ios/**'
      - '.github/workflows/ios.yml'
  pull_request:
    branches: [master]
    paths:
      - 'source/**'
      - 'express/**'
      - 'test/**'
      - 'project/ios/**'
      - '.github/workflows/ios.yml'

concurrency:
  group: ios-${{ github.ref }}
  cancel-in-progress: true
permissions:
  contents: read

jobs:
  ios_build:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: true

      - name: build-framework
        run: |
          brew install coreutils
          ./package_scripts/ios/xcodebuildiOS.sh -o ios_build
        shell: bash

      - name: show-framework
        run: |
            ls -lh ios_build
