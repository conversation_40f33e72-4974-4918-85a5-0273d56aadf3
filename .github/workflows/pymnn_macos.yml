name: pymnn-macos
on:
  push:
    branches:
     - master
     - 'feature/**'
    paths:
      - 'pymnn/**'
      - '.github/workflows/pymnn_macos.yml'
  pull_request:
    branches: [master]
    paths:
      - 'pymnn/**'
      - '.github/workflows/pymnn_macos.yml'

concurrency:
  group: pymnn-macos-${{ github.ref }}
  cancel-in-progress: true
permissions:
  contents: read

jobs:
  pymnn_macos_buil_test:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
            python-version: '3.8.10'
      - name: prepare
        run: |
            pip3 install numpy opencv-python torch
      - name: build
        run: |
            cd pymnn/pip_package
            python3 build_deps.py llm
            python3 setup.py install --version 1.0
      - name: test
        run: |
            cd pymnn/test
            python3 unit_test.py
