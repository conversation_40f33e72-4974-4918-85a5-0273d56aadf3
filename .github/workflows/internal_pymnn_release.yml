name: internal_pymnn_release
on:
  workflow_dispatch:

jobs:
  build_wheels:
    name: ${{ matrix.arch }} ${{ matrix.build }} on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        include:
          - { os: ubuntu-latest,    arch: x86_64,     build: 'cp*-manylinux*' }
          - { os: ubuntu-24.04-arm, arch: aarch64,    build: 'cp*-manylinux*' }
          - { os: windows-latest,   arch: AMD64,      build: 'cp*'          }
          - { os: macos-13,         arch: x86_64,     build: 'cp*'          }
          - { os: macos-14,         arch: arm64,      build: 'cp*'          }

    steps:
    - uses: actions/checkout@v3
      with:
        submodules: true

    - name: using msvc
      if: matrix.os == 'windows-latest'
      uses: ilammy/msvc-dev-cmd@v1

    - name: using private schema patch
      if: matrix.os != 'windows-latest'
      env:
        PATCH_CONTENT: ${{ secrets.PRIVATE_SCHEMA_PATCH }}
      run: |
        patch_file=$(mktemp schema_patch.XXXXXX.diff)
        echo "$PATCH_CONTENT" > $patch_file
        git apply $patch_file
        rm $patch_file
        git status
        cd schema
        ls -lh
        ./generate.sh
        cd ..
        git status
    - name: using private schema patch
      if: matrix.os == 'windows-latest'
      env:
        PATCH_CONTENT: ${{ secrets.PRIVATE_SCHEMA_PATCH }}
      run: |
        $patchFile = [System.IO.Path]::GetTempFileName() + ".diff"
        [System.IO.File]::WriteAllText($patchFile, $env:PATCH_CONTENT)
        git apply $patchFile
        Remove-Item $patchFile
        git status
        Push-Location schema
        Get-ChildItem -Name
        ./generate.ps1
        Set-Location ..
        git status
    - uses: actions/setup-python@v4
      with:
        python-version: '3.12'

    - name: install pipx
      if: matrix.os == 'macos-14'
      run: python -m pip install pipx

    - name: Build wheels
      uses: pypa/cibuildwheel@v2.16.5
      env:
        CIBW_ARCHS_MACOS: ${{ matrix.arch }}
        CIBW_ARCHS_LINUX: ${{ matrix.arch }}
        CIBW_ARCHS_WINDOWS: ${{ matrix.arch }}
        CIBW_BUILD: ${{ matrix.build }}
        CIBW_BUILD_VERBOSITY: 1
        CIBW_ENVIRONMENT: CMAKE_BUILD_PARALLEL_LEVEL=2
      with:
        package-dir: pymnn/pip_package
        output-dir: wheelhouse
        config-file: "{package}/pyproject.toml"

    - name: Show files
      run: ls -lh wheelhouse
      shell: bash

    - name: Upload wheels
      uses: actions/upload-artifact@v4
      with:
        name: artifact-${{ matrix.os }}-${{ matrix.arch }}
        path: wheelhouse/*.whl
