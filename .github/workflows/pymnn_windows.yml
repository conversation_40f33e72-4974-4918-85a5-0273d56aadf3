name: pymnn-windows
on:
  push:
    branches:
     - master
     - 'feature/**'
    paths:
      - 'pymnn/**'
      - '.github/workflows/pymnn_windows.yml'
  pull_request:
    branches: [master]
    paths:
      - 'pymnn/**'
      - '.github/workflows/pymnn_windows.yml'

concurrency:
  group: pymnn-windows-${{ github.ref }}
  cancel-in-progress: true
permissions:
  contents: read

jobs:
  pymnn_windows_buil_test:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v3
      - uses: ilammy/msvc-dev-cmd@v1
      - uses: actions/setup-python@v4
        with:
            python-version: '3.9'
      - name: prepare
        run: |
            pip3 install numpy==1.25 opencv-python torch
      - name: build
        run: |
            cd pymnn/pip_package
            python3 build_deps.py llm
            python3 setup.py install --version 1.0
      - name: test
        run: |
            cd pymnn/test
            python3 unit_test.py
